"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8703],{38703:function(ct,ve,m){m.d(ve,{Z:function(){return it}});var s=m(67294),he=m(15063),ye=m(19735),Ce=m(35918),Se=m(17012),$e=m(62208),be=m(93967),A=m.n(be),ke=m(98423),xe=m(53124),ne=m(87462),U=m(1413),oe=m(91),se={percent:0,prefixCls:"rc-progress",strokeColor:"#2db7f5",strokeLinecap:"round",strokeWidth:1,trailColor:"#D9D9D9",trailWidth:1,gapPosition:"bottom"},ie=function(){var t=(0,s.useRef)([]),r=(0,s.useRef)(null);return(0,s.useEffect)(function(){var n=Date.now(),o=!1;t.current.forEach(function(l){if(l){o=!0;var i=l.style;i.transitionDuration=".3s, .3s, .3s, .06s",r.current&&n-r.current<100&&(i.transitionDuration="0s, 0s")}}),o&&(r.current=Date.now())}),t.current},Pe=["className","percent","prefixCls","strokeColor","strokeLinecap","strokeWidth","style","trailColor","trailWidth","transition"],Ee=function(t){var r=(0,U.Z)((0,U.Z)({},se),t),n=r.className,o=r.percent,l=r.prefixCls,i=r.strokeColor,c=r.strokeLinecap,a=r.strokeWidth,f=r.style,d=r.trailColor,p=r.trailWidth,v=r.transition,S=(0,oe.Z)(r,Pe);delete S.gapPosition;var y=Array.isArray(o)?o:[o],C=Array.isArray(i)?i:[i],x=ie(),g=a/2,h=100-a/2,O="M ".concat(c==="round"?g:0,",").concat(g,`
         L `).concat(c==="round"?h:100,",").concat(g),E="0 0 100 ".concat(a),$=0;return s.createElement("svg",(0,ne.Z)({className:A()("".concat(l,"-line"),n),viewBox:E,preserveAspectRatio:"none",style:f},S),s.createElement("path",{className:"".concat(l,"-line-trail"),d:O,strokeLinecap:c,stroke:d,strokeWidth:p||a,fillOpacity:"0"}),y.map(function(b,P){var k=1;switch(c){case"round":k=1-a/100;break;case"square":k=1-a/2/100;break;default:k=1;break}var I={strokeDasharray:"".concat(b*k,"px, 100px"),strokeDashoffset:"-".concat($,"px"),transition:v||"stroke-dashoffset 0.3s ease 0s, stroke-dasharray .3s ease 0s, stroke 0.3s linear"},u=C[P]||C[C.length-1];return $+=b,s.createElement("path",{key:P,className:"".concat(l,"-line-path"),d:O,strokeLinecap:c,stroke:u,strokeWidth:a,fillOpacity:"0",ref:function(M){x[P]=M},style:I})}))},Ie=Ee,X=m(71002),Oe=m(97685),Le=m(98924),ae=0,je=(0,Le.Z)();function We(){var e;return je?(e=ae,ae+=1):e="TEST_OR_SSR",e}var we=function(e){var t=s.useState(),r=(0,Oe.Z)(t,2),n=r[0],o=r[1];return s.useEffect(function(){o("rc_progress_".concat(We()))},[]),e||n},ce=function(t){var r=t.bg,n=t.children;return s.createElement("div",{style:{width:"100%",height:"100%",background:r}},n)};function le(e,t){return Object.keys(e).map(function(r){var n=parseFloat(r),o="".concat(Math.floor(n*t),"%");return"".concat(e[r]," ").concat(o)})}var Ne=s.forwardRef(function(e,t){var r=e.prefixCls,n=e.color,o=e.gradientId,l=e.radius,i=e.style,c=e.ptg,a=e.strokeLinecap,f=e.strokeWidth,d=e.size,p=e.gapDegree,v=n&&(0,X.Z)(n)==="object",S=v?"#FFF":void 0,y=d/2,C=s.createElement("circle",{className:"".concat(r,"-circle-path"),r:l,cx:y,cy:y,stroke:S,strokeLinecap:a,strokeWidth:f,opacity:c===0?0:1,style:i,ref:t});if(!v)return C;var x="".concat(o,"-conic"),g=p?"".concat(180+p/2,"deg"):"0deg",h=le(n,(360-p)/360),O=le(n,1),E="conic-gradient(from ".concat(g,", ").concat(h.join(", "),")"),$="linear-gradient(to ".concat(p?"bottom":"top",", ").concat(O.join(", "),")");return s.createElement(s.Fragment,null,s.createElement("mask",{id:x},C),s.createElement("foreignObject",{x:0,y:0,width:d,height:d,mask:"url(#".concat(x,")")},s.createElement(ce,{bg:$},s.createElement(ce,{bg:E}))))}),Ae=Ne,G=100,_=function(t,r,n,o,l,i,c,a,f,d){var p=arguments.length>10&&arguments[10]!==void 0?arguments[10]:0,v=n/100*360*((360-i)/360),S=i===0?0:{bottom:0,top:180,left:90,right:-90}[c],y=(100-o)/100*r;f==="round"&&o!==100&&(y+=d/2,y>=r&&(y=r-.01));var C=G/2;return{stroke:typeof a=="string"?a:void 0,strokeDasharray:"".concat(r,"px ").concat(t),strokeDashoffset:y+p,transform:"rotate(".concat(l+v+S,"deg)"),transformOrigin:"".concat(C,"px ").concat(C,"px"),transition:"stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s",fillOpacity:0}},De=["id","prefixCls","steps","strokeWidth","trailWidth","gapDegree","gapPosition","trailColor","strokeLinecap","style","className","strokeColor","percent"];function de(e){var t=e!=null?e:[];return Array.isArray(t)?t:[t]}var Te=function(t){var r=(0,U.Z)((0,U.Z)({},se),t),n=r.id,o=r.prefixCls,l=r.steps,i=r.strokeWidth,c=r.trailWidth,a=r.gapDegree,f=a===void 0?0:a,d=r.gapPosition,p=r.trailColor,v=r.strokeLinecap,S=r.style,y=r.className,C=r.strokeColor,x=r.percent,g=(0,oe.Z)(r,De),h=G/2,O=we(n),E="".concat(O,"-gradient"),$=h-i/2,b=Math.PI*2*$,P=f>0?90+f/2:-90,k=b*((360-f)/360),I=(0,X.Z)(l)==="object"?l:{count:l,gap:2},u=I.count,H=I.gap,M=de(x),D=de(C),T=D.find(function(j){return j&&(0,X.Z)(j)==="object"}),Z=T&&(0,X.Z)(T)==="object",N=Z?"butt":v,V=_(b,k,0,100,P,f,d,p,N,i),J=ie(),W=function(){var B=0;return M.map(function(R,F){var te=D[F]||D[D.length-1],K=_(b,k,B,R,P,f,d,te,N,i);return B+=R,s.createElement(Ae,{key:F,color:te,ptg:R,radius:$,prefixCls:o,gradientId:E,style:K,strokeLinecap:N,strokeWidth:i,gapDegree:f,ref:function(re){J[F]=re},size:G})}).reverse()},L=function(){var B=Math.round(u*(M[0]/100)),R=100/u,F=0;return new Array(u).fill(null).map(function(te,K){var q=K<=B-1?D[0]:p,re=q&&(0,X.Z)(q)==="object"?"url(#".concat(E,")"):void 0,me=_(b,k,F,R,P,f,d,q,"butt",i,H);return F+=(k-me.strokeDashoffset+H)*100/k,s.createElement("circle",{key:K,className:"".concat(o,"-circle-path"),r:$,cx:h,cy:h,stroke:re,strokeWidth:i,opacity:1,style:me,ref:function(at){J[K]=at}})})};return s.createElement("svg",(0,ne.Z)({className:A()("".concat(o,"-circle"),y),viewBox:"0 0 ".concat(G," ").concat(G),style:S,id:n,role:"presentation"},g),!u&&s.createElement("circle",{className:"".concat(o,"-circle-trail"),r:$,cx:h,cy:h,stroke:p,strokeLinecap:N,strokeWidth:c||i,style:V}),u?L():W())},ue=Te,lt={Line:Ie,Circle:ue},Ze=m(83062),ee=m(65409);function w(e){return!e||e<0?0:e>100?100:e}function z({success:e,successPercent:t}){let r=t;return e&&"progress"in e&&(r=e.progress),e&&"percent"in e&&(r=e.percent),r}const Re=({percent:e,success:t,successPercent:r})=>{const n=w(z({success:t,successPercent:r}));return[n,w(w(e)-n)]},Me=({success:e={},strokeColor:t})=>{const{strokeColor:r}=e;return[r||ee.ez.green,t||null]},Q=(e,t,r)=>{var n,o,l,i;let c=-1,a=-1;if(t==="step"){const f=r.steps,d=r.strokeWidth;typeof e=="string"||typeof e=="undefined"?(c=e==="small"?2:14,a=d!=null?d:8):typeof e=="number"?[c,a]=[e,e]:[c=14,a=8]=Array.isArray(e)?e:[e.width,e.height],c*=f}else if(t==="line"){const f=r==null?void 0:r.strokeWidth;typeof e=="string"||typeof e=="undefined"?a=f||(e==="small"?6:8):typeof e=="number"?[c,a]=[e,e]:[c=-1,a=8]=Array.isArray(e)?e:[e.width,e.height]}else(t==="circle"||t==="dashboard")&&(typeof e=="string"||typeof e=="undefined"?[c,a]=e==="small"?[60,60]:[120,120]:typeof e=="number"?[c,a]=[e,e]:Array.isArray(e)&&(c=(o=(n=e[0])!==null&&n!==void 0?n:e[1])!==null&&o!==void 0?o:120,a=(i=(l=e[0])!==null&&l!==void 0?l:e[1])!==null&&i!==void 0?i:120));return[c,a]},Be=3,Fe=e=>Be/e*100;var Xe=e=>{const{prefixCls:t,trailColor:r=null,strokeLinecap:n="round",gapPosition:o,gapDegree:l,width:i=120,type:c,children:a,success:f,size:d=i,steps:p}=e,[v,S]=Q(d,"circle");let{strokeWidth:y}=e;y===void 0&&(y=Math.max(Fe(v),6));const C={width:v,height:S,fontSize:v*.15+6},x=s.useMemo(()=>{if(l||l===0)return l;if(c==="dashboard")return 75},[l,c]),g=Re(e),h=o||c==="dashboard"&&"bottom"||void 0,O=Object.prototype.toString.call(e.strokeColor)==="[object Object]",E=Me({success:f,strokeColor:e.strokeColor}),$=A()(`${t}-inner`,{[`${t}-circle-gradient`]:O}),b=s.createElement(ue,{steps:p,percent:p?g[1]:g,strokeWidth:y,trailWidth:y,strokeColor:p?E[1]:E,strokeLinecap:n,trailColor:r,prefixCls:t,gapDegree:x,gapPosition:h}),P=v<=20,k=s.createElement("div",{className:$,style:C},b,!P&&a);return P?s.createElement(Ze.Z,{title:a},k):k},fe=m(11568),Ge=m(14747),He=m(83559),Ve=m(83262);const Y="--progress-line-stroke-color",ge="--progress-percent",pe=e=>{const t=e?"100%":"-100%";return new fe.E4(`antProgress${e?"RTL":"LTR"}Active`,{"0%":{transform:`translateX(${t}) scaleX(0)`,opacity:.1},"20%":{transform:`translateX(${t}) scaleX(0)`,opacity:.5},to:{transform:"translateX(0) scaleX(1)",opacity:0}})},Ke=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:Object.assign(Object.assign({},(0,Ge.Wf)(e)),{display:"inline-block","&-rtl":{direction:"rtl"},"&-line":{position:"relative",width:"100%",fontSize:e.fontSize},[`${t}-outer`]:{display:"inline-flex",alignItems:"center",width:"100%"},[`${t}-inner`]:{position:"relative",display:"inline-block",width:"100%",flex:1,overflow:"hidden",verticalAlign:"middle",backgroundColor:e.remainingColor,borderRadius:e.lineBorderRadius},[`${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.defaultColor}},[`${t}-success-bg, ${t}-bg`]:{position:"relative",background:e.defaultColor,borderRadius:e.lineBorderRadius,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`},[`${t}-layout-bottom`]:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",[`${t}-text`]:{width:"max-content",marginInlineStart:0,marginTop:e.marginXXS}},[`${t}-bg`]:{overflow:"hidden","&::after":{content:'""',background:{_multi_value_:!0,value:["inherit",`var(${Y})`]},height:"100%",width:`calc(1 / var(${ge}) * 100%)`,display:"block"},[`&${t}-bg-inner`]:{minWidth:"max-content","&::after":{content:"none"},[`${t}-text-inner`]:{color:e.colorWhite,[`&${t}-text-bright`]:{color:"rgba(0, 0, 0, 0.45)"}}}},[`${t}-success-bg`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,backgroundColor:e.colorSuccess},[`${t}-text`]:{display:"inline-block",marginInlineStart:e.marginXS,color:e.colorText,lineHeight:1,width:"2em",whiteSpace:"nowrap",textAlign:"start",verticalAlign:"middle",wordBreak:"normal",[r]:{fontSize:e.fontSize},[`&${t}-text-outer`]:{width:"max-content"},[`&${t}-text-outer${t}-text-start`]:{width:"max-content",marginInlineStart:0,marginInlineEnd:e.marginXS}},[`${t}-text-inner`]:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"100%",marginInlineStart:0,padding:`0 ${(0,fe.bf)(e.paddingXXS)}`,[`&${t}-text-start`]:{justifyContent:"start"},[`&${t}-text-end`]:{justifyContent:"end"}},[`&${t}-status-active`]:{[`${t}-bg::before`]:{position:"absolute",inset:0,backgroundColor:e.colorBgContainer,borderRadius:e.lineBorderRadius,opacity:0,animationName:pe(),animationDuration:e.progressActiveMotionDuration,animationTimingFunction:e.motionEaseOutQuint,animationIterationCount:"infinite",content:'""'}},[`&${t}-rtl${t}-status-active`]:{[`${t}-bg::before`]:{animationName:pe(!0)}},[`&${t}-status-exception`]:{[`${t}-bg`]:{backgroundColor:e.colorError},[`${t}-text`]:{color:e.colorError}},[`&${t}-status-exception ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorError}},[`&${t}-status-success`]:{[`${t}-bg`]:{backgroundColor:e.colorSuccess},[`${t}-text`]:{color:e.colorSuccess}},[`&${t}-status-success ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorSuccess}}})}},Ue=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-circle-trail`]:{stroke:e.remainingColor},[`&${t}-circle ${t}-inner`]:{position:"relative",lineHeight:1,backgroundColor:"transparent"},[`&${t}-circle ${t}-text`]:{position:"absolute",insetBlockStart:"50%",insetInlineStart:0,width:"100%",margin:0,padding:0,color:e.circleTextColor,fontSize:e.circleTextFontSize,lineHeight:1,whiteSpace:"normal",textAlign:"center",transform:"translateY(-50%)",[r]:{fontSize:e.circleIconFontSize}},[`${t}-circle&-status-exception`]:{[`${t}-text`]:{color:e.colorError}},[`${t}-circle&-status-success`]:{[`${t}-text`]:{color:e.colorSuccess}}},[`${t}-inline-circle`]:{lineHeight:1,[`${t}-inner`]:{verticalAlign:"bottom"}}}},ze=e=>{const{componentCls:t}=e;return{[t]:{[`${t}-steps`]:{display:"inline-block","&-outer":{display:"flex",flexDirection:"row",alignItems:"center"},"&-item":{flexShrink:0,minWidth:e.progressStepMinWidth,marginInlineEnd:e.progressStepMarginInlineEnd,backgroundColor:e.remainingColor,transition:`all ${e.motionDurationSlow}`,"&-active":{backgroundColor:e.defaultColor}}}}}},Qe=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-small&-line, ${t}-small&-line ${t}-text ${r}`]:{fontSize:e.fontSizeSM}}}},Ye=e=>({circleTextColor:e.colorText,defaultColor:e.colorInfo,remainingColor:e.colorFillSecondary,lineBorderRadius:100,circleTextFontSize:"1em",circleIconFontSize:`${e.fontSize/e.fontSizeSM}em`});var Je=(0,He.I$)("Progress",e=>{const t=e.calc(e.marginXXS).div(2).equal(),r=(0,Ve.IX)(e,{progressStepMarginInlineEnd:t,progressStepMinWidth:t,progressActiveMotionDuration:"2.4s"});return[Ke(r),Ue(r),ze(r),Qe(r)]},Ye),qe=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const _e=e=>{let t=[];return Object.keys(e).forEach(r=>{const n=parseFloat(r.replace(/%/g,""));Number.isNaN(n)||t.push({key:n,value:e[r]})}),t=t.sort((r,n)=>r.key-n.key),t.map(({key:r,value:n})=>`${n} ${r}%`).join(", ")},et=(e,t)=>{const{from:r=ee.ez.blue,to:n=ee.ez.blue,direction:o=t==="rtl"?"to left":"to right"}=e,l=qe(e,["from","to","direction"]);if(Object.keys(l).length!==0){const c=_e(l),a=`linear-gradient(${o}, ${c})`;return{background:a,[Y]:a}}const i=`linear-gradient(${o}, ${r}, ${n})`;return{background:i,[Y]:i}};var tt=e=>{const{prefixCls:t,direction:r,percent:n,size:o,strokeWidth:l,strokeColor:i,strokeLinecap:c="round",children:a,trailColor:f=null,percentPosition:d,success:p}=e,{align:v,type:S}=d,y=i&&typeof i!="string"?et(i,r):{[Y]:i,background:i},C=c==="square"||c==="butt"?0:void 0,x=o!=null?o:[-1,l||(o==="small"?6:8)],[g,h]=Q(x,"line",{strokeWidth:l}),O={backgroundColor:f||void 0,borderRadius:C},E=Object.assign(Object.assign({width:`${w(n)}%`,height:h,borderRadius:C},y),{[ge]:w(n)/100}),$=z(e),b={width:`${w($)}%`,height:h,borderRadius:C,backgroundColor:p==null?void 0:p.strokeColor},P={width:g<0?"100%":g},k=s.createElement("div",{className:`${t}-inner`,style:O},s.createElement("div",{className:A()(`${t}-bg`,`${t}-bg-${S}`),style:E},S==="inner"&&a),$!==void 0&&s.createElement("div",{className:`${t}-success-bg`,style:b})),I=S==="outer"&&v==="start",u=S==="outer"&&v==="end";return S==="outer"&&v==="center"?s.createElement("div",{className:`${t}-layout-bottom`},k,a):s.createElement("div",{className:`${t}-outer`,style:P},I&&a,k,u&&a)},rt=e=>{const{size:t,steps:r,rounding:n=Math.round,percent:o=0,strokeWidth:l=8,strokeColor:i,trailColor:c=null,prefixCls:a,children:f}=e,d=n(r*(o/100)),p=t==="small"?2:14,v=t!=null?t:[p,l],[S,y]=Q(v,"step",{steps:r,strokeWidth:l}),C=S/r,x=Array.from({length:r});for(let g=0;g<r;g++){const h=Array.isArray(i)?i[g]:i;x[g]=s.createElement("div",{key:g,className:A()(`${a}-steps-item`,{[`${a}-steps-item-active`]:g<=d-1}),style:{backgroundColor:g<=d-1?h:c,width:C,height:y}})}return s.createElement("div",{className:`${a}-steps-outer`},x,f)},nt=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const gt=null,ot=["normal","exception","active","success"];var st=s.forwardRef((e,t)=>{const{prefixCls:r,className:n,rootClassName:o,steps:l,strokeColor:i,percent:c=0,size:a="default",showInfo:f=!0,type:d="line",status:p,format:v,style:S,percentPosition:y={}}=e,C=nt(e,["prefixCls","className","rootClassName","steps","strokeColor","percent","size","showInfo","type","status","format","style","percentPosition"]),{align:x="end",type:g="outer"}=y,h=Array.isArray(i)?i[0]:i,O=typeof i=="string"||Array.isArray(i)?i:void 0,E=s.useMemo(()=>{if(h){const W=typeof h=="string"?h:Object.values(h)[0];return new he.t(W).isLight()}return!1},[i]),$=s.useMemo(()=>{var W,L;const j=z(e);return parseInt(j!==void 0?(W=j!=null?j:0)===null||W===void 0?void 0:W.toString():(L=c!=null?c:0)===null||L===void 0?void 0:L.toString(),10)},[c,e.success,e.successPercent]),b=s.useMemo(()=>!ot.includes(p)&&$>=100?"success":p||"normal",[p,$]),{getPrefixCls:P,direction:k,progress:I}=s.useContext(xe.E_),u=P("progress",r),[H,M,D]=Je(u),T=d==="line",Z=T&&!l,N=s.useMemo(()=>{if(!f)return null;const W=z(e);let L;const j=v||(R=>`${R}%`),B=T&&E&&g==="inner";return g==="inner"||v||b!=="exception"&&b!=="success"?L=j(w(c),w(W)):b==="exception"?L=T?s.createElement(Se.Z,null):s.createElement($e.Z,null):b==="success"&&(L=T?s.createElement(ye.Z,null):s.createElement(Ce.Z,null)),s.createElement("span",{className:A()(`${u}-text`,{[`${u}-text-bright`]:B,[`${u}-text-${x}`]:Z,[`${u}-text-${g}`]:Z}),title:typeof L=="string"?L:void 0},L)},[f,c,$,b,d,u,v]);let V;d==="line"?V=l?s.createElement(rt,Object.assign({},e,{strokeColor:O,prefixCls:u,steps:typeof l=="object"?l.count:l}),N):s.createElement(tt,Object.assign({},e,{strokeColor:h,prefixCls:u,direction:k,percentPosition:{align:x,type:g}}),N):(d==="circle"||d==="dashboard")&&(V=s.createElement(Xe,Object.assign({},e,{strokeColor:h,prefixCls:u,progressStatus:b}),N));const J=A()(u,`${u}-status-${b}`,{[`${u}-${d==="dashboard"&&"circle"||d}`]:d!=="line",[`${u}-inline-circle`]:d==="circle"&&Q(a,"circle")[0]<=20,[`${u}-line`]:Z,[`${u}-line-align-${x}`]:Z,[`${u}-line-position-${g}`]:Z,[`${u}-steps`]:l,[`${u}-show-info`]:f,[`${u}-${a}`]:typeof a=="string",[`${u}-rtl`]:k==="rtl"},I==null?void 0:I.className,n,o,M,D);return H(s.createElement("div",Object.assign({ref:t,style:Object.assign(Object.assign({},I==null?void 0:I.style),S),className:J,role:"progressbar","aria-valuenow":$,"aria-valuemin":0,"aria-valuemax":100},(0,ke.Z)(C,["trailColor","strokeWidth","width","gapDegree","gapPosition","strokeLinecap","success","successPercent"])),V))}),it=st}}]);
