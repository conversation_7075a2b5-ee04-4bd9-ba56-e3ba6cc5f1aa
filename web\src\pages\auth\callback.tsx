import React, { useEffect, useState } from 'react';
import { Spin, Alert } from 'antd';
import { history, useModel } from '@umijs/max';
import keycloak from '../../../config/keycloak';

const AuthCallback: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const { setInitialState } = useModel('@@initialState');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        console.log('=== 处理Keycloak回调开始 ===');
        console.log('当前URL:', window.location.href);

        // 解析URL参数（支持query和hash两种方式）
        const urlParams = new URLSearchParams(window.location.search);
        const hashParams = new URLSearchParams(window.location.hash.substring(1));

        const code = urlParams.get('code') || hashParams.get('code');
        const state = urlParams.get('state') || hashParams.get('state');
        const sessionState = urlParams.get('session_state') || hashParams.get('session_state');
        const error = urlParams.get('error') || hashParams.get('error');
        const errorDescription = urlParams.get('error_description') || hashParams.get('error_description');

        console.log('回调参数:', {
          code: code ? '存在' : '不存在',
          state,
          sessionState,
          error,
          errorDescription
        });

        if (error) {
          throw new Error(`Keycloak错误: ${error} - ${errorDescription || '未知错误'}`);
        }

        if (code) {
          // 如果有授权码，初始化Keycloak
          console.log('检测到授权码，开始初始化Keycloak');

          const authenticated = await keycloak.init({
            onLoad: 'login-required',
            checkLoginIframe: false,
            redirectUri: window.location.origin + '/auth/callback',
            silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso.html',
            pkceMethod: 'S256'
          });

          console.log('Keycloak初始化结果:', authenticated);

          if (authenticated && keycloak.tokenParsed) {
            console.log('Keycloak认证成功，设置用户状态');

            // 获取用户信息
            const tokenParsed = keycloak.tokenParsed;
            const userInfo = {
              id: tokenParsed.sub || '',
              username: tokenParsed.preferred_username || '',
              email: tokenParsed.email || '',
              name: tokenParsed.name || tokenParsed.preferred_username || '',
              roles: tokenParsed.realm_access?.roles || [],
            };

            console.log('提取的用户信息:', userInfo);

            // 确定用户的访问级别
            const roles = userInfo.roles;
            let access = 'user';

            if (roles.includes('owner')) {
              access = 'owner';
            } else if (roles.includes('admin')) {
              access = 'admin';
            } else if (roles.includes('maintainer')) {
              access = 'maintainer';
            }

            console.log('用户角色信息:', { roles, access });

            // 更新全局状态
            console.log('开始更新全局状态...');
            setInitialState((prevState: any) => ({
              ...prevState,
              currentUser: {
                userid: userInfo.id,
                name: userInfo.name,
                username: userInfo.username,
                email: userInfo.email,
                avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(userInfo.name || userInfo.username)}&background=1890ff&color=fff`,
                roles: roles,
                access: access,
              },
            }));
            console.log('全局状态更新完成');

            // 获取重定向路径
            const redirectPath = sessionStorage.getItem('auth_redirect_path') || '/Console/projects';
            sessionStorage.removeItem('auth_redirect_path');

            console.log('认证成功，准备重定向到:', redirectPath);

            // 延迟重定向确保状态更新完成
            setTimeout(() => {
              console.log('执行重定向到:', redirectPath);
              history.replace(redirectPath);
            }, 500);
          } else {
            throw new Error('Keycloak认证失败或token解析失败');
          }
        } else {
          // 如果没有授权码，可能是直接访问，重定向到登录页面
          console.log('没有授权码，重定向到登录页面');
          setError('无效的回调访问，请重新登录');
          setTimeout(() => {
            history.replace('/user/login');
          }, 2000);
        }
      } catch (error: any) {
        console.error('回调处理失败:', error);
        console.error('错误详情:', {
          message: error?.message,
          stack: error?.stack,
          type: typeof error
        });

        let errorMessage = '未知错误';
        if (error && typeof error === 'object') {
          if (error.message) {
            errorMessage = error.message;
          } else if (error.toString && typeof error.toString === 'function') {
            errorMessage = error.toString();
          }
        } else if (typeof error === 'string') {
          errorMessage = error;
        }

        setError(`回调处理失败: ${errorMessage}`);
        setTimeout(() => {
          history.replace('/user/login');
        }, 3000);
      }
    };

    handleCallback();
  }, [setInitialState]);

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      flexDirection: 'column',
      padding: '20px',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white'
    }}>
      {error ? (
        <Alert
          message="认证错误"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      ) : (
        <>
          <Spin size="large" style={{ color: 'white' }} />
          <div style={{
            marginTop: 24,
            fontSize: '18px',
            fontWeight: 500,
            textAlign: 'center'
          }}>
            登录成功！正在跳转...
          </div>
          <div style={{
            marginTop: 8,
            fontSize: '14px',
            opacity: 0.8,
            textAlign: 'center'
          }}>
            请稍候，即将进入系统
          </div>
        </>
      )}
    </div>
  );
};

export default AuthCallback;
