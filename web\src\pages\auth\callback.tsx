import React, { useEffect, useState } from 'react';
import { Spin, Alert } from 'antd';
import { history, useModel } from '@umijs/max';
import keycloak from '../../../config/keycloak';

const AuthCallback: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const { setInitialState } = useModel('@@initialState');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        console.log('=== 处理Keycloak回调开始 ===');
        console.log('当前URL:', window.location.href);

        // 解析URL参数（支持query和hash两种方式）
        const urlParams = new URLSearchParams(window.location.search);
        const hashParams = new URLSearchParams(window.location.hash.substring(1));

        const code = urlParams.get('code') || hashParams.get('code');
        const state = urlParams.get('state') || hashParams.get('state');
        const sessionState = urlParams.get('session_state') || hashParams.get('session_state');
        const error = urlParams.get('error') || hashParams.get('error');
        const errorDescription = urlParams.get('error_description') || hashParams.get('error_description');

        console.log('回调参数:', {
          code: code ? '存在' : '不存在',
          state,
          sessionState,
          error,
          errorDescription
        });

        if (error) {
          throw new Error(`Keycloak错误: ${error} - ${errorDescription}`);
        }

        if (code) {
          // 如果有授权码，初始化Keycloak
          console.log('检测到授权码，开始初始化Keycloak');
          try {
            const authenticated = await keycloak.init({
              onLoad: 'login-required',
              checkLoginIframe: false,
              redirectUri: window.location.origin + '/auth/callback',
              silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso.html',
              pkceMethod: 'S256'
            });

            console.log('Keycloak初始化结果:', authenticated);

            if (authenticated && keycloak.tokenParsed) {
              console.log('Keycloak认证成功，设置用户状态');

              // 获取用户信息
              const userInfo = {
                id: keycloak.tokenParsed.sub,
                username: keycloak.tokenParsed.preferred_username,
                email: keycloak.tokenParsed.email,
                name: keycloak.tokenParsed.name,
                roles: keycloak.tokenParsed.realm_access?.roles || [],
              };

              // 确定用户的访问级别
              const roles = userInfo.roles || [];
              let access = 'user'; // 默认权限

              if (roles.includes('owner')) {
                access = 'owner';
              } else if (roles.includes('admin')) {
                access = 'admin';
              } else if (roles.includes('maintainer')) {
                access = 'maintainer';
              }

              console.log('用户角色信息:', { roles, access });

              // 更新全局状态
              await setInitialState((prevState: any) => ({
                ...prevState,
                currentUser: {
                  userid: userInfo.id,
                  name: userInfo.name,
                  username: userInfo.username,
                  email: userInfo.email,
                  avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(userInfo.name || userInfo.username)}&background=1890ff&color=fff`,
                  roles: roles,
                  access: access,
                },
              }));

              // 获取重定向路径
              const redirectPath = sessionStorage.getItem('auth_redirect_path') || '/Console/projects';
              sessionStorage.removeItem('auth_redirect_path');

              console.log('认证成功，重定向到:', redirectPath);

              // 使用 history.replace 进行重定向
              history.replace(redirectPath);
            } else {
              throw new Error('Keycloak认证失败');
            }
          } catch (initError: any) {
            console.error('Keycloak初始化失败:', initError);
            throw new Error(`Keycloak初始化失败: ${initError.message}`);
          }
        } else {
          // 如果没有授权码，可能是直接访问，重定向到登录页面
          console.log('没有授权码，重定向到登录页面');
          setError('无效的回调访问，请重新登录');
          setTimeout(() => {
            history.replace('/user/login');
          }, 2000);
        }
      } catch (error: any) {
        console.error('回调处理失败:', error);
        setError(`回调处理失败: ${error.message || '未知错误'}`);
        setTimeout(() => {
          history.replace('/user/login');
        }, 3000);
      }
    };

    handleCallback();
  }, [setInitialState]);

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      flexDirection: 'column',
      padding: '20px',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white'
    }}>
      {error ? (
        <Alert
          message="认证错误"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      ) : (
        <>
          <Spin size="large" style={{ color: 'white' }} />
          <div style={{
            marginTop: 24,
            fontSize: '18px',
            fontWeight: 500,
            textAlign: 'center'
          }}>
            登录成功！正在跳转...
          </div>
          <div style={{
            marginTop: 8,
            fontSize: '14px',
            opacity: 0.8,
            textAlign: 'center'
          }}>
            请稍候，即将进入系统
          </div>
        </>
      )}
    </div>
  );
};

export default AuthCallback;
