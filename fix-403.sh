#!/bin/bash

# 快速修复403 Forbidden错误

set -e

echo "=== 修复403 Forbidden错误 ==="

# 1. 获取nginx信息
NGINX_PREFIX=$(nginx -V 2>&1 | grep -o 'prefix=[^ ]*' | cut -d= -f2)
NGINX_USER=$(ps aux | grep nginx | grep -v grep | head -1 | awk '{print $1}')

echo "nginx prefix: $NGINX_PREFIX"
echo "nginx运行用户: $NGINX_USER"

# 2. 检查前端文件目录
FRONTEND_DIR=/usr/local/nginx/html/dist
echo ""
echo "步骤1: 检查前端文件目录..."
echo "目标目录: $FRONTEND_DIR"

if [ ! -d "$FRONTEND_DIR" ]; then
    echo "❌ 目录不存在，创建目录..."
     mkdir -p "$FRONTEND_DIR"
fi

# 3. 确保有index.html文件
if [ ! -f "$FRONTEND_DIR/index.html" ]; then
    echo "❌ index.html不存在，创建临时文件..."
     tee "$FRONTEND_DIR/index.html" > /dev/null << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XinHe Workbench - 403错误已修复</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        .logo { font-size: 48px; margin-bottom: 20px; }
        .title { font-size: 32px; color: #333; margin-bottom: 10px; }
        .subtitle { color: #666; margin-bottom: 30px; font-size: 18px; }
        .status {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: left;
            margin: 20px 0;
        }
        .info-item {
            margin: 8px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
        }
        .btn:hover { background: #5a6fd8; }
        .success { color: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🎉</div>
        <h1 class="title">XinHe Workbench</h1>
        <p class="subtitle">芯合跨架构智算软件工厂</p>
        
        <div class="status">
            <strong>✅ 403 Forbidden错误已修复！</strong><br>
            nginx现在可以正确访问静态文件
        </div>
        
        <div class="info">
            <div class="info-item"><strong>访问时间:</strong> <span id="time"></span></div>
            <div class="info-item"><strong>访问协议:</strong> <span id="protocol"></span></div>
            <div class="info-item"><strong>访问端口:</strong> <span id="port"></span></div>
            <div class="info-item"><strong>服务状态:</strong> <span class="success">正常运行</span></div>
        </div>
        
        <div>
            <a href="/api/health" class="btn">检查API</a>
            <button onclick="location.reload()" class="btn">刷新页面</button>
        </div>
        
        <div style="margin-top: 30px; font-size: 14px; color: #666;">
            <p>🎯 成功解决了权限问题！</p>
            <p>现在可以部署您的前端应用了。</p>
        </div>
    </div>
    
    <script>
        function updateInfo() {
            document.getElementById('time').textContent = new Date().toLocaleString('zh-CN');
            document.getElementById('protocol').textContent = location.protocol;
            document.getElementById('port').textContent = location.port || (location.protocol === 'https:' ? '443' : '80');
        }
        
        updateInfo();
        setInterval(updateInfo, 1000);
    </script>
</body>
</html>
EOF
    echo "✅ 创建了临时index.html"
else
    echo "✅ index.html已存在"
fi

# 4. 修复权限问题
echo ""
echo "步骤2: 修复权限问题..."

# 设置目录权限
echo "设置目录权限..."
 chmod 755 "$FRONTEND_DIR"
 chmod 755 "$NGINX_PREFIX/html"
 chmod 755 "$NGINX_PREFIX"

# 设置文件权限
echo "设置文件权限..."
 find "$FRONTEND_DIR" -type f -exec chmod 644 {} \;
 find "$FRONTEND_DIR" -type d -exec chmod 755 {} \;

# 设置所有者
echo "设置文件所有者..."
if [ "$NGINX_USER" = "nginx" ]; then
     chown -R nginx:nginx "$FRONTEND_DIR"
elif [ "$NGINX_USER" = "www-data" ]; then
     chown -R www-data:www-data "$FRONTEND_DIR"
else
     chown -R root:root "$FRONTEND_DIR"
fi

echo "✅ 权限设置完成"

# 5. 检查nginx配置中的用户设置
echo ""
echo "步骤3: 检查nginx用户配置..."

# 检查nginx.conf中是否有user指令
if ! grep -q "^user " /usr/local/nginx/conf/nginx.conf; then
    echo "添加user指令到nginx配置..."
    
    # 备份配置
     cp /usr/local/nginx/conf/nginx.conf /usr/local/nginx/conf/nginx.conf.backup
    
    # 添加user指令
    if [ "$NGINX_USER" = "nginx" ]; then
         sed -i '1i user nginx;' /usr/local/nginx/conf/nginx.conf
    elif [ "$NGINX_USER" = "www-data" ]; then
         sed -i '1i user www-data;' /usr/local/nginx/conf/nginx.conf
    else
         sed -i '1i user root;' /usr/local/nginx/conf/nginx.conf
    fi
    
    echo "✅ 添加了user指令"
else
    echo "✅ nginx配置中已有user指令"
fi

# 6. 测试nginx配置
echo ""
echo "步骤4: 测试nginx配置..."
if  nginx -t; then
    echo "✅ nginx配置测试通过"
else
    echo "❌ nginx配置测试失败，恢复备份"
    if [ -f "/usr/local/nginx/conf/nginx.conf.backup" ]; then
         cp /usr/local/nginx/conf/nginx.conf.backup /usr/local/nginx/conf/nginx.conf
    fi
    exit 1
fi

# 7. 重新加载nginx
echo ""
echo "步骤5: 重新加载nginx..."
 nginx -s reload

echo "✅ nginx已重新加载"

# 8. 显示权限信息
echo ""
echo "步骤6: 验证权限设置..."
echo "目录权限:"
ls -la "$NGINX_PREFIX/html/" | grep dist

echo ""
echo "文件权限:"
ls -la "$FRONTEND_DIR/" | head -5

# 9. 测试访问
echo ""
echo "步骤7: 测试访问..."
sleep 2

echo "测试本地访问:"
HTTP_CODE=$(curl -k -s -o /dev/null -w "%{http_code}" https://localhost:8089)
echo "HTTP状态码: $HTTP_CODE"

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ 访问成功！"
elif [ "$HTTP_CODE" = "403" ]; then
    echo "❌ 仍然是403错误"
    echo "检查SELinux状态..."
    if command -v getenforce &> /dev/null; then
        SELINUX_STATUS=$(getenforce)
        echo "SELinux状态: $SELINUX_STATUS"
        if [ "$SELINUX_STATUS" = "Enforcing" ]; then
            echo "尝试设置SELinux上下文..."
             setsebool -P httpd_can_network_connect 1
             restorecon -R "$FRONTEND_DIR"
        fi
    fi
else
    echo "⚠️  其他HTTP状态码: $HTTP_CODE"
fi

echo ""
echo "=== 修复完成 ==="
echo ""
echo "访问地址:"
echo "  HTTPS: https://*************:9082"
echo "  HTTP: http://*************:9083"
echo ""
echo "如果仍有403错误，请检查:"
echo "1. SELinux设置: getenforce"
echo "2. 防火墙设置"
echo "3. nginx错误日志:  tail -f /var/log/nginx/error.log"
