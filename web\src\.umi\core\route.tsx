// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/user","layout":false,"id":"1"},"2":{"name":"login","path":"/user/login","parentId":"1","id":"2"},"3":{"name":"register","path":"/user/register","parentId":"1","id":"3"},"4":{"name":"register-result","path":"/user/register-result","parentId":"1","id":"4"},"5":{"path":"/auth/callback","layout":false,"id":"5"},"6":{"path":"/auth/process-callback","layout":false,"id":"6"},"7":{"path":"/auth/logout-callback","layout":false,"id":"7"},"8":{"path":"/Console","name":"console","icon":"ConsoleSqlOutlined","parentId":"ant-design-pro-layout","id":"8"},"9":{"path":"/Console/projects","name":"projects","parentId":"8","id":"9"},"10":{"path":"/Console/xpu","name":"xpu","parentId":"8","id":"10"},"11":{"path":"/Console/plan","name":"plan","parentId":"8","id":"11"},"12":{"path":"/Console/plan/milestoneDetail","name":"里程碑详情","hideInMenu":true,"parentId":"11","id":"12"},"13":{"path":"/Console/plan/newMilestone","name":"新建里程碑","hideInMenu":true,"parentId":"11","id":"13"},"14":{"path":"/Console/plan/pipelineDetail","name":"流水线详情","hideInMenu":true,"parentId":"11","id":"14"},"15":{"path":"/repository","name":"repository","icon":"ProjectOutlined","parentId":"ant-design-pro-layout","id":"15"},"16":{"path":"/tools","name":"tools","icon":"ToolOutlined","parentId":"ant-design-pro-layout","id":"16"},"17":{"path":"/tools/modeling","name":"modeling","parentId":"16","id":"17"},"18":{"path":"/tools/s2s","name":"s2s","parentId":"16","id":"18"},"19":{"path":"/tools/ide","name":"ide","parentId":"16","id":"19"},"20":{"path":"/tools/repo","name":"repo","parentId":"16","id":"20"},"21":{"path":"/tools/repo","parentId":"20","id":"21"},"22":{"path":"/tools/repo/newProject","parentId":"20","id":"22"},"23":{"path":"/tools/repo/branch","parentId":"20","id":"23"},"24":{"path":"/tools/repo/commits","parentId":"20","id":"24"},"25":{"path":"/tools/repo/compare","parentId":"20","id":"25"},"26":{"path":"/tools/repo/files","parentId":"20","id":"26"},"27":{"path":"/tools/repo/files/newFile","parentId":"20","id":"27"},"28":{"path":"/tools/repo/newTag","parentId":"20","id":"28"},"29":{"path":"/tools/repo/compare/compare_result","parentId":"20","id":"29"},"30":{"path":"/tools/repo/tags","parentId":"20","id":"30"},"31":{"path":"/tools/repo/commits/newMergeRequest","parentId":"20","id":"31"},"32":{"path":"/tools/repo/mergeRequests","parentId":"20","id":"32"},"33":{"path":"/tools/repo/newMergeRequest","parentId":"20","id":"33"},"34":{"path":"/tools/repo/settings","parentId":"20","id":"34"},"35":{"path":"/tools/build","name":"build","parentId":"16","id":"35"},"36":{"path":"/tools/file","name":"file","parentId":"16","id":"36"},"37":{"path":"/ci","icon":"LineChartOutlined","name":"ci","parentId":"ant-design-pro-layout","id":"37"},"38":{"path":"/deploy","name":"deploy","icon":"DeploymentUnitOutlined","parentId":"ant-design-pro-layout","id":"38"},"39":{"path":"/deploy/resource","name":"resource","parentId":"38","id":"39"},"40":{"path":"/deploy/plan","name":"plan","parentId":"38","id":"40"},"41":{"path":"/deploy/tools","name":"tools","parentId":"38","id":"41"},"42":{"path":"/deploy/migration","name":"migration","parentId":"38","id":"42"},"43":{"path":"/deploy/tasks","name":"tasks","parentId":"38","id":"43"},"44":{"path":"/appstore","name":"appstore","icon":"AppstoreOutlined","parentId":"ant-design-pro-layout","id":"44"},"45":{"path":"/help","name":"help","icon":"DesktopOutlined","parentId":"ant-design-pro-layout","id":"45"},"46":{"path":"/admin","name":"admin","icon":"SettingOutlined","parentId":"ant-design-pro-layout","id":"46"},"47":{"path":"/admin/test","name":"API测试","access":"isAdmin","parentId":"46","id":"47"},"48":{"path":"/","layout":false,"id":"48"},"49":{"path":"*","layout":false,"id":"49"},"ant-design-pro-layout":{"id":"ant-design-pro-layout","path":"/","isLayout":true}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import('./EmptyRoute')),
'2': React.lazy(() => import(/* webpackChunkName: "p__User__Login__index" */'@/pages/User/Login/index.tsx')),
'3': React.lazy(() => import(/* webpackChunkName: "p__User__Register__index" */'@/pages/User/Register/index.tsx')),
'4': React.lazy(() => import(/* webpackChunkName: "p__User__register-result__index" */'@/pages/User/register-result/index.tsx')),
'5': React.lazy(() => import(/* webpackChunkName: "p__auth__callback" */'@/pages/auth/callback.tsx')),
'6': React.lazy(() => import(/* webpackChunkName: "p__auth__process-callback" */'@/pages/auth/process-callback.tsx')),
'7': React.lazy(() => import(/* webpackChunkName: "p__auth__logout-callback" */'@/pages/auth/logout-callback.tsx')),
'8': React.lazy(() => import('./EmptyRoute')),
'9': React.lazy(() => import(/* webpackChunkName: "p__Console__projects" */'@/pages/Console/projects.tsx')),
'10': React.lazy(() => import(/* webpackChunkName: "p__Console__xpu" */'@/pages/Console/xpu.tsx')),
'11': React.lazy(() => import(/* webpackChunkName: "p__Console__plan" */'@/pages/Console/plan.tsx')),
'12': React.lazy(() => import(/* webpackChunkName: "p__Console__milestoneDetail" */'@/pages/Console/milestoneDetail.tsx')),
'13': React.lazy(() => import(/* webpackChunkName: "p__Console__newMilestone" */'@/pages/Console/newMilestone.tsx')),
'14': React.lazy(() => import(/* webpackChunkName: "p__Console__pipelineDetail" */'@/pages/Console/pipelineDetail.tsx')),
'15': React.lazy(() => import(/* webpackChunkName: "p__repository__index" */'@/pages/repository/index.tsx')),
'16': React.lazy(() => import('./EmptyRoute')),
'17': React.lazy(() => import(/* webpackChunkName: "p__tools__modeling" */'@/pages/tools/modeling.tsx')),
'18': React.lazy(() => import(/* webpackChunkName: "p__tools__s2s" */'@/pages/tools/s2s.tsx')),
'19': React.lazy(() => import(/* webpackChunkName: "p__tools__ide__ide" */'@/pages/tools/ide/ide.tsx')),
'20': React.lazy(() => import(/* webpackChunkName: "layouts__RepoLayout" */'@/layouts/RepoLayout.tsx')),
'21': React.lazy(() => import(/* webpackChunkName: "p__tools__repo__index" */'@/pages/tools/repo/index.tsx')),
'22': React.lazy(() => import(/* webpackChunkName: "p__tools__repo__newProject" */'@/pages/tools/repo/newProject.tsx')),
'23': React.lazy(() => import(/* webpackChunkName: "p__tools__repo__branch" */'@/pages/tools/repo/branch.tsx')),
'24': React.lazy(() => import(/* webpackChunkName: "p__tools__repo__commits" */'@/pages/tools/repo/commits.tsx')),
'25': React.lazy(() => import(/* webpackChunkName: "p__tools__repo__compare" */'@/pages/tools/repo/compare.tsx')),
'26': React.lazy(() => import(/* webpackChunkName: "p__tools__repo__files" */'@/pages/tools/repo/files.tsx')),
'27': React.lazy(() => import(/* webpackChunkName: "p__tools__repo__newFile" */'@/pages/tools/repo/newFile.tsx')),
'28': React.lazy(() => import(/* webpackChunkName: "p__tools__repo__newTag" */'@/pages/tools/repo/newTag.tsx')),
'29': React.lazy(() => import(/* webpackChunkName: "p__tools__repo__compare_result" */'@/pages/tools/repo/compare_result.tsx')),
'30': React.lazy(() => import(/* webpackChunkName: "p__tools__repo__tags" */'@/pages/tools/repo/tags.tsx')),
'31': React.lazy(() => import(/* webpackChunkName: "p__tools__repo__newMergeRequest" */'@/pages/tools/repo/newMergeRequest.tsx')),
'32': React.lazy(() => import(/* webpackChunkName: "p__tools__repo__mergeRequests" */'@/pages/tools/repo/mergeRequests.tsx')),
'33': React.lazy(() => import(/* webpackChunkName: "p__tools__repo__newMergeRequest" */'@/pages/tools/repo/newMergeRequest.tsx')),
'34': React.lazy(() => import(/* webpackChunkName: "p__tools__repo__settings" */'@/pages/tools/repo/settings.tsx')),
'35': React.lazy(() => import(/* webpackChunkName: "p__tools__build" */'@/pages/tools/build.tsx')),
'36': React.lazy(() => import(/* webpackChunkName: "p__tools__file" */'@/pages/tools/file.tsx')),
'37': React.lazy(() => import(/* webpackChunkName: "p__ci__index" */'@/pages/ci/index.tsx')),
'38': React.lazy(() => import('./EmptyRoute')),
'39': React.lazy(() => import(/* webpackChunkName: "p__deploy__resource" */'@/pages/deploy/resource.tsx')),
'40': React.lazy(() => import(/* webpackChunkName: "p__deploy__plan" */'@/pages/deploy/plan.tsx')),
'41': React.lazy(() => import(/* webpackChunkName: "p__deploy__tools" */'@/pages/deploy/tools.tsx')),
'42': React.lazy(() => import(/* webpackChunkName: "p__deploy__migration" */'@/pages/deploy/migration.tsx')),
'43': React.lazy(() => import(/* webpackChunkName: "p__deploy__tasks" */'@/pages/deploy/tasks.tsx')),
'44': React.lazy(() => import(/* webpackChunkName: "p__appstore__index" */'@/pages/appstore/index.tsx')),
'45': React.lazy(() => import(/* webpackChunkName: "p__help__index" */'@/pages/help/index.tsx')),
'46': React.lazy(() => import('./EmptyRoute')),
'47': React.lazy(() => import(/* webpackChunkName: "p__admin__test" */'@/pages/admin/test.tsx')),
'48': React.lazy(() => import(/* webpackChunkName: "p__RootRedirect" */'@/pages/RootRedirect.tsx')),
'49': React.lazy(() => import(/* webpackChunkName: "p__404" */'@/pages/404.tsx')),
'ant-design-pro-layout': React.lazy(() => import(/* webpackChunkName: "umi__plugin-layout__Layout" */'D:/project/web_app_0527v2/web/src/.umi/plugin-layout/Layout.tsx')),
},
  };
}
