"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1470],{68334:function(J,C,e){e.r(C),e.d(C,{default:function(){return W}});var l=e(15009),O=e.n(l),A=e(13769),R=e.n(A),w=e(99289),T=e.n(w),L=e(5574),j=e.n(L),B=e(84226);function N(E){return a.apply(this,arguments)}function a(){return a=T()(O()().mark(function E(m){return O()().wrap(function(D){for(;;)switch(D.prev=D.next){case 0:return D.abrupt("return",(0,B.request)("/api/user/register",{method:"POST",data:m}));case 1:case"end":return D.stop()}},E)})),a.apply(this,arguments)}var r=e(10915),c=e(47019),s=e(34041),i=e(9361),t=e(2453),f=e(38703),u=e(55102),o=e(55241),d=e(78957),h=e(83622),g=e(67294),P=e(28846),$=(0,P.kc)(function(E){var m=E.token;return{main:{width:"368px",margin:"auto",paddingTop:"10%",h3:{marginBottom:"20px",fontSize:"16px"}},password:{marginBottom:"24px",".ant-form-item-explain":{display:"none"}},getCaptcha:{display:"block",width:"100%"},lang:{width:42,height:42,lineHeight:"42px",position:"fixed",right:16,borderRadius:m.borderRadius,":hover":{backgroundColor:m.colorBgTextHover}},footer:{width:"100%",display:"flex",alignItems:"center",justifyContent:"space-between"},submit:{width:"50%"},success:{transition:"color 0.3s",color:m.colorSuccess},warning:{transition:"color 0.3s",color:m.colorWarning},error:{transition:"color 0.3s",color:m.colorError},"progress-pass > .progress":{".ant-progress-bg":{backgroundColor:m.colorWarning}}}}),I=$,n=e(85893),U=["confirm"],p=c.Z.Item,_=s.Z.Option,V={ok:"success",pass:"normal",poor:"exception"},W=function(){var E=I(),m=E.styles,K=(0,B.useIntl)(),D=i.Z.useToken(),x=D.token,v=c.Z.useForm(),b=j()(v,1),M=b[0],X=(0,g.useState)(0),z=j()(X,2),q=z[0],ue=z[1],le=(0,g.useState)(!1),ee=j()(le,2),Q=ee[0],te=ee[1],ne=(0,g.useState)("86"),re=j()(ne,2),ie=re[0],se=re[1],oe=(0,g.useState)(!1),F=j()(oe,2),Y=F[0],k=F[1],me=!1,ce,ge=(0,g.useState)(!0),de=j()(ge,2),je=de[0],ve=de[1],he={ok:(0,n.jsx)("div",{className:m.success,children:(0,n.jsx)("span",{children:"\u5F3A\u5EA6\uFF1A\u5F3A"})}),pass:(0,n.jsx)("div",{className:m.warning,children:(0,n.jsx)("span",{children:"\u5F3A\u5EA6\uFF1A\u4E2D"})}),poor:(0,n.jsx)("div",{className:m.error,children:(0,n.jsx)("span",{children:"\u5F3A\u5EA6\uFF1A\u592A\u77ED"})})};(0,g.useEffect)(function(){return function(){clearInterval(ce)}},[ce]);var fe=function(){var y=M.getFieldValue("password");return y&&y.length>9?"ok":y&&y.length>5?"pass":"poor"},ye=function(){var G=T()(O()().mark(function y(S){var H,pe,ae;return O()().wrap(function(Z){for(;;)switch(Z.prev=Z.next){case 0:return Z.prev=0,H=S.confirm,pe=R()(S,U),Z.next=4,N(pe);case 4:ae=Z.sent,console.log("Register response:",ae.data),ae.success===!0?(t.ZP.success("\u6CE8\u518C\u6210\u529F\uFF01"),B.history.push({pathname:"/user/register-result?account=".concat(S.email)})):t.ZP.error(ae.message||"\u6CE8\u518C\u5931\u8D25,\u8BF7\u91CD\u8BD5\uFF01"),Z.next=13;break;case 9:Z.prev=9,Z.t0=Z.catch(0),t.ZP.error("\u6CE8\u518C\u5931\u8D25,\u8BF7\u91CD\u8BD5\uFF01"),console.error("\u6CE8\u518C\u5931\u8D25:",Z.t0);case 13:case"end":return Z.stop()}},y,null,[[0,9]])}));return function(S){return G.apply(this,arguments)}}(),Ee=function(y,S){var H=Promise;return S&&S!==M.getFieldValue("password")?H.reject("\u4E24\u6B21\u8F93\u5165\u7684\u5BC6\u7801\u4E0D\u5339\u914D!"):H.resolve()},Oe=function(y,S){var H=Promise;return S?(Q||te(!!S),k(!Y),S.length<6?H.reject(""):(S&&me&&M.validateFields(["confirm"]),H.resolve())):(te(!!S),H.reject("\u8BF7\u8F93\u5165\u5BC6\u7801!"))},Pe=function(y){se(y)},xe=function(){var y=M.getFieldValue("password"),S=fe();return y&&y.length?(0,n.jsx)("div",{children:(0,n.jsx)(f.Z,{status:V[S],strokeWidth:6,percent:y.length*10>100?100:y.length*10,showInfo:!1})}):null},Ce=function(y){ve(y==="admin"),M.setFieldsValue({access:y})};return(0,n.jsx)(r._Y,{hashed:!1,children:(0,n.jsxs)("div",{className:m.main,children:[(0,n.jsxs)("h3",{children:[" ",K.formatMessage({id:"pages.login.registerAccount",defaultMessage:"\u6CE8\u518C\u8D26\u6237"})]}),(0,n.jsxs)(c.Z,{form:M,name:"UserRegister",onFinish:ye,children:[(0,n.jsx)(p,{name:"username",rules:[{type:"string",message:"\u8BF7\u4EE5\u5B57\u6BCD\u5F00\u5934\uFF01"},{required:!0,message:"\u8BF7\u8F93\u5165\u7528\u6237\u540D\uFF01"},{}],children:(0,n.jsx)(u.Z,{placeholder:"\u7528\u6237\u540D"})}),(0,n.jsx)(p,{name:"email",rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u90AE\u7BB1\u5730\u5740!"},{type:"email",message:"\u90AE\u7BB1\u5730\u5740\u683C\u5F0F\u9519\u8BEF!"}],children:(0,n.jsx)(u.Z,{size:"large",placeholder:"\u90AE\u7BB1"})}),(0,n.jsx)(o.Z,{getPopupContainer:function(y){return y&&y.parentNode?y.parentNode:y},content:Q&&(0,n.jsxs)("div",{style:{padding:"4px 0"},children:[he[fe()],xe(),(0,n.jsx)("div",{style:{marginTop:10},children:(0,n.jsx)("span",{children:"\u8BF7\u81F3\u5C11\u8F93\u5165 6 \u4E2A\u5B57\u7B26\u3002\u8BF7\u4E0D\u8981\u4F7F\u7528\u5BB9\u6613\u88AB\u731C\u5230\u7684\u5BC6\u7801\u3002"})})]}),overlayStyle:{width:240},placement:"right",open:Q,children:(0,n.jsx)(p,{name:"password",className:M.getFieldValue("password")&&M.getFieldValue("password").length>0&&m.password,rules:[{validator:Oe}],children:(0,n.jsx)(u.Z,{size:"large",type:"password",placeholder:"\u81F3\u5C116\u4F4D\u5BC6\u7801\uFF0C\u533A\u5206\u5927\u5C0F\u5199"})})}),(0,n.jsx)(p,{name:"confirm",rules:[{required:!0,message:"\u786E\u8BA4\u5BC6\u7801"},{validator:Ee}],children:(0,n.jsx)(u.Z,{size:"large",type:"password",placeholder:"\u786E\u8BA4\u5BC6\u7801"})}),(0,n.jsx)(p,{name:"phone",rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7!"},{pattern:/^\d{11}$/,message:"\u624B\u673A\u53F7\u683C\u5F0F\u9519\u8BEF!"}],children:(0,n.jsxs)(d.Z.Compact,{style:{width:"100%"},children:[(0,n.jsxs)(s.Z,{size:"large",value:ie,onChange:Pe,style:{width:"30%"},children:[(0,n.jsx)(_,{value:"86",children:"+86"}),(0,n.jsx)(_,{value:"87",children:"+87"})]}),(0,n.jsx)(u.Z,{size:"large",placeholder:"\u624B\u673A\u53F7"})]})}),(0,n.jsx)(p,{label:"\u7528\u6237\u8EAB\u4EFD\uFF1A",name:"access",rules:[{required:!0,message:"\u8BF7\u9009\u62E9\u662F\u5426\u4E3A\u7BA1\u7406\u5458\uFF01"}],children:(0,n.jsxs)(s.Z,{size:"large",onChange:Ce,defaultValue:"admin",children:[(0,n.jsx)(_,{value:"admin",children:"\u7BA1\u7406\u5458"}),(0,n.jsx)(_,{value:"user",children:"\u666E\u901A\u7528\u6237"})]})}),(0,n.jsx)(p,{children:(0,n.jsxs)("div",{className:m.footer,children:[(0,n.jsx)(h.ZP,{size:"large",className:m.submit,type:"primary",htmlType:"submit",children:(0,n.jsx)("span",{children:"\u6CE8\u518C"})}),(0,n.jsx)(B.Link,{to:"/user/login",children:(0,n.jsx)("span",{children:"\u4F7F\u7528\u5DF2\u6709\u8D26\u6237\u767B\u5F55"})})]})})]})]})})}},81643:function(J,C,e){e.d(C,{Z:function(){return l}});const l=O=>O?typeof O=="function"?O():O:null},99134:function(J,C,e){var l=e(67294);const O=(0,l.createContext)({});C.Z=O},21584:function(J,C,e){var l=e(67294),O=e(93967),A=e.n(O),R=e(53124),w=e(99134),T=e(6999),L=function(a,r){var c={};for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&r.indexOf(s)<0&&(c[s]=a[s]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,s=Object.getOwnPropertySymbols(a);i<s.length;i++)r.indexOf(s[i])<0&&Object.prototype.propertyIsEnumerable.call(a,s[i])&&(c[s[i]]=a[s[i]]);return c};function j(a){return typeof a=="number"?`${a} ${a} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(a)?`0 0 ${a}`:a}const B=["xs","sm","md","lg","xl","xxl"],N=l.forwardRef((a,r)=>{const{getPrefixCls:c,direction:s}=l.useContext(R.E_),{gutter:i,wrap:t}=l.useContext(w.Z),{prefixCls:f,span:u,order:o,offset:d,push:h,pull:g,className:P,children:$,flex:I,style:n}=a,U=L(a,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),p=c("col",f),[_,V,W]=(0,T.cG)(p),E={};let m={};B.forEach(x=>{let v={};const b=a[x];typeof b=="number"?v.span=b:typeof b=="object"&&(v=b||{}),delete U[x],m=Object.assign(Object.assign({},m),{[`${p}-${x}-${v.span}`]:v.span!==void 0,[`${p}-${x}-order-${v.order}`]:v.order||v.order===0,[`${p}-${x}-offset-${v.offset}`]:v.offset||v.offset===0,[`${p}-${x}-push-${v.push}`]:v.push||v.push===0,[`${p}-${x}-pull-${v.pull}`]:v.pull||v.pull===0,[`${p}-rtl`]:s==="rtl"}),v.flex&&(m[`${p}-${x}-flex`]=!0,E[`--${p}-${x}-flex`]=j(v.flex))});const K=A()(p,{[`${p}-${u}`]:u!==void 0,[`${p}-order-${o}`]:o,[`${p}-offset-${d}`]:d,[`${p}-push-${h}`]:h,[`${p}-pull-${g}`]:g},P,m,V,W),D={};if(i&&i[0]>0){const x=i[0]/2;D.paddingLeft=x,D.paddingRight=x}return I&&(D.flex=j(I),t===!1&&!D.minWidth&&(D.minWidth=0)),_(l.createElement("div",Object.assign({},U,{style:Object.assign(Object.assign(Object.assign({},D),n),E),className:K,ref:r}),$))});C.Z=N},17621:function(J,C,e){e.d(C,{Z:function(){return i}});var l=e(67294),O=e(93967),A=e.n(O),R=e(74443),w=e(53124),T=e(25378);function L(t,f){const u=[void 0,void 0],o=Array.isArray(t)?t:[t,void 0],d=f||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return o.forEach((h,g)=>{if(typeof h=="object"&&h!==null)for(let P=0;P<R.c4.length;P++){const $=R.c4[P];if(d[$]&&h[$]!==void 0){u[g]=h[$];break}}else u[g]=h}),u}var j=e(99134),B=e(6999),N=function(t,f){var u={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&f.indexOf(o)<0&&(u[o]=t[o]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var d=0,o=Object.getOwnPropertySymbols(t);d<o.length;d++)f.indexOf(o[d])<0&&Object.prototype.propertyIsEnumerable.call(t,o[d])&&(u[o[d]]=t[o[d]]);return u};const a=null,r=null;function c(t,f){const[u,o]=l.useState(typeof t=="string"?t:""),d=()=>{if(typeof t=="string"&&o(t),typeof t=="object")for(let h=0;h<R.c4.length;h++){const g=R.c4[h];if(!f||!f[g])continue;const P=t[g];if(P!==void 0){o(P);return}}};return l.useEffect(()=>{d()},[JSON.stringify(t),f]),u}var i=l.forwardRef((t,f)=>{const{prefixCls:u,justify:o,align:d,className:h,style:g,children:P,gutter:$=0,wrap:I}=t,n=N(t,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:U,direction:p}=l.useContext(w.E_),_=(0,T.Z)(!0,null),V=c(d,_),W=c(o,_),E=U("row",u),[m,K,D]=(0,B.VM)(E),x=L($,_),v=A()(E,{[`${E}-no-wrap`]:I===!1,[`${E}-${W}`]:W,[`${E}-${V}`]:V,[`${E}-rtl`]:p==="rtl"},h,K,D),b={},M=x[0]!=null&&x[0]>0?x[0]/-2:void 0;M&&(b.marginLeft=M,b.marginRight=M);const[X,z]=x;b.rowGap=z;const q=l.useMemo(()=>({gutter:[X,z],wrap:I}),[X,z,I]);return m(l.createElement(j.Z.Provider,{value:q},l.createElement("div",Object.assign({},n,{className:v,style:Object.assign(Object.assign({},b),g),ref:f}),P)))})},66330:function(J,C,e){e.d(C,{aV:function(){return B}});var l=e(67294),O=e(93967),A=e.n(O),R=e(92419),w=e(81643),T=e(53124),L=e(20136),j=function(r,c){var s={};for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&c.indexOf(i)<0&&(s[i]=r[i]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var t=0,i=Object.getOwnPropertySymbols(r);t<i.length;t++)c.indexOf(i[t])<0&&Object.prototype.propertyIsEnumerable.call(r,i[t])&&(s[i[t]]=r[i[t]]);return s};const B=({title:r,content:c,prefixCls:s})=>!r&&!c?null:l.createElement(l.Fragment,null,r&&l.createElement("div",{className:`${s}-title`},r),c&&l.createElement("div",{className:`${s}-inner-content`},c)),N=r=>{const{hashId:c,prefixCls:s,className:i,style:t,placement:f="top",title:u,content:o,children:d}=r,h=(0,w.Z)(u),g=(0,w.Z)(o),P=A()(c,s,`${s}-pure`,`${s}-placement-${f}`,i);return l.createElement("div",{className:P,style:t},l.createElement("div",{className:`${s}-arrow`}),l.createElement(R.G,Object.assign({},r,{className:c,prefixCls:s}),d||l.createElement(B,{prefixCls:s,title:h,content:g})))},a=r=>{const{prefixCls:c,className:s}=r,i=j(r,["prefixCls","className"]),{getPrefixCls:t}=l.useContext(T.E_),f=t("popover",c),[u,o,d]=(0,L.Z)(f);return u(l.createElement(N,Object.assign({},i,{prefixCls:f,hashId:o,className:A()(s,d)})))};C.ZP=a},55241:function(J,C,e){var l=e(67294),O=e(93967),A=e.n(O),R=e(21770),w=e(15105),T=e(81643),L=e(33603),j=e(96159),B=e(83062),N=e(66330),a=e(53124),r=e(20136),c=function(t,f){var u={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&f.indexOf(o)<0&&(u[o]=t[o]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var d=0,o=Object.getOwnPropertySymbols(t);d<o.length;d++)f.indexOf(o[d])<0&&Object.prototype.propertyIsEnumerable.call(t,o[d])&&(u[o[d]]=t[o[d]]);return u};const i=l.forwardRef((t,f)=>{var u,o;const{prefixCls:d,title:h,content:g,overlayClassName:P,placement:$="top",trigger:I="hover",children:n,mouseEnterDelay:U=.1,mouseLeaveDelay:p=.1,onOpenChange:_,overlayStyle:V={},styles:W,classNames:E}=t,m=c(t,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:K,className:D,style:x,classNames:v,styles:b}=(0,a.dj)("popover"),M=K("popover",d),[X,z,q]=(0,r.Z)(M),ue=K(),le=A()(P,z,q,D,v.root,E==null?void 0:E.root),ee=A()(v.body,E==null?void 0:E.body),[Q,te]=(0,R.Z)(!1,{value:(u=t.open)!==null&&u!==void 0?u:t.visible,defaultValue:(o=t.defaultOpen)!==null&&o!==void 0?o:t.defaultVisible}),ne=(F,Y)=>{te(F,!0),_==null||_(F,Y)},re=F=>{F.keyCode===w.Z.ESC&&ne(!1,F)},ie=F=>{ne(F)},se=(0,T.Z)(h),oe=(0,T.Z)(g);return X(l.createElement(B.Z,Object.assign({placement:$,trigger:I,mouseEnterDelay:U,mouseLeaveDelay:p},m,{prefixCls:M,classNames:{root:le,body:ee},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},b.root),x),V),W==null?void 0:W.root),body:Object.assign(Object.assign({},b.body),W==null?void 0:W.body)},ref:f,open:Q,onOpenChange:ie,overlay:se||oe?l.createElement(N.aV,{prefixCls:M,title:se,content:oe}):null,transitionName:(0,L.m)(ue,"zoom-big",m.transitionName),"data-popover-inject":!0}),(0,j.Tm)(n,{onKeyDown:F=>{var Y,k;l.isValidElement(n)&&((k=n==null?void 0:(Y=n.props).onKeyDown)===null||k===void 0||k.call(Y,F)),re(F)}})))});i._InternalPanelDoNotUseOrYouWillBeFired=N.ZP,C.Z=i},20136:function(J,C,e){var l=e(14747),O=e(50438),A=e(97414),R=e(79511),w=e(8796),T=e(83559),L=e(83262);const j=a=>{const{componentCls:r,popoverColor:c,titleMinWidth:s,fontWeightStrong:i,innerPadding:t,boxShadowSecondary:f,colorTextHeading:u,borderRadiusLG:o,zIndexPopup:d,titleMarginBottom:h,colorBgElevated:g,popoverBg:P,titleBorderBottom:$,innerContentPadding:I,titlePadding:n}=a;return[{[r]:Object.assign(Object.assign({},(0,l.Wf)(a)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:d,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":g,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${r}-content`]:{position:"relative"},[`${r}-inner`]:{backgroundColor:P,backgroundClip:"padding-box",borderRadius:o,boxShadow:f,padding:t},[`${r}-title`]:{minWidth:s,marginBottom:h,color:u,fontWeight:i,borderBottom:$,padding:n},[`${r}-inner-content`]:{color:c,padding:I}})},(0,A.ZP)(a,"var(--antd-arrow-background-color)"),{[`${r}-pure`]:{position:"relative",maxWidth:"none",margin:a.sizePopupArrow,display:"inline-block",[`${r}-content`]:{display:"inline-block"}}}]},B=a=>{const{componentCls:r}=a;return{[r]:w.i.map(c=>{const s=a[`${c}6`];return{[`&${r}-${c}`]:{"--antd-arrow-background-color":s,[`${r}-inner`]:{backgroundColor:s},[`${r}-arrow`]:{background:"transparent"}}}})}},N=a=>{const{lineWidth:r,controlHeight:c,fontHeight:s,padding:i,wireframe:t,zIndexPopupBase:f,borderRadiusLG:u,marginXS:o,lineType:d,colorSplit:h,paddingSM:g}=a,P=c-s,$=P/2,I=P/2-r,n=i;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:f+30},(0,R.w)(a)),(0,A.wZ)({contentRadius:u,limitVerticalRadius:!0})),{innerPadding:t?0:12,titleMarginBottom:t?0:o,titlePadding:t?`${$}px ${n}px ${I}px`:0,titleBorderBottom:t?`${r}px ${d} ${h}`:"none",innerContentPadding:t?`${g}px ${n}px`:0})};C.Z=(0,T.I$)("Popover",a=>{const{colorBgElevated:r,colorText:c}=a,s=(0,L.IX)(a,{popoverBg:r,popoverColor:c});return[j(s),B(s),(0,O._y)(s,"zoom-big")]},N,{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]})}}]);
