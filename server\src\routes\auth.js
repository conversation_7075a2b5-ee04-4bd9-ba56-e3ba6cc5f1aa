const express = require("express");
const { getKeycloak } = require("../config/keycloak");
const { asyncHandler } = require("../middleware/errorHandler");

const router = express.Router();

// Keycloak登录重定向
router.get(
  "/login",
  asyncHandler(async (req, res) => {
    const keycloak = getKeycloak();
    const loginUrl = keycloak.loginUrl(
      req.session.id,
      req.query.redirect || process.env.FRONTEND_URL
    );

    res.json({
      success: true,
      loginUrl,
      message: "请重定向到Keycloak登录页面",
    });
  })
);

// Keycloak登出
router.post(
  "/logout",
  asyncHandler(async (req, res) => {
    const keycloak = getKeycloak();

    try {
      // 获取token（从Authorization header或请求体）
      const token =
        req.headers.authorization?.replace("Bearer ", "") || req.body.token;

      if (token) {
        // 如果有token，尝试验证并撤销
        try {
          const grant = await keycloak.grantManager.createGrant({
            access_token: token,
          });

          if (grant && grant.access_token) {
            // 撤销token
            await keycloak.grantManager.userInfo(token);
            console.log("Token验证成功，准备登出");
          }
        } catch (tokenError) {
          console.log("Token验证失败，继续登出流程:", tokenError.message);
        }
      }

      // 清除session
      if (req.session) {
        req.session.destroy((err) => {
          if (err) {
            console.error("Session销毁失败:", err);
          }
        });
      }

      // 生成Keycloak登出URL
      const redirectUri =
        req.body.redirectUri ||
        req.query.redirect ||
        process.env.FRONTEND_URL + "/user/login";
      const logoutUrl = keycloak.logoutUrl(redirectUri);

      res.json({
        success: true,
        logoutUrl,
        message: "登出成功，请重定向到Keycloak登出页面",
      });
    } catch (error) {
      console.error("登出处理失败:", error);

      // 即使出错也返回登出URL，确保用户能够登出
      const redirectUri =
        req.body.redirectUri ||
        req.query.redirect ||
        process.env.FRONTEND_URL + "/user/login";
      const logoutUrl = keycloak.logoutUrl(redirectUri);

      res.json({
        success: true,
        logoutUrl,
        message: "登出成功",
      });
    }
  })
);

// 获取Keycloak配置信息（用于前端）
router.get("/config", (req, res) => {
  res.json({
    success: true,
    data: {
      url: process.env.KEYCLOAK_URL,
      realm: process.env.KEYCLOAK_REALM,
      clientId: process.env.KEYCLOAK_CLIENT_ID,
    },
  });
});

// Token验证端点
router.post(
  "/verify",
  asyncHandler(async (req, res) => {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: "缺少token参数",
      });
    }

    try {
      const keycloak = getKeycloak();
      const grant = await keycloak.grantManager.createGrant({
        access_token: token,
      });

      if (grant && grant.access_token) {
        res.json({
          success: true,
          valid: true,
          message: "Token有效",
        });
      } else {
        res.status(401).json({
          success: false,
          valid: false,
          message: "Token无效",
        });
      }
    } catch (error) {
      console.error("Token验证失败:", error);
      res.status(401).json({
        success: false,
        valid: false,
        message: "Token验证失败",
      });
    }
  })
);

// Keycloak回调处理端点
router.get(
  "/callback",
  asyncHandler(async (req, res) => {
    try {
      console.log("=== 后端处理Keycloak回调 ===");
      console.log("查询参数:", req.query);

      const { code, redirect } = req.query;

      if (!code) {
        console.error("回调中缺少授权码");
        return res.redirect(
          `${process.env.FRONTEND_URL}/user/login?error=missing_code`
        );
      }

      // 获取重定向路径，默认为主页
      const redirectPath = redirect || "/Console/projects";
      const frontendUrl = process.env.FRONTEND_URL || "http://localhost:8088";

      console.log("准备重定向到:", `${frontendUrl}${redirectPath}`);

      // 重定向到前端回调页面，将授权码作为查询参数传递
      const targetUrl = `${frontendUrl}/auth/callback?code=${encodeURIComponent(
        code
      )}&redirect=${encodeURIComponent(redirectPath)}`;

      console.log("重定向URL:", targetUrl);
      res.redirect(targetUrl);
    } catch (error) {
      console.error("后端回调处理失败:", error);
      const frontendUrl = process.env.FRONTEND_URL || "http://localhost:8088";
      res.redirect(`${frontendUrl}/user/login?error=callback_failed`);
    }
  })
);

module.exports = router;
