"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[665],{90665:function(Ln,fe,q){q.r(fe),q.d(fe,{CompletionAdapter:function(){return lt},DefinitionAdapter:function(){return Kt},DiagnosticsAdapter:function(){return ct},DocumentColorAdapter:function(){return bt},DocumentFormattingEditProvider:function(){return ht},DocumentHighlightAdapter:function(){return Gt},DocumentLinkAdapter:function(){return rn},DocumentRangeFormattingEditProvider:function(){return _t},DocumentSymbolAdapter:function(){return pt},FoldingRangeAdapter:function(){return wt},HoverAdapter:function(){return ft},ReferenceAdapter:function(){return Ct},RenameAdapter:function(){return en},SelectionRangeAdapter:function(){return At},WorkerManager:function(){return ge},fromPosition:function(){return R},fromRange:function(){return ae},getWorker:function(){return kn},setupMode:function(){return wn},toRange:function(){return b},toTextEdit:function(){return T}});var xt=q(89732);var Pt=Object.defineProperty,yt=Object.getOwnPropertyDescriptor,St=Object.getOwnPropertyNames,Wt=Object.prototype.hasOwnProperty,de=(e,r,i,n)=>{if(r&&typeof r=="object"||typeof r=="function")for(let t of St(r))!Wt.call(e,t)&&t!==i&&Pt(e,t,{get:()=>r[t],enumerable:!(n=yt(r,t))||n.enumerable});return e},Bt=(e,r,i)=>(de(e,r,"default"),i&&de(i,r,"default")),f={};Bt(f,xt);var Vt=2*60*1e3,ge=class{constructor(e){this._defaults=e,this._worker=null,this._client=null,this._idleCheckInterval=window.setInterval(()=>this._checkIfIdle(),30*1e3),this._lastUsedTime=0,this._configChangeListener=this._defaults.onDidChange(()=>this._stopWorker())}_stopWorker(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null}dispose(){clearInterval(this._idleCheckInterval),this._configChangeListener.dispose(),this._stopWorker()}_checkIfIdle(){if(!this._worker)return;Date.now()-this._lastUsedTime>Vt&&this._stopWorker()}_getClient(){return this._lastUsedTime=Date.now(),this._client||(this._worker=f.editor.createWebWorker({moduleId:"vs/language/json/jsonWorker",label:this._defaults.languageId,createData:{languageSettings:this._defaults.diagnosticsOptions,languageId:this._defaults.languageId,enableSchemaRequest:this._defaults.diagnosticsOptions.enableSchemaRequest}}),this._client=this._worker.getProxy()),this._client}getLanguageServiceWorker(...e){let r;return this._getClient().then(i=>{r=i}).then(i=>{if(this._worker)return this._worker.withSyncedResources(e)}).then(i=>r)}},pe;(function(e){function r(i){return typeof i=="string"}e.is=r})(pe||(pe={}));var $;(function(e){function r(i){return typeof i=="string"}e.is=r})($||($={}));var ve;(function(e){e.MIN_VALUE=-2147483648,e.MAX_VALUE=2147483647;function r(i){return typeof i=="number"&&e.MIN_VALUE<=i&&i<=e.MAX_VALUE}e.is=r})(ve||(ve={}));var V;(function(e){e.MIN_VALUE=0,e.MAX_VALUE=2147483647;function r(i){return typeof i=="number"&&e.MIN_VALUE<=i&&i<=e.MAX_VALUE}e.is=r})(V||(V={}));var L;(function(e){function r(n,t){return n===Number.MAX_VALUE&&(n=V.MAX_VALUE),t===Number.MAX_VALUE&&(t=V.MAX_VALUE),{line:n,character:t}}e.create=r;function i(n){let t=n;return a.objectLiteral(t)&&a.uinteger(t.line)&&a.uinteger(t.character)}e.is=i})(L||(L={}));var m;(function(e){function r(n,t,o,s){if(a.uinteger(n)&&a.uinteger(t)&&a.uinteger(o)&&a.uinteger(s))return{start:L.create(n,t),end:L.create(o,s)};if(L.is(n)&&L.is(t))return{start:n,end:t};throw new Error(`Range#create called with invalid arguments[${n}, ${t}, ${o}, ${s}]`)}e.create=r;function i(n){let t=n;return a.objectLiteral(t)&&L.is(t.start)&&L.is(t.end)}e.is=i})(m||(m={}));var H;(function(e){function r(n,t){return{uri:n,range:t}}e.create=r;function i(n){let t=n;return a.objectLiteral(t)&&m.is(t.range)&&(a.string(t.uri)||a.undefined(t.uri))}e.is=i})(H||(H={}));var me;(function(e){function r(n,t,o,s){return{targetUri:n,targetRange:t,targetSelectionRange:o,originSelectionRange:s}}e.create=r;function i(n){let t=n;return a.objectLiteral(t)&&m.is(t.targetRange)&&a.string(t.targetUri)&&m.is(t.targetSelectionRange)&&(m.is(t.originSelectionRange)||a.undefined(t.originSelectionRange))}e.is=i})(me||(me={}));var Q;(function(e){function r(n,t,o,s){return{red:n,green:t,blue:o,alpha:s}}e.create=r;function i(n){const t=n;return a.objectLiteral(t)&&a.numberRange(t.red,0,1)&&a.numberRange(t.green,0,1)&&a.numberRange(t.blue,0,1)&&a.numberRange(t.alpha,0,1)}e.is=i})(Q||(Q={}));var he;(function(e){function r(n,t){return{range:n,color:t}}e.create=r;function i(n){const t=n;return a.objectLiteral(t)&&m.is(t.range)&&Q.is(t.color)}e.is=i})(he||(he={}));var _e;(function(e){function r(n,t,o){return{label:n,textEdit:t,additionalTextEdits:o}}e.create=r;function i(n){const t=n;return a.objectLiteral(t)&&a.string(t.label)&&(a.undefined(t.textEdit)||F.is(t))&&(a.undefined(t.additionalTextEdits)||a.typedArray(t.additionalTextEdits,F.is))}e.is=i})(_e||(_e={}));var P;(function(e){e.Comment="comment",e.Imports="imports",e.Region="region"})(P||(P={}));var ke;(function(e){function r(n,t,o,s,u,g){const c={startLine:n,endLine:t};return a.defined(o)&&(c.startCharacter=o),a.defined(s)&&(c.endCharacter=s),a.defined(u)&&(c.kind=u),a.defined(g)&&(c.collapsedText=g),c}e.create=r;function i(n){const t=n;return a.objectLiteral(t)&&a.uinteger(t.startLine)&&a.uinteger(t.startLine)&&(a.undefined(t.startCharacter)||a.uinteger(t.startCharacter))&&(a.undefined(t.endCharacter)||a.uinteger(t.endCharacter))&&(a.undefined(t.kind)||a.string(t.kind))}e.is=i})(ke||(ke={}));var Y;(function(e){function r(n,t){return{location:n,message:t}}e.create=r;function i(n){let t=n;return a.defined(t)&&H.is(t.location)&&a.string(t.message)}e.is=i})(Y||(Y={}));var D;(function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4})(D||(D={}));var be;(function(e){e.Unnecessary=1,e.Deprecated=2})(be||(be={}));var we;(function(e){function r(i){const n=i;return a.objectLiteral(n)&&a.string(n.href)}e.is=r})(we||(we={}));var z;(function(e){function r(n,t,o,s,u,g){let c={range:n,message:t};return a.defined(o)&&(c.severity=o),a.defined(s)&&(c.code=s),a.defined(u)&&(c.source=u),a.defined(g)&&(c.relatedInformation=g),c}e.create=r;function i(n){var t;let o=n;return a.defined(o)&&m.is(o.range)&&a.string(o.message)&&(a.number(o.severity)||a.undefined(o.severity))&&(a.integer(o.code)||a.string(o.code)||a.undefined(o.code))&&(a.undefined(o.codeDescription)||a.string((t=o.codeDescription)===null||t===void 0?void 0:t.href))&&(a.string(o.source)||a.undefined(o.source))&&(a.undefined(o.relatedInformation)||a.typedArray(o.relatedInformation,Y.is))}e.is=i})(z||(z={}));var M;(function(e){function r(n,t,...o){let s={title:n,command:t};return a.defined(o)&&o.length>0&&(s.arguments=o),s}e.create=r;function i(n){let t=n;return a.defined(t)&&a.string(t.title)&&a.string(t.command)}e.is=i})(M||(M={}));var F;(function(e){function r(o,s){return{range:o,newText:s}}e.replace=r;function i(o,s){return{range:{start:o,end:o},newText:s}}e.insert=i;function n(o){return{range:o,newText:""}}e.del=n;function t(o){const s=o;return a.objectLiteral(s)&&a.string(s.newText)&&m.is(s.range)}e.is=t})(F||(F={}));var G;(function(e){function r(n,t,o){const s={label:n};return t!==void 0&&(s.needsConfirmation=t),o!==void 0&&(s.description=o),s}e.create=r;function i(n){const t=n;return a.objectLiteral(t)&&a.string(t.label)&&(a.boolean(t.needsConfirmation)||t.needsConfirmation===void 0)&&(a.string(t.description)||t.description===void 0)}e.is=i})(G||(G={}));var U;(function(e){function r(i){const n=i;return a.string(n)}e.is=r})(U||(U={}));var Ae;(function(e){function r(o,s,u){return{range:o,newText:s,annotationId:u}}e.replace=r;function i(o,s,u){return{range:{start:o,end:o},newText:s,annotationId:u}}e.insert=i;function n(o,s){return{range:o,newText:"",annotationId:s}}e.del=n;function t(o){const s=o;return F.is(s)&&(G.is(s.annotationId)||U.is(s.annotationId))}e.is=t})(Ae||(Ae={}));var Z;(function(e){function r(n,t){return{textDocument:n,edits:t}}e.create=r;function i(n){let t=n;return a.defined(t)&&ne.is(t.textDocument)&&Array.isArray(t.edits)}e.is=i})(Z||(Z={}));var K;(function(e){function r(n,t,o){let s={kind:"create",uri:n};return t!==void 0&&(t.overwrite!==void 0||t.ignoreIfExists!==void 0)&&(s.options=t),o!==void 0&&(s.annotationId=o),s}e.create=r;function i(n){let t=n;return t&&t.kind==="create"&&a.string(t.uri)&&(t.options===void 0||(t.options.overwrite===void 0||a.boolean(t.options.overwrite))&&(t.options.ignoreIfExists===void 0||a.boolean(t.options.ignoreIfExists)))&&(t.annotationId===void 0||U.is(t.annotationId))}e.is=i})(K||(K={}));var C;(function(e){function r(n,t,o,s){let u={kind:"rename",oldUri:n,newUri:t};return o!==void 0&&(o.overwrite!==void 0||o.ignoreIfExists!==void 0)&&(u.options=o),s!==void 0&&(u.annotationId=s),u}e.create=r;function i(n){let t=n;return t&&t.kind==="rename"&&a.string(t.oldUri)&&a.string(t.newUri)&&(t.options===void 0||(t.options.overwrite===void 0||a.boolean(t.options.overwrite))&&(t.options.ignoreIfExists===void 0||a.boolean(t.options.ignoreIfExists)))&&(t.annotationId===void 0||U.is(t.annotationId))}e.is=i})(C||(C={}));var ee;(function(e){function r(n,t,o){let s={kind:"delete",uri:n};return t!==void 0&&(t.recursive!==void 0||t.ignoreIfNotExists!==void 0)&&(s.options=t),o!==void 0&&(s.annotationId=o),s}e.create=r;function i(n){let t=n;return t&&t.kind==="delete"&&a.string(t.uri)&&(t.options===void 0||(t.options.recursive===void 0||a.boolean(t.options.recursive))&&(t.options.ignoreIfNotExists===void 0||a.boolean(t.options.ignoreIfNotExists)))&&(t.annotationId===void 0||U.is(t.annotationId))}e.is=i})(ee||(ee={}));var te;(function(e){function r(i){let n=i;return n&&(n.changes!==void 0||n.documentChanges!==void 0)&&(n.documentChanges===void 0||n.documentChanges.every(t=>a.string(t.kind)?K.is(t)||C.is(t)||ee.is(t):Z.is(t)))}e.is=r})(te||(te={}));var Ee;(function(e){function r(n){return{uri:n}}e.create=r;function i(n){let t=n;return a.defined(t)&&a.string(t.uri)}e.is=i})(Ee||(Ee={}));var Ie;(function(e){function r(n,t){return{uri:n,version:t}}e.create=r;function i(n){let t=n;return a.defined(t)&&a.string(t.uri)&&a.integer(t.version)}e.is=i})(Ie||(Ie={}));var ne;(function(e){function r(n,t){return{uri:n,version:t}}e.create=r;function i(n){let t=n;return a.defined(t)&&a.string(t.uri)&&(t.version===null||a.integer(t.version))}e.is=i})(ne||(ne={}));var Le;(function(e){function r(n,t,o,s){return{uri:n,languageId:t,version:o,text:s}}e.create=r;function i(n){let t=n;return a.defined(t)&&a.string(t.uri)&&a.string(t.languageId)&&a.integer(t.version)&&a.string(t.text)}e.is=i})(Le||(Le={}));var re;(function(e){e.PlainText="plaintext",e.Markdown="markdown";function r(i){const n=i;return n===e.PlainText||n===e.Markdown}e.is=r})(re||(re={}));var y;(function(e){function r(i){const n=i;return a.objectLiteral(i)&&re.is(n.kind)&&a.string(n.value)}e.is=r})(y||(y={}));var h;(function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25})(h||(h={}));var ie;(function(e){e.PlainText=1,e.Snippet=2})(ie||(ie={}));var Oe;(function(e){e.Deprecated=1})(Oe||(Oe={}));var Re;(function(e){function r(n,t,o){return{newText:n,insert:t,replace:o}}e.create=r;function i(n){const t=n;return t&&a.string(t.newText)&&m.is(t.insert)&&m.is(t.replace)}e.is=i})(Re||(Re={}));var Ne;(function(e){e.asIs=1,e.adjustIndentation=2})(Ne||(Ne={}));var De;(function(e){function r(i){const n=i;return n&&(a.string(n.detail)||n.detail===void 0)&&(a.string(n.description)||n.description===void 0)}e.is=r})(De||(De={}));var Me;(function(e){function r(i){return{label:i}}e.create=r})(Me||(Me={}));var Fe;(function(e){function r(i,n){return{items:i||[],isIncomplete:!!n}}e.create=r})(Fe||(Fe={}));var X;(function(e){function r(n){return n.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}e.fromPlainText=r;function i(n){const t=n;return a.string(t)||a.objectLiteral(t)&&a.string(t.language)&&a.string(t.value)}e.is=i})(X||(X={}));var Ue;(function(e){function r(i){let n=i;return!!n&&a.objectLiteral(n)&&(y.is(n.contents)||X.is(n.contents)||a.typedArray(n.contents,X.is))&&(i.range===void 0||m.is(i.range))}e.is=r})(Ue||(Ue={}));var Te;(function(e){function r(i,n){return n?{label:i,documentation:n}:{label:i}}e.create=r})(Te||(Te={}));var je;(function(e){function r(i,n,...t){let o={label:i};return a.defined(n)&&(o.documentation=n),a.defined(t)?o.parameters=t:o.parameters=[],o}e.create=r})(je||(je={}));var S;(function(e){e.Text=1,e.Read=2,e.Write=3})(S||(S={}));var xe;(function(e){function r(i,n){let t={range:i};return a.number(n)&&(t.kind=n),t}e.create=r})(xe||(xe={}));var _;(function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26})(_||(_={}));var Pe;(function(e){e.Deprecated=1})(Pe||(Pe={}));var ye;(function(e){function r(i,n,t,o,s){let u={name:i,kind:n,location:{uri:o,range:t}};return s&&(u.containerName=s),u}e.create=r})(ye||(ye={}));var Se;(function(e){function r(i,n,t,o){return o!==void 0?{name:i,kind:n,location:{uri:t,range:o}}:{name:i,kind:n,location:{uri:t}}}e.create=r})(Se||(Se={}));var We;(function(e){function r(n,t,o,s,u,g){let c={name:n,detail:t,kind:o,range:s,selectionRange:u};return g!==void 0&&(c.children=g),c}e.create=r;function i(n){let t=n;return t&&a.string(t.name)&&a.number(t.kind)&&m.is(t.range)&&m.is(t.selectionRange)&&(t.detail===void 0||a.string(t.detail))&&(t.deprecated===void 0||a.boolean(t.deprecated))&&(t.children===void 0||Array.isArray(t.children))&&(t.tags===void 0||Array.isArray(t.tags))}e.is=i})(We||(We={}));var Be;(function(e){e.Empty="",e.QuickFix="quickfix",e.Refactor="refactor",e.RefactorExtract="refactor.extract",e.RefactorInline="refactor.inline",e.RefactorRewrite="refactor.rewrite",e.Source="source",e.SourceOrganizeImports="source.organizeImports",e.SourceFixAll="source.fixAll"})(Be||(Be={}));var J;(function(e){e.Invoked=1,e.Automatic=2})(J||(J={}));var Ve;(function(e){function r(n,t,o){let s={diagnostics:n};return t!=null&&(s.only=t),o!=null&&(s.triggerKind=o),s}e.create=r;function i(n){let t=n;return a.defined(t)&&a.typedArray(t.diagnostics,z.is)&&(t.only===void 0||a.typedArray(t.only,a.string))&&(t.triggerKind===void 0||t.triggerKind===J.Invoked||t.triggerKind===J.Automatic)}e.is=i})(Ve||(Ve={}));var He;(function(e){function r(n,t,o){let s={title:n},u=!0;return typeof t=="string"?(u=!1,s.kind=t):M.is(t)?s.command=t:s.edit=t,u&&o!==void 0&&(s.kind=o),s}e.create=r;function i(n){let t=n;return t&&a.string(t.title)&&(t.diagnostics===void 0||a.typedArray(t.diagnostics,z.is))&&(t.kind===void 0||a.string(t.kind))&&(t.edit!==void 0||t.command!==void 0)&&(t.command===void 0||M.is(t.command))&&(t.isPreferred===void 0||a.boolean(t.isPreferred))&&(t.edit===void 0||te.is(t.edit))}e.is=i})(He||(He={}));var ze;(function(e){function r(n,t){let o={range:n};return a.defined(t)&&(o.data=t),o}e.create=r;function i(n){let t=n;return a.defined(t)&&m.is(t.range)&&(a.undefined(t.command)||M.is(t.command))}e.is=i})(ze||(ze={}));var Xe;(function(e){function r(n,t){return{tabSize:n,insertSpaces:t}}e.create=r;function i(n){let t=n;return a.defined(t)&&a.uinteger(t.tabSize)&&a.boolean(t.insertSpaces)}e.is=i})(Xe||(Xe={}));var Je;(function(e){function r(n,t,o){return{range:n,target:t,data:o}}e.create=r;function i(n){let t=n;return a.defined(t)&&m.is(t.range)&&(a.undefined(t.target)||a.string(t.target))}e.is=i})(Je||(Je={}));var qe;(function(e){function r(n,t){return{range:n,parent:t}}e.create=r;function i(n){let t=n;return a.objectLiteral(t)&&m.is(t.range)&&(t.parent===void 0||e.is(t.parent))}e.is=i})(qe||(qe={}));var $e;(function(e){e.namespace="namespace",e.type="type",e.class="class",e.enum="enum",e.interface="interface",e.struct="struct",e.typeParameter="typeParameter",e.parameter="parameter",e.variable="variable",e.property="property",e.enumMember="enumMember",e.event="event",e.function="function",e.method="method",e.macro="macro",e.keyword="keyword",e.modifier="modifier",e.comment="comment",e.string="string",e.number="number",e.regexp="regexp",e.operator="operator",e.decorator="decorator"})($e||($e={}));var Qe;(function(e){e.declaration="declaration",e.definition="definition",e.readonly="readonly",e.static="static",e.deprecated="deprecated",e.abstract="abstract",e.async="async",e.modification="modification",e.documentation="documentation",e.defaultLibrary="defaultLibrary"})(Qe||(Qe={}));var Ye;(function(e){function r(i){const n=i;return a.objectLiteral(n)&&(n.resultId===void 0||typeof n.resultId=="string")&&Array.isArray(n.data)&&(n.data.length===0||typeof n.data[0]=="number")}e.is=r})(Ye||(Ye={}));var Ge;(function(e){function r(n,t){return{range:n,text:t}}e.create=r;function i(n){const t=n;return t!=null&&m.is(t.range)&&a.string(t.text)}e.is=i})(Ge||(Ge={}));var Ze;(function(e){function r(n,t,o){return{range:n,variableName:t,caseSensitiveLookup:o}}e.create=r;function i(n){const t=n;return t!=null&&m.is(t.range)&&a.boolean(t.caseSensitiveLookup)&&(a.string(t.variableName)||t.variableName===void 0)}e.is=i})(Ze||(Ze={}));var Ke;(function(e){function r(n,t){return{range:n,expression:t}}e.create=r;function i(n){const t=n;return t!=null&&m.is(t.range)&&(a.string(t.expression)||t.expression===void 0)}e.is=i})(Ke||(Ke={}));var Ce;(function(e){function r(n,t){return{frameId:n,stoppedLocation:t}}e.create=r;function i(n){const t=n;return a.defined(t)&&m.is(n.stoppedLocation)}e.is=i})(Ce||(Ce={}));var oe;(function(e){e.Type=1,e.Parameter=2;function r(i){return i===1||i===2}e.is=r})(oe||(oe={}));var se;(function(e){function r(n){return{value:n}}e.create=r;function i(n){const t=n;return a.objectLiteral(t)&&(t.tooltip===void 0||a.string(t.tooltip)||y.is(t.tooltip))&&(t.location===void 0||H.is(t.location))&&(t.command===void 0||M.is(t.command))}e.is=i})(se||(se={}));var et;(function(e){function r(n,t,o){const s={position:n,label:t};return o!==void 0&&(s.kind=o),s}e.create=r;function i(n){const t=n;return a.objectLiteral(t)&&L.is(t.position)&&(a.string(t.label)||a.typedArray(t.label,se.is))&&(t.kind===void 0||oe.is(t.kind))&&t.textEdits===void 0||a.typedArray(t.textEdits,F.is)&&(t.tooltip===void 0||a.string(t.tooltip)||y.is(t.tooltip))&&(t.paddingLeft===void 0||a.boolean(t.paddingLeft))&&(t.paddingRight===void 0||a.boolean(t.paddingRight))}e.is=i})(et||(et={}));var tt;(function(e){function r(i){return{kind:"snippet",value:i}}e.createSnippet=r})(tt||(tt={}));var nt;(function(e){function r(i,n,t,o){return{insertText:i,filterText:n,range:t,command:o}}e.create=r})(nt||(nt={}));var rt;(function(e){function r(i){return{items:i}}e.create=r})(rt||(rt={}));var it;(function(e){e.Invoked=0,e.Automatic=1})(it||(it={}));var ot;(function(e){function r(i,n){return{range:i,text:n}}e.create=r})(ot||(ot={}));var st;(function(e){function r(i,n){return{triggerKind:i,selectedCompletionInfo:n}}e.create=r})(st||(st={}));var at;(function(e){function r(i){const n=i;return a.objectLiteral(n)&&$.is(n.uri)&&a.string(n.name)}e.is=r})(at||(at={}));var ut;(function(e){function r(o,s,u,g){return new Ht(o,s,u,g)}e.create=r;function i(o){let s=o;return!!(a.defined(s)&&a.string(s.uri)&&(a.undefined(s.languageId)||a.string(s.languageId))&&a.uinteger(s.lineCount)&&a.func(s.getText)&&a.func(s.positionAt)&&a.func(s.offsetAt))}e.is=i;function n(o,s){let u=o.getText(),g=t(s,(v,d)=>{let k=v.range.start.line-d.range.start.line;return k===0?v.range.start.character-d.range.start.character:k}),c=u.length;for(let v=g.length-1;v>=0;v--){let d=g[v],k=o.offsetAt(d.range.start),p=o.offsetAt(d.range.end);if(p<=c)u=u.substring(0,k)+d.newText+u.substring(p,u.length);else throw new Error("Overlapping edit");c=k}return u}e.applyEdits=n;function t(o,s){if(o.length<=1)return o;const u=o.length/2|0,g=o.slice(0,u),c=o.slice(u);t(g,s),t(c,s);let v=0,d=0,k=0;for(;v<g.length&&d<c.length;)s(g[v],c[d])<=0?o[k++]=g[v++]:o[k++]=c[d++];for(;v<g.length;)o[k++]=g[v++];for(;d<c.length;)o[k++]=c[d++];return o}})(ut||(ut={}));var Ht=class{constructor(e,r,i,n){this._uri=e,this._languageId=r,this._version=i,this._content=n,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let r=this.offsetAt(e.start),i=this.offsetAt(e.end);return this._content.substring(r,i)}return this._content}update(e,r){this._content=e.text,this._version=r,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let e=[],r=this._content,i=!0;for(let n=0;n<r.length;n++){i&&(e.push(n),i=!1);let t=r.charAt(n);i=t==="\r"||t===`
`,t==="\r"&&n+1<r.length&&r.charAt(n+1)===`
`&&n++}i&&r.length>0&&e.push(r.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let r=this.getLineOffsets(),i=0,n=r.length;if(n===0)return L.create(0,e);for(;i<n;){let o=Math.floor((i+n)/2);r[o]>e?n=o:i=o+1}let t=i-1;return L.create(t,e-r[t])}offsetAt(e){let r=this.getLineOffsets();if(e.line>=r.length)return this._content.length;if(e.line<0)return 0;let i=r[e.line],n=e.line+1<r.length?r[e.line+1]:this._content.length;return Math.max(Math.min(i+e.character,n),i)}get lineCount(){return this.getLineOffsets().length}},a;(function(e){const r=Object.prototype.toString;function i(p){return typeof p!="undefined"}e.defined=i;function n(p){return typeof p=="undefined"}e.undefined=n;function t(p){return p===!0||p===!1}e.boolean=t;function o(p){return r.call(p)==="[object String]"}e.string=o;function s(p){return r.call(p)==="[object Number]"}e.number=s;function u(p,N,ce){return r.call(p)==="[object Number]"&&N<=p&&p<=ce}e.numberRange=u;function g(p){return r.call(p)==="[object Number]"&&-2147483648<=p&&p<=2147483647}e.integer=g;function c(p){return r.call(p)==="[object Number]"&&0<=p&&p<=2147483647}e.uinteger=c;function v(p){return r.call(p)==="[object Function]"}e.func=v;function d(p){return p!==null&&typeof p=="object"}e.objectLiteral=d;function k(p,N){return Array.isArray(p)&&p.every(N)}e.typedArray=k})(a||(a={}));var ct=class{constructor(e,r,i){this._languageId=e,this._worker=r,this._disposables=[],this._listener=Object.create(null);const n=o=>{let s=o.getLanguageId();if(s!==this._languageId)return;let u;this._listener[o.uri.toString()]=o.onDidChangeContent(()=>{window.clearTimeout(u),u=window.setTimeout(()=>this._doValidate(o.uri,s),500)}),this._doValidate(o.uri,s)},t=o=>{f.editor.setModelMarkers(o,this._languageId,[]);let s=o.uri.toString(),u=this._listener[s];u&&(u.dispose(),delete this._listener[s])};this._disposables.push(f.editor.onDidCreateModel(n)),this._disposables.push(f.editor.onWillDisposeModel(t)),this._disposables.push(f.editor.onDidChangeModelLanguage(o=>{t(o.model),n(o.model)})),this._disposables.push(i(o=>{f.editor.getModels().forEach(s=>{s.getLanguageId()===this._languageId&&(t(s),n(s))})})),this._disposables.push({dispose:()=>{f.editor.getModels().forEach(t);for(let o in this._listener)this._listener[o].dispose()}}),f.editor.getModels().forEach(n)}dispose(){this._disposables.forEach(e=>e&&e.dispose()),this._disposables.length=0}_doValidate(e,r){this._worker(e).then(i=>i.doValidation(e.toString())).then(i=>{const n=i.map(o=>Xt(e,o));let t=f.editor.getModel(e);t&&t.getLanguageId()===r&&f.editor.setModelMarkers(t,r,n)}).then(void 0,i=>{console.error(i)})}};function zt(e){switch(e){case D.Error:return f.MarkerSeverity.Error;case D.Warning:return f.MarkerSeverity.Warning;case D.Information:return f.MarkerSeverity.Info;case D.Hint:return f.MarkerSeverity.Hint;default:return f.MarkerSeverity.Info}}function Xt(e,r){let i=typeof r.code=="number"?String(r.code):r.code;return{severity:zt(r.severity),startLineNumber:r.range.start.line+1,startColumn:r.range.start.character+1,endLineNumber:r.range.end.line+1,endColumn:r.range.end.character+1,message:r.message,code:i,source:r.source}}var lt=class{constructor(e,r){this._worker=e,this._triggerCharacters=r}get triggerCharacters(){return this._triggerCharacters}provideCompletionItems(e,r,i,n){const t=e.uri;return this._worker(t).then(o=>o.doComplete(t.toString(),R(r))).then(o=>{if(!o)return;const s=e.getWordUntilPosition(r),u=new f.Range(r.lineNumber,s.startColumn,r.lineNumber,s.endColumn),g=o.items.map(c=>{const v={label:c.label,insertText:c.insertText||c.label,sortText:c.sortText,filterText:c.filterText,documentation:c.documentation,detail:c.detail,command:$t(c.command),range:u,kind:qt(c.kind)};return c.textEdit&&(Jt(c.textEdit)?v.range={insert:b(c.textEdit.insert),replace:b(c.textEdit.replace)}:v.range=b(c.textEdit.range),v.insertText=c.textEdit.newText),c.additionalTextEdits&&(v.additionalTextEdits=c.additionalTextEdits.map(T)),c.insertTextFormat===ie.Snippet&&(v.insertTextRules=f.languages.CompletionItemInsertTextRule.InsertAsSnippet),v});return{isIncomplete:o.isIncomplete,suggestions:g}})}};function R(e){if(e)return{character:e.column-1,line:e.lineNumber-1}}function ae(e){if(e)return{start:{line:e.startLineNumber-1,character:e.startColumn-1},end:{line:e.endLineNumber-1,character:e.endColumn-1}}}function b(e){if(e)return new f.Range(e.start.line+1,e.start.character+1,e.end.line+1,e.end.character+1)}function Jt(e){return typeof e.insert!="undefined"&&typeof e.replace!="undefined"}function qt(e){const r=f.languages.CompletionItemKind;switch(e){case h.Text:return r.Text;case h.Method:return r.Method;case h.Function:return r.Function;case h.Constructor:return r.Constructor;case h.Field:return r.Field;case h.Variable:return r.Variable;case h.Class:return r.Class;case h.Interface:return r.Interface;case h.Module:return r.Module;case h.Property:return r.Property;case h.Unit:return r.Unit;case h.Value:return r.Value;case h.Enum:return r.Enum;case h.Keyword:return r.Keyword;case h.Snippet:return r.Snippet;case h.Color:return r.Color;case h.File:return r.File;case h.Reference:return r.Reference}return r.Property}function T(e){if(e)return{range:b(e.range),text:e.newText}}function $t(e){return e&&e.command==="editor.action.triggerSuggest"?{id:e.command,title:e.title,arguments:e.arguments}:void 0}var ft=class{constructor(e){this._worker=e}provideHover(e,r,i){let n=e.uri;return this._worker(n).then(t=>t.doHover(n.toString(),R(r))).then(t=>{if(t)return{range:b(t.range),contents:Yt(t.contents)}})}};function Qt(e){return e&&typeof e=="object"&&typeof e.kind=="string"}function dt(e){return typeof e=="string"?{value:e}:Qt(e)?e.kind==="plaintext"?{value:e.value.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}:{value:e.value}:{value:"```"+e.language+`
`+e.value+"\n```\n"}}function Yt(e){if(e)return Array.isArray(e)?e.map(dt):[dt(e)]}var Gt=class{constructor(e){this._worker=e}provideDocumentHighlights(e,r,i){const n=e.uri;return this._worker(n).then(t=>t.findDocumentHighlights(n.toString(),R(r))).then(t=>{if(t)return t.map(o=>({range:b(o.range),kind:Zt(o.kind)}))})}};function Zt(e){switch(e){case S.Read:return f.languages.DocumentHighlightKind.Read;case S.Write:return f.languages.DocumentHighlightKind.Write;case S.Text:return f.languages.DocumentHighlightKind.Text}return f.languages.DocumentHighlightKind.Text}var Kt=class{constructor(e){this._worker=e}provideDefinition(e,r,i){const n=e.uri;return this._worker(n).then(t=>t.findDefinition(n.toString(),R(r))).then(t=>{if(t)return[gt(t)]})}};function gt(e){return{uri:f.Uri.parse(e.uri),range:b(e.range)}}var Ct=class{constructor(e){this._worker=e}provideReferences(e,r,i,n){const t=e.uri;return this._worker(t).then(o=>o.findReferences(t.toString(),R(r))).then(o=>{if(o)return o.map(gt)})}},en=class{constructor(e){this._worker=e}provideRenameEdits(e,r,i,n){const t=e.uri;return this._worker(t).then(o=>o.doRename(t.toString(),R(r),i)).then(o=>tn(o))}};function tn(e){if(!e||!e.changes)return;let r=[];for(let i in e.changes){const n=f.Uri.parse(i);for(let t of e.changes[i])r.push({resource:n,versionId:void 0,textEdit:{range:b(t.range),text:t.newText}})}return{edits:r}}var pt=class{constructor(e){this._worker=e}provideDocumentSymbols(e,r){const i=e.uri;return this._worker(i).then(n=>n.findDocumentSymbols(i.toString())).then(n=>{if(n)return n.map(t=>nn(t)?vt(t):{name:t.name,detail:"",containerName:t.containerName,kind:mt(t.kind),range:b(t.location.range),selectionRange:b(t.location.range),tags:[]})})}};function nn(e){return"children"in e}function vt(e){var r,i,n;return{name:e.name,detail:(r=e.detail)!=null?r:"",kind:mt(e.kind),range:b(e.range),selectionRange:b(e.selectionRange),tags:(i=e.tags)!=null?i:[],children:((n=e.children)!=null?n:[]).map(t=>vt(t))}}function mt(e){let r=f.languages.SymbolKind;switch(e){case _.File:return r.File;case _.Module:return r.Module;case _.Namespace:return r.Namespace;case _.Package:return r.Package;case _.Class:return r.Class;case _.Method:return r.Method;case _.Property:return r.Property;case _.Field:return r.Field;case _.Constructor:return r.Constructor;case _.Enum:return r.Enum;case _.Interface:return r.Interface;case _.Function:return r.Function;case _.Variable:return r.Variable;case _.Constant:return r.Constant;case _.String:return r.String;case _.Number:return r.Number;case _.Boolean:return r.Boolean;case _.Array:return r.Array}return r.Function}var rn=class{constructor(e){this._worker=e}provideLinks(e,r){const i=e.uri;return this._worker(i).then(n=>n.findDocumentLinks(i.toString())).then(n=>{if(n)return{links:n.map(t=>({range:b(t.range),url:t.target}))}})}},ht=class{constructor(e){this._worker=e}provideDocumentFormattingEdits(e,r,i){const n=e.uri;return this._worker(n).then(t=>t.format(n.toString(),null,kt(r)).then(o=>{if(!(!o||o.length===0))return o.map(T)}))}},_t=class{constructor(e){this._worker=e,this.canFormatMultipleRanges=!1}provideDocumentRangeFormattingEdits(e,r,i,n){const t=e.uri;return this._worker(t).then(o=>o.format(t.toString(),ae(r),kt(i)).then(s=>{if(!(!s||s.length===0))return s.map(T)}))}};function kt(e){return{tabSize:e.tabSize,insertSpaces:e.insertSpaces}}var bt=class{constructor(e){this._worker=e}provideDocumentColors(e,r){const i=e.uri;return this._worker(i).then(n=>n.findDocumentColors(i.toString())).then(n=>{if(n)return n.map(t=>({color:t.color,range:b(t.range)}))})}provideColorPresentations(e,r,i){const n=e.uri;return this._worker(n).then(t=>t.getColorPresentations(n.toString(),r.color,ae(r.range))).then(t=>{if(t)return t.map(o=>{let s={label:o.label};return o.textEdit&&(s.textEdit=T(o.textEdit)),o.additionalTextEdits&&(s.additionalTextEdits=o.additionalTextEdits.map(T)),s})})}},wt=class{constructor(e){this._worker=e}provideFoldingRanges(e,r,i){const n=e.uri;return this._worker(n).then(t=>t.getFoldingRanges(n.toString(),r)).then(t=>{if(t)return t.map(o=>{const s={start:o.startLine+1,end:o.endLine+1};return typeof o.kind!="undefined"&&(s.kind=on(o.kind)),s})})}};function on(e){switch(e){case P.Comment:return f.languages.FoldingRangeKind.Comment;case P.Imports:return f.languages.FoldingRangeKind.Imports;case P.Region:return f.languages.FoldingRangeKind.Region}}var At=class{constructor(e){this._worker=e}provideSelectionRanges(e,r,i){const n=e.uri;return this._worker(n).then(t=>t.getSelectionRanges(n.toString(),r.map(R))).then(t=>{if(t)return t.map(o=>{const s=[];for(;o;)s.push({range:b(o.range)}),o=o.parent;return s})})}};function sn(e,r=!1){const i=e.length;let n=0,t="",o=0,s=16,u=0,g=0,c=0,v=0,d=0;function k(l,A){let O=0,I=0;for(;O<l||!A;){let w=e.charCodeAt(n);if(w>=48&&w<=57)I=I*16+w-48;else if(w>=65&&w<=70)I=I*16+w-65+10;else if(w>=97&&w<=102)I=I*16+w-97+10;else break;n++,O++}return O<l&&(I=-1),I}function p(l){n=l,t="",o=0,s=16,d=0}function N(){let l=n;if(e.charCodeAt(n)===48)n++;else for(n++;n<e.length&&j(e.charCodeAt(n));)n++;if(n<e.length&&e.charCodeAt(n)===46)if(n++,n<e.length&&j(e.charCodeAt(n)))for(n++;n<e.length&&j(e.charCodeAt(n));)n++;else return d=3,e.substring(l,n);let A=n;if(n<e.length&&(e.charCodeAt(n)===69||e.charCodeAt(n)===101))if(n++,(n<e.length&&e.charCodeAt(n)===43||e.charCodeAt(n)===45)&&n++,n<e.length&&j(e.charCodeAt(n))){for(n++;n<e.length&&j(e.charCodeAt(n));)n++;A=n}else d=3;return e.substring(l,A)}function ce(){let l="",A=n;for(;;){if(n>=i){l+=e.substring(A,n),d=2;break}const O=e.charCodeAt(n);if(O===34){l+=e.substring(A,n),n++;break}if(O===92){if(l+=e.substring(A,n),n++,n>=i){d=2;break}switch(e.charCodeAt(n++)){case 34:l+='"';break;case 92:l+="\\";break;case 47:l+="/";break;case 98:l+="\b";break;case 102:l+="\f";break;case 110:l+=`
`;break;case 114:l+="\r";break;case 116:l+="	";break;case 117:const w=k(4,!0);w>=0?l+=String.fromCharCode(w):d=4;break;default:d=5}A=n;continue}if(O>=0&&O<=31)if(W(O)){l+=e.substring(A,n),d=2;break}else d=6;n++}return l}function Tt(){if(t="",d=0,o=n,g=u,v=c,n>=i)return o=i,s=17;let l=e.charCodeAt(n);if(ue(l)){do n++,t+=String.fromCharCode(l),l=e.charCodeAt(n);while(ue(l));return s=15}if(W(l))return n++,t+=String.fromCharCode(l),l===13&&e.charCodeAt(n)===10&&(n++,t+=`
`),u++,c=n,s=14;switch(l){case 123:return n++,s=1;case 125:return n++,s=2;case 91:return n++,s=3;case 93:return n++,s=4;case 58:return n++,s=6;case 44:return n++,s=5;case 34:return n++,t=ce(),s=10;case 47:const A=n-1;if(e.charCodeAt(n+1)===47){for(n+=2;n<i&&!W(e.charCodeAt(n));)n++;return t=e.substring(A,n),s=12}if(e.charCodeAt(n+1)===42){n+=2;const O=i-1;let I=!1;for(;n<O;){const w=e.charCodeAt(n);if(w===42&&e.charCodeAt(n+1)===47){n+=2,I=!0;break}n++,W(w)&&(w===13&&e.charCodeAt(n)===10&&n++,u++,c=n)}return I||(n++,d=1),t=e.substring(A,n),s=13}return t+=String.fromCharCode(l),n++,s=16;case 45:if(t+=String.fromCharCode(l),n++,n===i||!j(e.charCodeAt(n)))return s=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return t+=N(),s=11;default:for(;n<i&&En(l);)n++,l=e.charCodeAt(n);if(o!==n){switch(t=e.substring(o,n),t){case"true":return s=8;case"false":return s=9;case"null":return s=7}return s=16}return t+=String.fromCharCode(l),n++,s=16}}function En(l){if(ue(l)||W(l))return!1;switch(l){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}function In(){let l;do l=Tt();while(l>=12&&l<=15);return l}return{setPosition:p,getPosition:()=>n,scan:r?In:Tt,getToken:()=>s,getTokenValue:()=>t,getTokenOffset:()=>o,getTokenLength:()=>n-o,getTokenStartLine:()=>g,getTokenStartCharacter:()=>o-v,getTokenError:()=>d}}function ue(e){return e===32||e===9}function W(e){return e===10||e===13}function j(e){return e>=48&&e<=57}var Et;(function(e){e[e.lineFeed=10]="lineFeed",e[e.carriageReturn=13]="carriageReturn",e[e.space=32]="space",e[e._0=48]="_0",e[e._1=49]="_1",e[e._2=50]="_2",e[e._3=51]="_3",e[e._4=52]="_4",e[e._5=53]="_5",e[e._6=54]="_6",e[e._7=55]="_7",e[e._8=56]="_8",e[e._9=57]="_9",e[e.a=97]="a",e[e.b=98]="b",e[e.c=99]="c",e[e.d=100]="d",e[e.e=101]="e",e[e.f=102]="f",e[e.g=103]="g",e[e.h=104]="h",e[e.i=105]="i",e[e.j=106]="j",e[e.k=107]="k",e[e.l=108]="l",e[e.m=109]="m",e[e.n=110]="n",e[e.o=111]="o",e[e.p=112]="p",e[e.q=113]="q",e[e.r=114]="r",e[e.s=115]="s",e[e.t=116]="t",e[e.u=117]="u",e[e.v=118]="v",e[e.w=119]="w",e[e.x=120]="x",e[e.y=121]="y",e[e.z=122]="z",e[e.A=65]="A",e[e.B=66]="B",e[e.C=67]="C",e[e.D=68]="D",e[e.E=69]="E",e[e.F=70]="F",e[e.G=71]="G",e[e.H=72]="H",e[e.I=73]="I",e[e.J=74]="J",e[e.K=75]="K",e[e.L=76]="L",e[e.M=77]="M",e[e.N=78]="N",e[e.O=79]="O",e[e.P=80]="P",e[e.Q=81]="Q",e[e.R=82]="R",e[e.S=83]="S",e[e.T=84]="T",e[e.U=85]="U",e[e.V=86]="V",e[e.W=87]="W",e[e.X=88]="X",e[e.Y=89]="Y",e[e.Z=90]="Z",e[e.asterisk=42]="asterisk",e[e.backslash=92]="backslash",e[e.closeBrace=125]="closeBrace",e[e.closeBracket=93]="closeBracket",e[e.colon=58]="colon",e[e.comma=44]="comma",e[e.dot=46]="dot",e[e.doubleQuote=34]="doubleQuote",e[e.minus=45]="minus",e[e.openBrace=123]="openBrace",e[e.openBracket=91]="openBracket",e[e.plus=43]="plus",e[e.slash=47]="slash",e[e.formFeed=12]="formFeed",e[e.tab=9]="tab"})(Et||(Et={}));var On=new Array(20).fill(0).map((e,r)=>" ".repeat(r)),x=200,Rn={" ":{"\n":new Array(x).fill(0).map((e,r)=>`
`+" ".repeat(r)),"\r":new Array(x).fill(0).map((e,r)=>"\r"+" ".repeat(r)),"\r\n":new Array(x).fill(0).map((e,r)=>`\r
`+" ".repeat(r))},"	":{"\n":new Array(x).fill(0).map((e,r)=>`
`+"	".repeat(r)),"\r":new Array(x).fill(0).map((e,r)=>"\r"+"	".repeat(r)),"\r\n":new Array(x).fill(0).map((e,r)=>`\r
`+"	".repeat(r))}},It;(function(e){e.DEFAULT={allowTrailingComma:!1}})(It||(It={}));var an=sn,Lt;(function(e){e[e.None=0]="None",e[e.UnexpectedEndOfComment=1]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=2]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=3]="UnexpectedEndOfNumber",e[e.InvalidUnicode=4]="InvalidUnicode",e[e.InvalidEscapeCharacter=5]="InvalidEscapeCharacter",e[e.InvalidCharacter=6]="InvalidCharacter"})(Lt||(Lt={}));var Ot;(function(e){e[e.OpenBraceToken=1]="OpenBraceToken",e[e.CloseBraceToken=2]="CloseBraceToken",e[e.OpenBracketToken=3]="OpenBracketToken",e[e.CloseBracketToken=4]="CloseBracketToken",e[e.CommaToken=5]="CommaToken",e[e.ColonToken=6]="ColonToken",e[e.NullKeyword=7]="NullKeyword",e[e.TrueKeyword=8]="TrueKeyword",e[e.FalseKeyword=9]="FalseKeyword",e[e.StringLiteral=10]="StringLiteral",e[e.NumericLiteral=11]="NumericLiteral",e[e.LineCommentTrivia=12]="LineCommentTrivia",e[e.BlockCommentTrivia=13]="BlockCommentTrivia",e[e.LineBreakTrivia=14]="LineBreakTrivia",e[e.Trivia=15]="Trivia",e[e.Unknown=16]="Unknown",e[e.EOF=17]="EOF"})(Ot||(Ot={}));var Rt;(function(e){e[e.InvalidSymbol=1]="InvalidSymbol",e[e.InvalidNumberFormat=2]="InvalidNumberFormat",e[e.PropertyNameExpected=3]="PropertyNameExpected",e[e.ValueExpected=4]="ValueExpected",e[e.ColonExpected=5]="ColonExpected",e[e.CommaExpected=6]="CommaExpected",e[e.CloseBraceExpected=7]="CloseBraceExpected",e[e.CloseBracketExpected=8]="CloseBracketExpected",e[e.EndOfFileExpected=9]="EndOfFileExpected",e[e.InvalidCommentToken=10]="InvalidCommentToken",e[e.UnexpectedEndOfComment=11]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=12]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=13]="UnexpectedEndOfNumber",e[e.InvalidUnicode=14]="InvalidUnicode",e[e.InvalidEscapeCharacter=15]="InvalidEscapeCharacter",e[e.InvalidCharacter=16]="InvalidCharacter"})(Rt||(Rt={}));function un(e){return{getInitialState:()=>new Mt(null,null,!1,null),tokenize:(r,i)=>_n(e,r,i)}}var Nt="delimiter.bracket.json",Dt="delimiter.array.json",cn="delimiter.colon.json",ln="delimiter.comma.json",fn="keyword.json",dn="keyword.json",gn="string.value.json",pn="number.json",vn="string.key.json",mn="comment.block.json",hn="comment.line.json",B=class jt{constructor(r,i){this.parent=r,this.type=i}static pop(r){return r?r.parent:null}static push(r,i){return new jt(r,i)}static equals(r,i){if(!r&&!i)return!0;if(!r||!i)return!1;for(;r&&i;){if(r===i)return!0;if(r.type!==i.type)return!1;r=r.parent,i=i.parent}return!0}},Mt=class le{constructor(r,i,n,t){this._state=r,this.scanError=i,this.lastWasColon=n,this.parents=t}clone(){return new le(this._state,this.scanError,this.lastWasColon,this.parents)}equals(r){return r===this?!0:!r||!(r instanceof le)?!1:this.scanError===r.scanError&&this.lastWasColon===r.lastWasColon&&B.equals(this.parents,r.parents)}getStateData(){return this._state}setStateData(r){this._state=r}};function _n(e,r,i,n=0){let t=0,o=!1;switch(i.scanError){case 2:r='"'+r,t=1;break;case 1:r="/*"+r,t=2;break}const s=an(r);let u=i.lastWasColon,g=i.parents;const c={tokens:[],endState:i.clone()};for(;;){let v=n+s.getPosition(),d="";const k=s.scan();if(k===17)break;if(v===n+s.getPosition())throw new Error("Scanner did not advance, next 3 characters are: "+r.substr(s.getPosition(),3));switch(o&&(v-=t),o=t>0,k){case 1:g=B.push(g,0),d=Nt,u=!1;break;case 2:g=B.pop(g),d=Nt,u=!1;break;case 3:g=B.push(g,1),d=Dt,u=!1;break;case 4:g=B.pop(g),d=Dt,u=!1;break;case 6:d=cn,u=!0;break;case 5:d=ln,u=!1;break;case 8:case 9:d=fn,u=!1;break;case 7:d=dn,u=!1;break;case 10:const N=(g?g.type:0)===1;d=u||N?gn:vn,u=!1;break;case 11:d=pn,u=!1;break}if(e)switch(k){case 12:d=hn;break;case 13:d=mn;break}c.endState=new Mt(i.getStateData(),s.getTokenError(),u,g),c.tokens.push({startIndex:v,scopes:d})}return c}var E;function kn(){return new Promise((e,r)=>{if(!E)return r("JSON not registered!");e(E)})}var bn=class extends ct{constructor(e,r,i){super(e,r,i.onDidChange),this._disposables.push(f.editor.onWillDisposeModel(n=>{this._resetSchema(n.uri)})),this._disposables.push(f.editor.onDidChangeModelLanguage(n=>{this._resetSchema(n.model.uri)}))}_resetSchema(e){this._worker().then(r=>{r.resetSchema(e.toString())})}};function wn(e){const r=[],i=[],n=new ge(e);r.push(n),E=(...s)=>n.getLanguageServiceWorker(...s);function t(){const{languageId:s,modeConfiguration:u}=e;Ut(i),u.documentFormattingEdits&&i.push(f.languages.registerDocumentFormattingEditProvider(s,new ht(E))),u.documentRangeFormattingEdits&&i.push(f.languages.registerDocumentRangeFormattingEditProvider(s,new _t(E))),u.completionItems&&i.push(f.languages.registerCompletionItemProvider(s,new lt(E,[" ",":",'"']))),u.hovers&&i.push(f.languages.registerHoverProvider(s,new ft(E))),u.documentSymbols&&i.push(f.languages.registerDocumentSymbolProvider(s,new pt(E))),u.tokens&&i.push(f.languages.setTokensProvider(s,un(!0))),u.colors&&i.push(f.languages.registerColorProvider(s,new bt(E))),u.foldingRanges&&i.push(f.languages.registerFoldingRangeProvider(s,new wt(E))),u.diagnostics&&i.push(new bn(s,E,e)),u.selectionRanges&&i.push(f.languages.registerSelectionRangeProvider(s,new At(E)))}t(),r.push(f.languages.setLanguageConfiguration(e.languageId,An));let o=e.modeConfiguration;return e.onDidChange(s=>{s.modeConfiguration!==o&&(o=s.modeConfiguration,t())}),r.push(Ft(i)),Ft(r)}function Ft(e){return{dispose:()=>Ut(e)}}function Ut(e){for(;e.length;)e.pop().dispose()}var An={wordPattern:/(-?\d*\.\d\w*)|([^\[\{\]\}\:\"\,\s]+)/g,comments:{lineComment:"//",blockComment:["/*","*/"]},brackets:[["{","}"],["[","]"]],autoClosingPairs:[{open:"{",close:"}",notIn:["string"]},{open:"[",close:"]",notIn:["string"]},{open:'"',close:'"',notIn:["string"]}]}}}]);
