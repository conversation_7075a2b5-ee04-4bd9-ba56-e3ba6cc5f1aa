"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[933],{48898:function(w,y){var t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"};y.Z=t},48118:function(w,y,t){t.d(y,{Z:function(){return Z}});var f=t(1413),c=t(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm72-112c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48zm400-188h-59.3c-2.6 0-5 1.2-6.5 3.3L763.7 538.1l-49.9-68.8a7.92 7.92 0 00-6.5-3.3H648c-6.5 0-10.3 7.4-6.5 12.7l109.2 150.7a16.1 16.1 0 0026 0l165.8-228.7c3.8-5.3 0-12.7-6.5-12.7zm-44 306h-64.2c-5.5 0-10.6 2.9-13.6 7.5a352.2 352.2 0 01-49.8 62.2A355.92 355.92 0 01651.1 840a355 355 0 01-138.7 27.9c-48.1 0-94.8-9.4-138.7-27.9a355.92 355.92 0 01-113.3-76.3A353.06 353.06 0 01184 650.5c-18.6-43.8-28-90.5-28-138.5s9.4-94.7 28-138.5c17.9-42.4 43.6-80.5 76.4-113.2 32.8-32.7 70.9-58.4 113.3-76.3a355 355 0 01138.7-27.9c48.1 0 94.8 9.4 138.7 27.9 42.4 17.9 80.5 43.6 113.3 76.3 19 19 35.6 39.8 49.8 62.2 2.9 4.7 8.1 7.5 13.6 7.5H892c6 0 9.8-6.3 7.2-11.6C828.8 178.5 684.7 82 517.7 80 278.9 77.2 80.5 272.5 80 511.2 79.5 750.1 273.3 944 512.4 944c169.2 0 315.6-97 386.7-238.4A8 8 0 00892 694z"}}]},name:"issues-close",theme:"outlined"},g=o,P=t(91146),O=function(ee,v){return c.createElement(P.Z,(0,f.Z)((0,f.Z)({},ee),{},{ref:v,icon:g}))},Q=c.forwardRef(O),Z=Q},63783:function(w,y,t){var f=t(1413),c=t(67294),o=t(36688),g=t(91146),P=function(Z,H){return c.createElement(g.Z,(0,f.Z)((0,f.Z)({},Z),{},{ref:H,icon:o.Z}))},O=c.forwardRef(P);y.Z=O},40110:function(w,y,t){var f=t(1413),c=t(67294),o=t(509),g=t(91146),P=function(Z,H){return c.createElement(g.Z,(0,f.Z)((0,f.Z)({},Z),{},{ref:H,icon:o.Z}))},O=c.forwardRef(P);y.Z=O},184:function(w,y,t){t.d(y,{a:function(){return fe}});var f=t(4942),c=t(74165),o=t(15861),g=t(1413),P=t(97685),O=t(91),Q=t(12044),Z=t(51812),H=t(48171),ee=t(73177),v=t(21532),te=t(85265),K=t(93967),I=t.n(K),e=t(21770),h=t(8880),T=t(80334),l=t(67294),C=t(73935),F=t(89671),D=t(64847),L=function(u){return(0,f.Z)({},u.componentCls,{"&-sidebar-dragger":{width:"5px",cursor:"ew-resize",padding:"4px 0 0",borderTop:"1px solid transparent",position:"absolute",top:0,left:0,bottom:0,zIndex:100,backgroundColor:"transparent","&-min-disabled":{cursor:"w-resize"},"&-max-disabled":{cursor:"e-resize"}}})};function W(j){return(0,D.Xj)("DrawerForm",function(u){var $=(0,g.Z)((0,g.Z)({},u),{},{componentCls:".".concat(j)});return[L($)]})}var S=t(85893),_=["children","trigger","onVisibleChange","drawerProps","onFinish","submitTimeout","title","width","resize","onOpenChange","visible","open"];function fe(j){var u,$,ie=j.children,N=j.trigger,B=j.onVisibleChange,i=j.drawerProps,ne=j.onFinish,M=j.submitTimeout,E=j.title,m=j.width,x=j.resize,X=j.onOpenChange,G=j.visible,ue=j.open,b=(0,O.Z)(j,_);(0,T.ET)(!b.footer||!(i!=null&&i.footer),"DrawerForm \u662F\u4E00\u4E2A ProForm \u7684\u7279\u6B8A\u5E03\u5C40\uFF0C\u5982\u679C\u60F3\u81EA\u5B9A\u4E49\u6309\u94AE\uFF0C\u8BF7\u4F7F\u7528 submit.render \u81EA\u5B9A\u4E49\u3002");var r=l.useMemo(function(){var d,n,s,a={onResize:function(){},maxWidth:(0,Q.j)()?window.innerWidth*.8:void 0,minWidth:300};return typeof x=="boolean"?x?a:{}:(0,Z.Y)({onResize:(d=x==null?void 0:x.onResize)!==null&&d!==void 0?d:a.onResize,maxWidth:(n=x==null?void 0:x.maxWidth)!==null&&n!==void 0?n:a.maxWidth,minWidth:(s=x==null?void 0:x.minWidth)!==null&&s!==void 0?s:a.minWidth})},[x]),Y=(0,l.useContext)(v.ZP.ConfigContext),xe=Y.getPrefixCls("pro-form-drawer"),be=W(xe),q=be.wrapSSR,pe=be.hashId,ge=function(n){return"".concat(xe,"-").concat(n," ").concat(pe)},Ie=(0,l.useState)([]),Be=(0,P.Z)(Ie,2),Re=Be[1],Le=(0,l.useState)(!1),Te=(0,P.Z)(Le,2),Ce=Te[0],J=Te[1],z=(0,l.useState)(!1),V=(0,P.Z)(z,2),je=V[0],ye=V[1],Fe=(0,l.useState)(m||(x?r==null?void 0:r.minWidth:800)),de=(0,P.Z)(Fe,2),re=de[0],se=de[1],Oe=(0,e.Z)(!!G,{value:ue||G,onChange:X||B}),ve=(0,P.Z)(Oe,2),le=ve[0],U=ve[1],he=(0,l.useRef)(null),De=(0,l.useCallback)(function(d){he.current===null&&d&&Re([]),he.current=d},[]),ce=(0,l.useRef)(),$e=(0,l.useCallback)(function(){var d,n,s,a=(d=(n=(s=b.formRef)===null||s===void 0?void 0:s.current)!==null&&n!==void 0?n:b.form)!==null&&d!==void 0?d:ce.current;a&&i!==null&&i!==void 0&&i.destroyOnClose&&a.resetFields()},[i==null?void 0:i.destroyOnClose,b.form,b.formRef]);(0,l.useEffect)(function(){le&&(ue||G)&&(X==null||X(!0),B==null||B(!0)),je&&se(r==null?void 0:r.minWidth)},[G,le,je]),(0,l.useImperativeHandle)(b.formRef,function(){return ce.current},[ce.current]);var Me=(0,l.useMemo)(function(){return N?l.cloneElement(N,(0,g.Z)((0,g.Z)({key:"trigger"},N.props),{},{onClick:function(){var d=(0,o.Z)((0,c.Z)().mark(function s(a){var A,R;return(0,c.Z)().wrap(function(p){for(;;)switch(p.prev=p.next){case 0:U(!le),ye(!Object.keys(r)),(A=N.props)===null||A===void 0||(R=A.onClick)===null||R===void 0||R.call(A,a);case 3:case"end":return p.stop()}},s)}));function n(s){return d.apply(this,arguments)}return n}()})):null},[U,N,le,ye,je]),Ae=(0,l.useMemo)(function(){var d,n,s,a,A;return b.submitter===!1?!1:(0,h.T)({searchConfig:{submitText:(d=(n=Y.locale)===null||n===void 0||(n=n.Modal)===null||n===void 0?void 0:n.okText)!==null&&d!==void 0?d:"\u786E\u8BA4",resetText:(s=(a=Y.locale)===null||a===void 0||(a=a.Modal)===null||a===void 0?void 0:a.cancelText)!==null&&s!==void 0?s:"\u53D6\u6D88"},resetButtonProps:{preventDefault:!0,disabled:M?Ce:void 0,onClick:function(Se){var p;U(!1),i==null||(p=i.onClose)===null||p===void 0||p.call(i,Se)}}},(A=b.submitter)!==null&&A!==void 0?A:{})},[b.submitter,(u=Y.locale)===null||u===void 0||(u=u.Modal)===null||u===void 0?void 0:u.okText,($=Y.locale)===null||$===void 0||($=$.Modal)===null||$===void 0?void 0:$.cancelText,M,Ce,U,i]),Pe=(0,l.useCallback)(function(d,n){return(0,S.jsxs)(S.Fragment,{children:[d,he.current&&n?(0,S.jsx)(l.Fragment,{children:(0,C.createPortal)(n,he.current)},"submitter"):n]})},[]),ze=(0,H.J)(function(){var d=(0,o.Z)((0,c.Z)().mark(function n(s){var a,A,R;return(0,c.Z)().wrap(function(p){for(;;)switch(p.prev=p.next){case 0:return a=ne==null?void 0:ne(s),M&&a instanceof Promise&&(J(!0),A=setTimeout(function(){return J(!1)},M),a.finally(function(){clearTimeout(A),J(!1)})),p.next=4,a;case 4:return R=p.sent,R&&U(!1),p.abrupt("return",R);case 7:case"end":return p.stop()}},n)}));return function(n){return d.apply(this,arguments)}}()),We=(0,ee.X)(le,B),me=(0,l.useCallback)(function(d){var n,s,a=(document.body.offsetWidth||1e3)-(d.clientX-document.body.offsetLeft),A=(n=r==null?void 0:r.minWidth)!==null&&n!==void 0?n:m||800,R=(s=r==null?void 0:r.maxWidth)!==null&&s!==void 0?s:window.innerWidth*.8;if(a<A){se(A);return}if(a>R){se(R);return}se(a)},[r==null?void 0:r.maxWidth,r==null?void 0:r.minWidth,m]),Ze=(0,l.useCallback)(function(){document.removeEventListener("mousemove",me),document.removeEventListener("mouseup",Ze)},[me]);return q((0,S.jsxs)(S.Fragment,{children:[(0,S.jsxs)(te.Z,(0,g.Z)((0,g.Z)((0,g.Z)({title:E,width:re},i),We),{},{afterOpenChange:function(n){var s;n||$e(),i==null||(s=i.afterOpenChange)===null||s===void 0||s.call(i,n)},onClose:function(n){var s;M&&Ce||(U(!1),i==null||(s=i.onClose)===null||s===void 0||s.call(i,n))},footer:b.submitter!==!1&&(0,S.jsx)("div",{ref:De,style:{display:"flex",justifyContent:"flex-end"}}),children:[x?(0,S.jsx)("div",{className:I()(ge("sidebar-dragger"),pe,(0,f.Z)((0,f.Z)({},ge("sidebar-dragger-min-disabled"),re===(r==null?void 0:r.minWidth)),ge("sidebar-dragger-max-disabled"),re===(r==null?void 0:r.maxWidth))),onMouseDown:function(n){var s;r==null||(s=r.onResize)===null||s===void 0||s.call(r),n.stopPropagation(),n.preventDefault(),document.addEventListener("mousemove",me),document.addEventListener("mouseup",Ze),ye(!0)}}):null,(0,S.jsx)(S.Fragment,{children:(0,S.jsx)(F.I,(0,g.Z)((0,g.Z)({formComponentType:"DrawerForm",layout:"vertical"},b),{},{formRef:ce,onInit:function(n,s){var a;b.formRef&&(b.formRef.current=s),b==null||(a=b.onInit)===null||a===void 0||a.call(b,n,s),ce.current=s},submitter:Ae,onFinish:function(){var d=(0,o.Z)((0,c.Z)().mark(function n(s){var a;return(0,c.Z)().wrap(function(R){for(;;)switch(R.prev=R.next){case 0:return R.next=2,ze(s);case 2:return a=R.sent,R.abrupt("return",a);case 4:case"end":return R.stop()}},n)}));return function(n){return d.apply(this,arguments)}}(),contentRender:Pe,children:ie}))})]})),Me]}))}},4870:function(w,y,t){t.r(y),t.d(y,{default:function(){return Ce}});var f=t(5574),c=t.n(f),o=t(67294),g=t(2453),P=t(55102),O=t(83622),Q=t(43425),Z=t(1413),H={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M931.4 498.9L94.9 79.5c-3.4-1.7-7.3-2.1-11-1.2a15.99 15.99 0 00-11.7 19.3l86.2 352.2c1.3 5.3 5.2 9.6 10.4 11.3l147.7 50.7-147.6 50.7c-5.2 1.8-9.1 6-10.3 11.3L72.2 926.5c-.9 3.7-.5 7.6 1.2 10.9 3.9 7.9 13.5 11.1 21.5 7.2l836.5-417c3.1-1.5 5.6-4.1 7.2-7.1 3.9-8 .7-17.6-7.2-21.6zM170.8 826.3l50.3-205.6 295.2-101.3c2.3-.8 4.2-2.6 5-5 1.4-4.2-.8-8.7-5-10.2L221.1 403 171 198.2l628 314.9-628.2 313.2z"}}]},name:"send",theme:"outlined"},ee=H,v=t(91146),te=function(z,V){return o.createElement(v.Z,(0,Z.Z)((0,Z.Z)({},z),{},{ref:V,icon:ee}))},K=o.forwardRef(te),I=K,e=t(85893),h=function(){var z=(0,o.useState)([]),V=c()(z,2),je=V[0],ye=V[1],Fe=(0,o.useState)(""),de=c()(Fe,2),re=de[0],se=de[1],Oe=function(){if(re.trim()===""){g.ZP.warning("\u8BF7\u8F93\u5165\u6D88\u606F\u5185\u5BB9\uFF01");return}se("")};return(0,e.jsxs)("div",{style:{display:"flex",flexDirection:"column",height:"80vh"},children:[(0,e.jsxs)("div",{style:{flex:1,overflowY:"auto",paddingBottom:"1.5rem",borderBottom:"1px solid #262626"},children:[(0,e.jsxs)("div",{style:{display:"flex",justifyContent:"space-between"},children:[(0,e.jsx)("div",{style:{display:"flex",alignItems:"center"},children:(0,e.jsx)("h1",{children:"\u667A\u80FD\u52A9\u624B"})}),(0,e.jsx)("div",{style:{display:"flex"},children:(0,e.jsx)(Q.Z,{})})]}),(0,e.jsxs)("p",{style:{color:"#A6AAAE",margin:0},children:["\u4F60\u53EF\u4EE5\u8BD5\u7740\u8BA9\u6211\uFF1A",(0,e.jsx)("br",{}),"\u5728AI\u667A\u80FD\u52A9\u624B\u9875\u9762\uFF0C\u901A\u8FC7\u4F7F\u7528\u64B0\u5199\u529F\u80FD\uFF0C\u60A8\u53EF\u8F7B\u677E\u8F93\u5165\u6240\u9700\u8F6C\u6362\u7684CUDA\u4EE3\u7801\uFF0C\u5E76\u901A\u8FC7\u5BF9\u8BDD\u7684\u65B9\u5F0F\u5BF9\u8F93\u51FA\u7684\u6587\u672C\u8FDB\u884C\u8C03\u6574\uFF0C\u5B9E\u73B0\u4FBF\u6377\u800C\u667A\u80FD\u7684\u4EE3\u7801\u7F16\u5199\u4F53\u9A8C..."]})]}),(0,e.jsx)("div",{}),(0,e.jsxs)("div",{style:{display:"flex",flexDirection:"row",flexWrap:"wrap",gap:" 0.5rem"},children:[(0,e.jsx)("button",{style:{backgroundColor:"#200033",color:"#9946B9",borderRadius:"0.5rem",border:"none"},children:"\u201CcudaSetDevice()\u201D API \u5982\u4F55\u8F6C\u8BD1\u4E3A SYCL \u8BED\u8A00 \uFF1F"}),(0,e.jsx)("button",{style:{backgroundColor:"#200033",color:"#9946B9",borderRadius:"0.5rem",border:"none"},children:"\u201CVectorAdd()\u201D\u65B9\u6CD5\u793A\u4F8B?"}),(0,e.jsx)("button",{style:{backgroundColor:"#200033",color:"#9946B9",borderRadius:"0.5rem",border:"none"},children:"\u201CVectorAdd()\u201D API \u5982\u4F55\u8F6C\u8BD1\u4E3A SYCL \u8BED\u8A00 \uFF1F"})]}),(0,e.jsxs)("div",{style:{display:"flex",paddingTop:"16px"},children:[(0,e.jsx)(P.Z,{placeholder:"\u8F93\u5165\u60A8\u7684\u95EE\u9898 ...",value:re,onChange:function(le){return se(le.target.value)},style:{flex:1,marginRight:"8px"}}),(0,e.jsxs)(O.ZP,{type:"primary",onClick:Oe,children:[(0,e.jsx)(I,{}),"\u53D1\u9001"]})]})]})},T=h,l=t(53373),C=t(48898),F=function(z,V){return o.createElement(v.Z,(0,Z.Z)((0,Z.Z)({},z),{},{ref:V,icon:C.Z}))},D=o.forwardRef(F),L=D,W=t(40110),S={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M893.3 293.3L730.7 130.7c-7.5-7.5-16.7-13-26.7-16V112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V338.5c0-17-6.7-33.2-18.7-45.2zM384 184h256v104H384V184zm456 656H184V184h136v136c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V205.8l136 136V840zM512 442c-79.5 0-144 64.5-144 144s64.5 144 144 144 144-64.5 144-144-64.5-144-144-144zm0 224c-44.2 0-80-35.8-80-80s35.8-80 80-80 80 35.8 80 80-35.8 80-80 80z"}}]},name:"save",theme:"outlined"},_=S,fe=function(z,V){return o.createElement(v.Z,(0,Z.Z)((0,Z.Z)({},z),{},{ref:V,icon:_}))},j=o.forwardRef(fe),u=j,$={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M140 188h584v164h76V144c0-17.7-14.3-32-32-32H96c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h544v-76H140V188z"}},{tag:"path",attrs:{d:"M414.3 256h-60.6c-3.4 0-6.4 2.2-7.6 5.4L219 629.4c-.3.8-.4 1.7-.4 2.6 0 4.4 3.6 8 8 8h55.1c3.4 0 6.4-2.2 7.6-5.4L322 540h196.2L422 261.4a8.42 8.42 0 00-7.7-5.4zm12.4 228h-85.5L384 360.2 426.7 484zM936 528H800v-93c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v93H592c-13.3 0-24 10.7-24 24v176c0 13.3 10.7 24 24 24h136v152c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V752h136c13.3 0 24-10.7 24-24V552c0-13.3-10.7-24-24-24zM728 680h-88v-80h88v80zm160 0h-88v-80h88v80z"}}]},name:"translation",theme:"outlined"},ie=$,N=function(z,V){return o.createElement(v.Z,(0,Z.Z)((0,Z.Z)({},z),{},{ref:V,icon:ie}))},B=o.forwardRef(N),i=B,ne=t(63783),M=t(48118),E=t(84017),m=t(184),x=t(88372),X=t(5966),G=t(93410),ue=t(47019),b=t(64218),r=t(71230),Y=t(15746),xe=t(4393),be=t(78367),q=t(83062),pe=t(11941),ge=t(28846),Ie=(0,ge.kc)(function(J){var z=J.token;return{title:{marginBottom:"16px",color:z.colorTextHeading,fontWeight:"500",fontSize:"16px"},subtitle:{fontWeight:"100",fontSize:"14px"},logo:{width:"40px",height:"40px",float:"left",borderRadius:"50%",marginRight:"20px"}}}),Be=Ie,Re=t(33577),Le={width:"100%",height:100,overflow:"auto",boxShadow:"0 0 0 1px #1677ff",scrollbarWidth:"thin",scrollbarGutter:"stable"},Te={width:"100%",height:1e3},Ce=function(){var J=(0,o.useState)(!0),z=c()(J,2),V=z[0],je=z[1],ye=[{value:"cpp",label:"cpp"},{value:"c#",label:"c#"},{value:"typescript",label:"typescript"}],Fe=function(k){var ae=k.icon,oe=k.text;return(0,e.jsxs)("span",{children:[o.createElement(ae,{style:{marginInlineEnd:8}}),oe]})},de=function(k){var ae=Re.editor.getModels()[k],oe=Re.editor.getEditors()[k];ae&&oe&&oe.trigger("keyboard","editor.action.startFindReplaceAction",{})},re=function(k,ae){var oe=new Blob([k],{type:"text/plain;charset=utf-8"}),Ee=document.createElement("a");Ee.href=URL.createObjectURL(oe),Ee.download=ae,Ee.click()},se=ue.Z.useForm(),Oe=c()(se,1),ve=Oe[0],le=Be(),U=le.styles,he=(0,o.useState)(`/*#------------------- 
 * Please,
 * Input CUDA Source Code @ here....  
 *#-------------------*/
`),De=c()(he,2),ce=De[0],$e=De[1],Me=(0,o.useState)(`/*#------------------- 
 * Look,
 * Output SYCL Source Code @ here....  
 *#-------------------*/
`),Ae=c()(Me,2),Pe=Ae[0],ze=Ae[1],We=(0,o.useState)([{key:"1",label:"new"}]),me=c()(We,2),Ze=me[0],d=me[1],n=(0,o.useState)([{key:"1",label:"new"}]),s=c()(n,2),a=s[0],A=s[1],R=function(k){var ae=k.file;if(ae instanceof File){var oe=new FileReader;oe.onload=function(Ee){var we;$e((we=Ee.target)===null||we===void 0?void 0:we.result),d([{key:"1",label:ae.name}])},oe.readAsText(ae)}else console.error("The selected file is not a Blob.")},Se=function(){var k=`#include <sycl/ sycl.hpp >
#include < dpct / dpct.hpp >
#include < stdio.h >
#define VECTOR_SIZE 256

void VectorAddKernel(float * A, float * B, float * C, 
const sycl:: nd_item< 3 > & item_ct1) 
{
A[item_ct1.get_local_id(2)] = item_ct1.get_local_id(2) + 1.0f; 
B[item_ct1.get_local_id(2)] = item_ct1.get_local_id(2) + 1.0f; 
C[item_ct1.get_local_id(2)] =
A[item_ct1.get_local_id(2)] + B[item_ct1.get_local_id(2)];
}
int main() {
sycl::device dev_ct1; 
sycl::queue q_ct1(dev_ct1, 
sycl:: property_list{ sycl:: property:: queue:: in_order() }); 
float * d_A, * d_B, * d_C; 

d_A = sycl:: malloc_device < float > (VECTOR_SIZE, q_ct1); 
d_B = sycl:: malloc_device < float > (VECTOR_SIZE, q_ct1); 
d_C = sycl:: malloc_device < float > (VECTOR_SIZE, q_ct1); 

q_ct1.parallel_for(sycl:: nd_range < 3 > (sycl:: range < 3 > (1, 1, VECTOR_SIZE), 
sycl:: range < 3 > (1, 1, VECTOR_SIZE)), 
[=](sycl:: nd_item < 3 > item_ct1) {
VectorAddKernel(d_A, d_B, d_C, item_ct1); 
}); 

float Result[VECTOR_SIZE] = {}; 
q_ct1.memcpy(Result, d_C, VECTOR_SIZE * sizeof(float)).wait(); 

sycl:: free(d_A, q_ct1); 
sycl:: free(d_B, q_ct1); 
sycl:: free(d_C, q_ct1); 

for (int i = 0; i < VECTOR_SIZE; i++) {

if (i % 16 == 0) {

printf(""); 
} 
printf("%f ", Result[i]); 
} 

return 0; 
}
`;ze(k),A(Ze)};return(0,e.jsxs)(E._z,{header:{breadcrumb:{}},children:[(0,e.jsx)(m.a,{title:"\u4F60\u597D\uFF0C\u6211\u662F\u4EE3\u7801\u52A9\u624B",resize:{onResize:function(){console.log("resize!")},maxWidth:window.innerWidth*.8,minWidth:400},form:ve,trigger:(0,e.jsx)(b.Z,{offsetBottom:20,target:function(){return window},children:(0,e.jsx)("a",{style:{position:"fixed",right:"7vw",bottom:"7vh",zIndex:1e3},children:(0,e.jsx)("img",{src:t(13235),width:130})})}),autoFocusFirstInput:!0,drawerProps:{destroyOnClose:!1},submitter:!1,children:(0,e.jsx)(T,{})}),(0,e.jsxs)(x.f,{children:[(0,e.jsxs)(r.Z,{gutter:24,children:[(0,e.jsx)(Y.Z,{xl:12,lg:24,md:24,sm:24,xs:24,children:(0,e.jsx)(o.Suspense,{fallback:null,children:(0,e.jsxs)(xe.Z,{bordered:!1,title:[(0,e.jsx)("br",{}),(0,e.jsx)("img",{src:t(22369),className:U.logo}),(0,e.jsxs)("div",{className:U.title,children:["CUDA Code - Input @ HERE",(0,e.jsx)("br",{}),(0,e.jsx)("span",{className:U.subtitle,children:"Cuda Programming Model [CUDA 11.7]"})]})],style:{width:"100%"},children:[(0,e.jsxs)(r.Z,{children:[(0,e.jsxs)(Y.Z,{span:2,children:[(0,e.jsxs)(r.Z,{gutter:[16,16],children:[(0,e.jsx)(be.Z,{beforeUpload:function(){return!1},onChange:R,showUploadList:!1,children:(0,e.jsx)(q.Z,{title:"\u6253\u5F00\u6587\u4EF6",children:(0,e.jsx)(O.ZP,{shape:"circle",icon:(0,e.jsx)(L,{})})})}),(0,e.jsx)("br",{})," ",(0,e.jsx)("br",{})]}),(0,e.jsxs)(r.Z,{gutter:[16,16],children:[(0,e.jsx)(q.Z,{title:"\u641C\u7D22",children:(0,e.jsx)(O.ZP,{shape:"circle",icon:(0,e.jsx)(W.Z,{}),onClick:function(){return de(0)}})}),(0,e.jsx)("br",{})," ",(0,e.jsx)("br",{})]}),(0,e.jsxs)(r.Z,{gutter:[16,16],children:[(0,e.jsx)(q.Z,{title:"\u4FDD\u5B58",children:(0,e.jsx)(O.ZP,{shape:"circle",icon:(0,e.jsx)(u,{}),onClick:function(){return re(ce,"cuda_code.cu")}})}),(0,e.jsx)("br",{})," ",(0,e.jsx)("br",{})]}),(0,e.jsxs)(r.Z,{gutter:[16,16],children:[(0,e.jsx)(q.Z,{title:"\u4E00\u952E\u8F6C\u8BD1",children:(0,e.jsx)(O.ZP,{shape:"circle",icon:(0,e.jsx)(i,{}),onClick:Se})}),(0,e.jsx)("br",{})," ",(0,e.jsx)("br",{})]})]}),(0,e.jsxs)(Y.Z,{span:22,children:[(0,e.jsx)(pe.Z,{defaultActiveKey:"1",items:Ze}),(0,e.jsx)(l.Z,{value:ce,isREadOnly:!1,height:400,language:"cpp"})]})]}),(0,e.jsx)(X.Z,{placeholder:"Command @ here",style:{marginTop:"24px"}})]})})}),(0,e.jsx)(Y.Z,{xl:12,lg:24,md:24,sm:24,xs:24,children:(0,e.jsx)(o.Suspense,{fallback:null,children:(0,e.jsxs)(xe.Z,{bordered:!1,title:[(0,e.jsx)("br",{}),(0,e.jsx)("img",{src:t(36806),className:U.logo}),(0,e.jsxs)("div",{className:U.title,children:["SYCL Code - Output @ HERE",(0,e.jsx)("br",{}),(0,e.jsx)("span",{className:U.subtitle,children:"SYCL Programming Model [Standard 2020 Revision 7]"})]})],style:{width:"100%"},children:[(0,e.jsxs)(r.Z,{children:[(0,e.jsxs)(Y.Z,{span:2,children:[(0,e.jsxs)(r.Z,{gutter:[16,16],children:[(0,e.jsx)(q.Z,{title:"\u6559\u7A0B",children:(0,e.jsx)(O.ZP,{shape:"circle",icon:(0,e.jsx)(ne.Z,{})})}),(0,e.jsx)("br",{})," ",(0,e.jsx)("br",{})]}),(0,e.jsxs)(r.Z,{gutter:[16,16],children:[(0,e.jsx)(q.Z,{title:"\u641C\u7D22",children:(0,e.jsx)(O.ZP,{shape:"circle",icon:(0,e.jsx)(W.Z,{}),onClick:function(){return de(1)}})}),(0,e.jsx)("br",{})," ",(0,e.jsx)("br",{})]}),(0,e.jsxs)(r.Z,{gutter:[16,16],children:[(0,e.jsx)(q.Z,{title:"\u4FDD\u5B58",children:(0,e.jsx)(O.ZP,{shape:"circle",icon:(0,e.jsx)(u,{}),onClick:function(){return re(Pe,"sycl_code.cpp")}})}),(0,e.jsx)("br",{})," ",(0,e.jsx)("br",{})]}),(0,e.jsxs)(r.Z,{gutter:[16,16],children:[(0,e.jsx)(q.Z,{title:"\u6821\u9A8C",children:(0,e.jsx)(O.ZP,{shape:"circle",icon:(0,e.jsx)(M.Z,{})})}),(0,e.jsx)("br",{})," ",(0,e.jsx)("br",{})]})]}),(0,e.jsxs)(Y.Z,{span:22,children:[(0,e.jsx)(pe.Z,{defaultActiveKey:"1",items:a}),(0,e.jsx)(l.Z,{value:Pe,isREadOnly:!1,height:400,language:"cpp"})]})]}),(0,e.jsx)(X.Z,{placeholder:"Command @ here",style:{marginTop:"24px"}})]})})})]}),(0,e.jsx)("br",{}),(0,e.jsx)("br",{}),(0,e.jsxs)(G.Z,{tabs:{type:"card"},children:[(0,e.jsx)(G.Z.TabPane,{tab:"CUDA Terminal",children:(0,e.jsx)("iframe",{src:"http://172.31.166.162:63080/",style:{height:"400px",width:"100%"},frameBorder:0})},"tab1"),(0,e.jsx)(G.Z.TabPane,{tab:"SYCL Terminal",children:(0,e.jsx)("iframe",{src:"http://172.30.232.22:61222/",style:{height:"400px",width:"100%"},frameBorder:0})},"tab2"),(0,e.jsx)(G.Z.TabPane,{tab:"Other Settings",children:(0,e.jsx)("iframe",{src:"http://127.0.0.1:63222/",style:{height:"400px",width:"100%"},frameBorder:0})},"tab3")]})]})]})}},99134:function(w,y,t){var f=t(67294);const c=(0,f.createContext)({});y.Z=c},21584:function(w,y,t){var f=t(67294),c=t(93967),o=t.n(c),g=t(53124),P=t(99134),O=t(6999),Q=function(v,te){var K={};for(var I in v)Object.prototype.hasOwnProperty.call(v,I)&&te.indexOf(I)<0&&(K[I]=v[I]);if(v!=null&&typeof Object.getOwnPropertySymbols=="function")for(var e=0,I=Object.getOwnPropertySymbols(v);e<I.length;e++)te.indexOf(I[e])<0&&Object.prototype.propertyIsEnumerable.call(v,I[e])&&(K[I[e]]=v[I[e]]);return K};function Z(v){return typeof v=="number"?`${v} ${v} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(v)?`0 0 ${v}`:v}const H=["xs","sm","md","lg","xl","xxl"],ee=f.forwardRef((v,te)=>{const{getPrefixCls:K,direction:I}=f.useContext(g.E_),{gutter:e,wrap:h}=f.useContext(P.Z),{prefixCls:T,span:l,order:C,offset:F,push:D,pull:L,className:W,children:S,flex:_,style:fe}=v,j=Q(v,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),u=K("col",T),[$,ie,N]=(0,O.cG)(u),B={};let i={};H.forEach(E=>{let m={};const x=v[E];typeof x=="number"?m.span=x:typeof x=="object"&&(m=x||{}),delete j[E],i=Object.assign(Object.assign({},i),{[`${u}-${E}-${m.span}`]:m.span!==void 0,[`${u}-${E}-order-${m.order}`]:m.order||m.order===0,[`${u}-${E}-offset-${m.offset}`]:m.offset||m.offset===0,[`${u}-${E}-push-${m.push}`]:m.push||m.push===0,[`${u}-${E}-pull-${m.pull}`]:m.pull||m.pull===0,[`${u}-rtl`]:I==="rtl"}),m.flex&&(i[`${u}-${E}-flex`]=!0,B[`--${u}-${E}-flex`]=Z(m.flex))});const ne=o()(u,{[`${u}-${l}`]:l!==void 0,[`${u}-order-${C}`]:C,[`${u}-offset-${F}`]:F,[`${u}-push-${D}`]:D,[`${u}-pull-${L}`]:L},W,i,ie,N),M={};if(e&&e[0]>0){const E=e[0]/2;M.paddingLeft=E,M.paddingRight=E}return _&&(M.flex=Z(_),h===!1&&!M.minWidth&&(M.minWidth=0)),$(f.createElement("div",Object.assign({},j,{style:Object.assign(Object.assign(Object.assign({},M),fe),B),className:ne,ref:te}),S))});y.Z=ee},17621:function(w,y,t){t.d(y,{Z:function(){return e}});var f=t(67294),c=t(93967),o=t.n(c),g=t(74443),P=t(53124),O=t(25378);function Q(h,T){const l=[void 0,void 0],C=Array.isArray(h)?h:[h,void 0],F=T||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return C.forEach((D,L)=>{if(typeof D=="object"&&D!==null)for(let W=0;W<g.c4.length;W++){const S=g.c4[W];if(F[S]&&D[S]!==void 0){l[L]=D[S];break}}else l[L]=D}),l}var Z=t(99134),H=t(6999),ee=function(h,T){var l={};for(var C in h)Object.prototype.hasOwnProperty.call(h,C)&&T.indexOf(C)<0&&(l[C]=h[C]);if(h!=null&&typeof Object.getOwnPropertySymbols=="function")for(var F=0,C=Object.getOwnPropertySymbols(h);F<C.length;F++)T.indexOf(C[F])<0&&Object.prototype.propertyIsEnumerable.call(h,C[F])&&(l[C[F]]=h[C[F]]);return l};const v=null,te=null;function K(h,T){const[l,C]=f.useState(typeof h=="string"?h:""),F=()=>{if(typeof h=="string"&&C(h),typeof h=="object")for(let D=0;D<g.c4.length;D++){const L=g.c4[D];if(!T||!T[L])continue;const W=h[L];if(W!==void 0){C(W);return}}};return f.useEffect(()=>{F()},[JSON.stringify(h),T]),l}var e=f.forwardRef((h,T)=>{const{prefixCls:l,justify:C,align:F,className:D,style:L,children:W,gutter:S=0,wrap:_}=h,fe=ee(h,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:j,direction:u}=f.useContext(P.E_),$=(0,O.Z)(!0,null),ie=K(F,$),N=K(C,$),B=j("row",l),[i,ne,M]=(0,H.VM)(B),E=Q(S,$),m=o()(B,{[`${B}-no-wrap`]:_===!1,[`${B}-${N}`]:N,[`${B}-${ie}`]:ie,[`${B}-rtl`]:u==="rtl"},D,ne,M),x={},X=E[0]!=null&&E[0]>0?E[0]/-2:void 0;X&&(x.marginLeft=X,x.marginRight=X);const[G,ue]=E;x.rowGap=ue;const b=f.useMemo(()=>({gutter:[G,ue],wrap:_}),[G,ue,_]);return i(f.createElement(Z.Z.Provider,{value:b},f.createElement("div",Object.assign({},fe,{className:m,style:Object.assign(Object.assign({},x),L),ref:T}),W)))})},13235:function(w,y,t){w.exports=t.p+"static/ai-robot.61f54a4a.png"},22369:function(w,y,t){w.exports=t.p+"static/NVLOGO.31a697e1.png"},36806:function(w,y,t){w.exports=t.p+"static/SYCL_LOGO_200x200.224753ca.png"}}]);
