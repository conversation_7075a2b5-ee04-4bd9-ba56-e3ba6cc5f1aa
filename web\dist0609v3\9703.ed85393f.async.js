"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9703],{40561:function(Le,ye,d){d.d(ye,{ZP:function(){return fe},Yk:function(){return z},TM:function(){return H}});var $=d(11568),k=d(63185),A=d(14747),xe=d(33507),Ke=d(83262),Z=d(83559);const D=({treeCls:l,treeNodeCls:s,directoryNodeSelectedBg:y,directoryNodeSelectedColor:M,motionDurationMid:I,borderRadius:n,controlItemBgHover:t})=>({[`${l}${l}-directory ${s}`]:{[`${l}-node-content-wrapper`]:{position:"static",[`> *:not(${l}-drop-indicator)`]:{position:"relative"},"&:hover":{background:"transparent"},"&:before":{position:"absolute",inset:0,transition:`background-color ${I}`,content:'""',borderRadius:n},"&:hover:before":{background:t}},[`${l}-switcher, ${l}-checkbox, ${l}-draggable-icon`]:{zIndex:1},"&-selected":{[`${l}-switcher, ${l}-draggable-icon`]:{color:M},[`${l}-node-content-wrapper`]:{color:M,background:"transparent","&:before, &:hover:before":{background:y}}}}}),Q=new $.E4("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),ie=(l,s)=>({[`.${l}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${s.motionDurationSlow}`}}}),f=(l,s)=>({[`.${l}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:s.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${(0,$.bf)(s.lineWidthBold)} solid ${s.colorPrimary}`,borderRadius:"50%",content:'""'}}}),oe=(l,s)=>{const{treeCls:y,treeNodeCls:M,treeNodePadding:I,titleHeight:n,indentSize:t,nodeSelectedBg:v,nodeHoverBg:m,colorTextQuaternary:p,controlItemBgActiveDisabled:C}=s;return{[y]:Object.assign(Object.assign({},(0,A.Wf)(s)),{background:s.colorBgContainer,borderRadius:s.borderRadius,transition:`background-color ${s.motionDurationSlow}`,"&-rtl":{direction:"rtl"},[`&${y}-rtl ${y}-switcher_close ${y}-switcher-icon svg`]:{transform:"rotate(90deg)"},[`&-focused:not(:hover):not(${y}-active-focused)`]:Object.assign({},(0,A.oN)(s)),[`${y}-list-holder-inner`]:{alignItems:"flex-start"},[`&${y}-block-node`]:{[`${y}-list-holder-inner`]:{alignItems:"stretch",[`${y}-node-content-wrapper`]:{flex:"auto"},[`${M}.dragging:after`]:{position:"absolute",inset:0,border:`1px solid ${s.colorPrimary}`,opacity:0,animationName:Q,animationDuration:s.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none",borderRadius:s.borderRadius}}},[M]:{display:"flex",alignItems:"flex-start",marginBottom:I,lineHeight:(0,$.bf)(n),position:"relative","&:before":{content:'""',position:"absolute",zIndex:1,insetInlineStart:0,width:"100%",top:"100%",height:I},[`&-disabled ${y}-node-content-wrapper`]:{color:s.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}},[`${y}-checkbox-disabled + ${y}-node-selected,&${M}-disabled${M}-selected ${y}-node-content-wrapper`]:{backgroundColor:C},[`${y}-checkbox-disabled`]:{pointerEvents:"unset"},[`&:not(${M}-disabled)`]:{[`${y}-node-content-wrapper`]:{"&:hover":{color:s.nodeHoverColor}}},[`&-active ${y}-node-content-wrapper`]:{background:s.controlItemBgHover},[`&:not(${M}-disabled).filter-node ${y}-title`]:{color:s.colorPrimary,fontWeight:500},"&-draggable":{cursor:"grab",[`${y}-draggable-icon`]:{flexShrink:0,width:n,textAlign:"center",visibility:"visible",color:p},[`&${M}-disabled ${y}-draggable-icon`]:{visibility:"hidden"}}},[`${y}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:t}},[`${y}-draggable-icon`]:{visibility:"hidden"},[`${y}-switcher, ${y}-checkbox`]:{marginInlineEnd:s.calc(s.calc(n).sub(s.controlInteractiveSize)).div(2).equal()},[`${y}-switcher`]:Object.assign(Object.assign({},ie(l,s)),{position:"relative",flex:"none",alignSelf:"stretch",width:n,textAlign:"center",cursor:"pointer",userSelect:"none",transition:`all ${s.motionDurationSlow}`,"&-noop":{cursor:"unset"},"&:before":{pointerEvents:"none",content:'""',width:n,height:n,position:"absolute",left:{_skip_check_:!0,value:0},top:0,borderRadius:s.borderRadius,transition:`all ${s.motionDurationSlow}`},[`&:not(${y}-switcher-noop):hover:before`]:{backgroundColor:s.colorBgTextHover},[`&_close ${y}-switcher-icon svg`]:{transform:"rotate(-90deg)"},"&-loading-icon":{color:s.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:s.calc(n).div(2).equal(),bottom:s.calc(I).mul(-1).equal(),marginInlineStart:-1,borderInlineEnd:`1px solid ${s.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:s.calc(s.calc(n).div(2).equal()).mul(.8).equal(),height:s.calc(n).div(2).equal(),borderBottom:`1px solid ${s.colorBorder}`,content:'""'}}}),[`${y}-node-content-wrapper`]:Object.assign(Object.assign({position:"relative",minHeight:n,paddingBlock:0,paddingInline:s.paddingXS,background:"transparent",borderRadius:s.borderRadius,cursor:"pointer",transition:`all ${s.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`},f(l,s)),{"&:hover":{backgroundColor:m},[`&${y}-node-selected`]:{color:s.nodeSelectedColor,backgroundColor:v},[`${y}-iconEle`]:{display:"inline-block",width:n,height:n,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}}),[`${y}-unselectable ${y}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${M}.drop-container > [draggable]`]:{boxShadow:`0 0 0 2px ${s.colorPrimary}`},"&-show-line":{[`${y}-indent-unit`]:{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:s.calc(n).div(2).equal(),bottom:s.calc(I).mul(-1).equal(),borderInlineEnd:`1px solid ${s.colorBorder}`,content:'""'},"&-end:before":{display:"none"}},[`${y}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${M}-leaf-last ${y}-switcher-leaf-line:before`]:{top:"auto !important",bottom:"auto !important",height:`${(0,$.bf)(s.calc(n).div(2).equal())} !important`}})}},z=(l,s,y=!0)=>{const M=`.${l}`,I=`${M}-treenode`,n=s.calc(s.paddingXS).div(2).equal(),t=(0,Ke.IX)(s,{treeCls:M,treeNodeCls:I,treeNodePadding:n});return[oe(l,t),y&&D(t)].filter(Boolean)},H=l=>{const{controlHeightSM:s,controlItemBgHover:y,controlItemBgActive:M}=l,I=s;return{titleHeight:I,indentSize:I,nodeHoverBg:y,nodeHoverColor:l.colorText,nodeSelectedBg:M,nodeSelectedColor:l.colorText}},J=l=>{const{colorTextLightSolid:s,colorPrimary:y}=l;return Object.assign(Object.assign({},H(l)),{directoryNodeSelectedColor:s,directoryNodeSelectedBg:y})};var fe=(0,Z.I$)("Tree",(l,{prefixCls:s})=>[{[l.componentCls]:(0,k.C2)(`${s}-checkbox`,l)},z(s,l),(0,xe.Z)(l)],J)},77632:function(Le,ye,d){d.d(ye,{Z:function(){return p}});var $=d(67294),k=d(87462),A={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"},xe=A,Ke=d(93771),Z=function(x,L){return $.createElement(Ke.Z,(0,k.Z)({},x,{ref:L,icon:xe}))},D=$.forwardRef(Z),Q=D,ie=d(41018),f=d(19267),oe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"},z=oe,H=function(x,L){return $.createElement(Ke.Z,(0,k.Z)({},x,{ref:L,icon:z}))},J=$.forwardRef(H),fe=J,l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"},s=l,y=function(x,L){return $.createElement(Ke.Z,(0,k.Z)({},x,{ref:L,icon:s}))},M=$.forwardRef(y),I=M,n=d(93967),t=d.n(n),v=d(96159),p=C=>{const{prefixCls:x,switcherIcon:L,treeNodeProps:K,showLine:_,switcherLoadingIcon:ce}=C,{isLeaf:de,expanded:ae,loading:se}=K;if(se)return $.isValidElement(ce)?ce:$.createElement(f.Z,{className:`${x}-switcher-loading-icon`});let B;if(_&&typeof _=="object"&&(B=_.showLeafIcon),de){if(!_)return null;if(typeof B!="boolean"&&B){const le=typeof B=="function"?B(K):B,ve=`${x}-switcher-line-custom-icon`;return $.isValidElement(le)?(0,v.Tm)(le,{className:t()(le.props.className||"",ve)}):le}return B?$.createElement(ie.Z,{className:`${x}-switcher-line-icon`}):$.createElement("span",{className:`${x}-switcher-leaf-line`})}const ue=`${x}-switcher-icon`,q=typeof L=="function"?L(K):L;return $.isValidElement(q)?(0,v.Tm)(q,{className:t()(q.props.className||"",ue)}):q!==void 0?q:_?ae?$.createElement(fe,{className:`${x}-switcher-line-icon`}):$.createElement(I,{className:`${x}-switcher-line-icon`}):$.createElement(Q,{className:ue})}},41018:function(Le,ye,d){var $=d(87462),k=d(67294),A=d(75573),xe=d(93771),Ke=function(Q,ie){return k.createElement(xe.Z,(0,$.Z)({},Q,{ref:ie,icon:A.Z}))},Z=k.forwardRef(Ke);ye.Z=Z},86128:function(Le,ye,d){d.d(ye,{Z:function(){return I}});var $=d(87462),k=d(4942),A=d(1413),xe=d(97685),Ke=d(91),Z=d(67294),D=d(93967),Q=d.n(D),ie=d(64217),f=d(27822),oe=function(t){for(var v=t.prefixCls,m=t.level,p=t.isStart,C=t.isEnd,x="".concat(v,"-indent-unit"),L=[],K=0;K<m;K+=1)L.push(Z.createElement("span",{key:K,className:Q()(x,(0,k.Z)((0,k.Z)({},"".concat(x,"-start"),p[K]),"".concat(x,"-end"),C[K]))}));return Z.createElement("span",{"aria-hidden":"true",className:"".concat(v,"-indent")},L)},z=Z.memo(oe),H=d(35381),J=d(1089),fe=["eventKey","className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","isStart","isEnd","expanded","selected","checked","halfChecked","loading","domRef","active","data","onMouseMove","selectable"],l="open",s="close",y="---",M=function(t){var v,m,p,C=t.eventKey,x=t.className,L=t.style,K=t.dragOver,_=t.dragOverGapTop,ce=t.dragOverGapBottom,de=t.isLeaf,ae=t.isStart,se=t.isEnd,B=t.expanded,ue=t.selected,q=t.checked,le=t.halfChecked,ve=t.loading,ee=t.domRef,ke=t.active,ge=t.data,we=t.onMouseMove,Oe=t.selectable,Fe=(0,Ke.Z)(t,fe),o=Z.useContext(f.k),U=Z.useContext(f.y),Ie=Z.useRef(null),Te=Z.useState(!1),Me=(0,xe.Z)(Te,2),Ze=Me[0],$e=Me[1],Ne=!!(o.disabled||t.disabled||(v=U.nodeDisabled)!==null&&v!==void 0&&v.call(U,ge)),c=Z.useMemo(function(){return!o.checkable||t.checkable===!1?!1:o.checkable},[o.checkable,t.checkable]),te=function(b){Ne||o.onNodeSelect(b,(0,J.F)(t))},G=function(b){Ne||!c||t.disableCheckbox||o.onNodeCheck(b,(0,J.F)(t),!q)},e=Z.useMemo(function(){return typeof Oe=="boolean"?Oe:o.selectable},[Oe,o.selectable]),h=function(b){o.onNodeClick(b,(0,J.F)(t)),e?te(b):G(b)},O=function(b){o.onNodeDoubleClick(b,(0,J.F)(t))},V=function(b){o.onNodeMouseEnter(b,(0,J.F)(t))},a=function(b){o.onNodeMouseLeave(b,(0,J.F)(t))},i=function(b){o.onNodeContextMenu(b,(0,J.F)(t))},r=Z.useMemo(function(){return!!(o.draggable&&(!o.draggable.nodeDraggable||o.draggable.nodeDraggable(ge)))},[o.draggable,ge]),u=function(b){b.stopPropagation(),$e(!0),o.onNodeDragStart(b,t);try{b.dataTransfer.setData("text/plain","")}catch(De){}},E=function(b){b.preventDefault(),b.stopPropagation(),o.onNodeDragEnter(b,t)},P=function(b){b.preventDefault(),b.stopPropagation(),o.onNodeDragOver(b,t)},S=function(b){b.stopPropagation(),o.onNodeDragLeave(b,t)},T=function(b){b.stopPropagation(),$e(!1),o.onNodeDragEnd(b,t)},W=function(b){b.preventDefault(),b.stopPropagation(),$e(!1),o.onNodeDrop(b,t)},g=function(b){ve||o.onNodeExpand(b,(0,J.F)(t))},N=Z.useMemo(function(){var Y=(0,H.Z)(o.keyEntities,C)||{},b=Y.children;return!!(b||[]).length},[o.keyEntities,C]),F=Z.useMemo(function(){return de===!1?!1:de||!o.loadData&&!N||o.loadData&&t.loaded&&!N},[de,o.loadData,N,t.loaded]);Z.useEffect(function(){ve||typeof o.loadData=="function"&&B&&!F&&!t.loaded&&o.onNodeLoad((0,J.F)(t))},[ve,o.loadData,o.onNodeLoad,B,F,t]);var w=Z.useMemo(function(){var Y;return(Y=o.draggable)!==null&&Y!==void 0&&Y.icon?Z.createElement("span",{className:"".concat(o.prefixCls,"-draggable-icon")},o.draggable.icon):null},[o.draggable]),R=function(b){var De=t.switcherIcon||o.switcherIcon;return typeof De=="function"?De((0,A.Z)((0,A.Z)({},t),{},{isLeaf:b})):De},ne=function(){if(F){var b=R(!0);return b!==!1?Z.createElement("span",{className:Q()("".concat(o.prefixCls,"-switcher"),"".concat(o.prefixCls,"-switcher-noop"))},b):null}var De=R(!1);return De!==!1?Z.createElement("span",{onClick:g,className:Q()("".concat(o.prefixCls,"-switcher"),"".concat(o.prefixCls,"-switcher_").concat(B?l:s))},De):null},X=Z.useMemo(function(){if(!c)return null;var Y=typeof c!="boolean"?c:null;return Z.createElement("span",{className:Q()("".concat(o.prefixCls,"-checkbox"),(0,k.Z)((0,k.Z)((0,k.Z)({},"".concat(o.prefixCls,"-checkbox-checked"),q),"".concat(o.prefixCls,"-checkbox-indeterminate"),!q&&le),"".concat(o.prefixCls,"-checkbox-disabled"),Ne||t.disableCheckbox)),onClick:G,role:"checkbox","aria-checked":le?"mixed":q,"aria-disabled":Ne||t.disableCheckbox,"aria-label":"Select ".concat(typeof t.title=="string"?t.title:"tree node")},Y)},[c,q,le,Ne,t.disableCheckbox,t.title]),j=Z.useMemo(function(){return F?null:B?l:s},[F,B]),re=Z.useMemo(function(){return Z.createElement("span",{className:Q()("".concat(o.prefixCls,"-iconEle"),"".concat(o.prefixCls,"-icon__").concat(j||"docu"),(0,k.Z)({},"".concat(o.prefixCls,"-icon_loading"),ve))})},[o.prefixCls,j,ve]),he=Z.useMemo(function(){var Y=!!o.draggable,b=!t.disabled&&Y&&o.dragOverNodeKey===C;return b?o.dropIndicatorRender({dropPosition:o.dropPosition,dropLevelOffset:o.dropLevelOffset,indent:o.indent,prefixCls:o.prefixCls,direction:o.direction}):null},[o.dropPosition,o.dropLevelOffset,o.indent,o.prefixCls,o.direction,o.draggable,o.dragOverNodeKey,o.dropIndicatorRender]),Ee=Z.useMemo(function(){var Y=t.title,b=Y===void 0?y:Y,De="".concat(o.prefixCls,"-node-content-wrapper"),He;if(o.showIcon){var Ae=t.icon||o.icon;He=Ae?Z.createElement("span",{className:Q()("".concat(o.prefixCls,"-iconEle"),"".concat(o.prefixCls,"-icon__customize"))},typeof Ae=="function"?Ae(t):Ae):re}else o.loadData&&ve&&(He=re);var Be;return typeof b=="function"?Be=b(ge):o.titleRender?Be=o.titleRender(ge):Be=b,Z.createElement("span",{ref:Ie,title:typeof b=="string"?b:"",className:Q()(De,"".concat(De,"-").concat(j||"normal"),(0,k.Z)({},"".concat(o.prefixCls,"-node-selected"),!Ne&&(ue||Ze))),onMouseEnter:V,onMouseLeave:a,onContextMenu:i,onClick:h,onDoubleClick:O},He,Z.createElement("span",{className:"".concat(o.prefixCls,"-title")},Be),he)},[o.prefixCls,o.showIcon,t,o.icon,re,o.titleRender,ge,j,V,a,i,h,O]),pe=(0,ie.Z)(Fe,{aria:!0,data:!0}),Ce=(0,H.Z)(o.keyEntities,C)||{},Se=Ce.level,be=se[se.length-1],Pe=!Ne&&r,We=o.draggingNodeKey===C,Re=Oe!==void 0?{"aria-selected":!!Oe}:void 0;return Z.createElement("div",(0,$.Z)({ref:ee,role:"treeitem","aria-expanded":de?void 0:B,className:Q()(x,"".concat(o.prefixCls,"-treenode"),(p={},(0,k.Z)((0,k.Z)((0,k.Z)((0,k.Z)((0,k.Z)((0,k.Z)((0,k.Z)((0,k.Z)((0,k.Z)((0,k.Z)(p,"".concat(o.prefixCls,"-treenode-disabled"),Ne),"".concat(o.prefixCls,"-treenode-switcher-").concat(B?"open":"close"),!de),"".concat(o.prefixCls,"-treenode-checkbox-checked"),q),"".concat(o.prefixCls,"-treenode-checkbox-indeterminate"),le),"".concat(o.prefixCls,"-treenode-selected"),ue),"".concat(o.prefixCls,"-treenode-loading"),ve),"".concat(o.prefixCls,"-treenode-active"),ke),"".concat(o.prefixCls,"-treenode-leaf-last"),be),"".concat(o.prefixCls,"-treenode-draggable"),r),"dragging",We),(0,k.Z)((0,k.Z)((0,k.Z)((0,k.Z)((0,k.Z)((0,k.Z)((0,k.Z)(p,"drop-target",o.dropTargetKey===C),"drop-container",o.dropContainerKey===C),"drag-over",!Ne&&K),"drag-over-gap-top",!Ne&&_),"drag-over-gap-bottom",!Ne&&ce),"filter-node",(m=o.filterTreeNode)===null||m===void 0?void 0:m.call(o,(0,J.F)(t))),"".concat(o.prefixCls,"-treenode-leaf"),F))),style:L,draggable:Pe,onDragStart:Pe?u:void 0,onDragEnter:r?E:void 0,onDragOver:r?P:void 0,onDragLeave:r?S:void 0,onDrop:r?W:void 0,onDragEnd:r?T:void 0,onMouseMove:we},Re,pe),Z.createElement(z,{prefixCls:o.prefixCls,level:Se,isStart:ae,isEnd:se}),w,ne(),X,Ee)};M.isTreeNode=1;var I=M},27822:function(Le,ye,d){d.d(ye,{k:function(){return k},y:function(){return A}});var $=d(67294),k=$.createContext(null),A=$.createContext({})},70593:function(Le,ye,d){d.d(ye,{OF:function(){return C.Z},y6:function(){return s.y},ZP:function(){return Ne}});var $=d(87462),k=d(71002),A=d(1413),xe=d(74902),Ke=d(15671),Z=d(43144),D=d(97326),Q=d(60136),ie=d(29388),f=d(4942),oe=d(93967),z=d.n(oe),H=d(15105),J=d(64217),fe=d(80334),l=d(67294),s=d(27822),y=function(te){var G=te.dropPosition,e=te.dropLevelOffset,h=te.indent,O={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:2};switch(G){case-1:O.top=0,O.left=-e*h;break;case 1:O.bottom=0,O.left=-e*h;break;case 0:O.bottom=0,O.left=h;break}return l.createElement("div",{style:O})},M=y;function I(c){if(c==null)throw new TypeError("Cannot destructure "+c)}var n=d(97685),t=d(91),v=d(8410),m=d(87718),p=d(29372),C=d(86128);function x(c,te){var G=l.useState(!1),e=(0,n.Z)(G,2),h=e[0],O=e[1];(0,v.Z)(function(){if(h)return c(),function(){te()}},[h]),(0,v.Z)(function(){return O(!0),function(){O(!1)}},[])}var L=x,K=d(1089),_=["className","style","motion","motionNodes","motionType","onMotionStart","onMotionEnd","active","treeNodeRequiredProps"],ce=l.forwardRef(function(c,te){var G=c.className,e=c.style,h=c.motion,O=c.motionNodes,V=c.motionType,a=c.onMotionStart,i=c.onMotionEnd,r=c.active,u=c.treeNodeRequiredProps,E=(0,t.Z)(c,_),P=l.useState(!0),S=(0,n.Z)(P,2),T=S[0],W=S[1],g=l.useContext(s.k),N=g.prefixCls,F=O&&V!=="hide";(0,v.Z)(function(){O&&F!==T&&W(F)},[O]);var w=function(){O&&a()},R=l.useRef(!1),ne=function(){O&&!R.current&&(R.current=!0,i())};L(w,ne);var X=function(re){F===re&&ne()};return O?l.createElement(p.ZP,(0,$.Z)({ref:te,visible:T},h,{motionAppear:V==="show",onVisibleChanged:X}),function(j,re){var he=j.className,Ee=j.style;return l.createElement("div",{ref:re,className:z()("".concat(N,"-treenode-motion"),he),style:Ee},O.map(function(pe){var Ce=Object.assign({},(I(pe.data),pe.data)),Se=pe.title,be=pe.key,Pe=pe.isStart,We=pe.isEnd;delete Ce.children;var Re=(0,K.H8)(be,u);return l.createElement(C.Z,(0,$.Z)({},Ce,Re,{title:Se,active:r,data:pe.data,key:be,isStart:Pe,isEnd:We}))}))}):l.createElement(C.Z,(0,$.Z)({domRef:te,className:G,style:e},E,{active:r}))}),de=ce;function ae(){var c=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],te=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],G=c.length,e=te.length;if(Math.abs(G-e)!==1)return{add:!1,key:null};function h(O,V){var a=new Map;O.forEach(function(r){a.set(r,!0)});var i=V.filter(function(r){return!a.has(r)});return i.length===1?i[0]:null}return G<e?{add:!0,key:h(c,te)}:{add:!1,key:h(te,c)}}function se(c,te,G){var e=c.findIndex(function(a){return a.key===G}),h=c[e+1],O=te.findIndex(function(a){return a.key===G});if(h){var V=te.findIndex(function(a){return a.key===h.key});return te.slice(O+1,V)}return te.slice(O+1)}var B=["prefixCls","data","selectable","checkable","expandedKeys","selectedKeys","checkedKeys","loadedKeys","loadingKeys","halfCheckedKeys","keyEntities","disabled","dragging","dragOverNodeKey","dropPosition","motion","height","itemHeight","virtual","scrollWidth","focusable","activeItem","focused","tabIndex","onKeyDown","onFocus","onBlur","onActiveChange","onListChangeStart","onListChangeEnd"],ue={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},q=function(){},le="RC_TREE_MOTION_".concat(Math.random()),ve={key:le},ee={key:le,level:0,index:0,pos:"0",node:ve,nodes:[ve]},ke={parent:null,children:[],pos:ee.pos,data:ve,title:null,key:le,isStart:[],isEnd:[]};function ge(c,te,G,e){return te===!1||!G?c:c.slice(0,Math.ceil(G/e)+1)}function we(c){var te=c.key,G=c.pos;return(0,K.km)(te,G)}function Oe(c){for(var te=String(c.data.key),G=c;G.parent;)G=G.parent,te="".concat(G.data.key," > ").concat(te);return te}var Fe=l.forwardRef(function(c,te){var G=c.prefixCls,e=c.data,h=c.selectable,O=c.checkable,V=c.expandedKeys,a=c.selectedKeys,i=c.checkedKeys,r=c.loadedKeys,u=c.loadingKeys,E=c.halfCheckedKeys,P=c.keyEntities,S=c.disabled,T=c.dragging,W=c.dragOverNodeKey,g=c.dropPosition,N=c.motion,F=c.height,w=c.itemHeight,R=c.virtual,ne=c.scrollWidth,X=c.focusable,j=c.activeItem,re=c.focused,he=c.tabIndex,Ee=c.onKeyDown,pe=c.onFocus,Ce=c.onBlur,Se=c.onActiveChange,be=c.onListChangeStart,Pe=c.onListChangeEnd,We=(0,t.Z)(c,B),Re=l.useRef(null),Y=l.useRef(null);l.useImperativeHandle(te,function(){return{scrollTo:function(Ue){Re.current.scrollTo(Ue)},getIndentWidth:function(){return Y.current.offsetWidth}}});var b=l.useState(V),De=(0,n.Z)(b,2),He=De[0],Ae=De[1],Be=l.useState(e),Qe=(0,n.Z)(Be,2),je=Qe[0],Je=Qe[1],at=l.useState(e),qe=(0,n.Z)(at,2),rt=qe[0],Ge=qe[1],Ve=l.useState([]),et=(0,n.Z)(Ve,2),ft=et[0],ot=et[1],vt=l.useState(null),st=(0,n.Z)(vt,2),gt=st[0],it=st[1],ct=l.useRef(e);ct.current=e;function dt(){var me=ct.current;Je(me),Ge(me),ot([]),it(null),Pe()}(0,v.Z)(function(){Ae(V);var me=ae(He,V);if(me.key!==null)if(me.add){var Ue=je.findIndex(function(Xe){var Ye=Xe.key;return Ye===me.key}),ze=ge(se(je,e,me.key),R,F,w),tt=je.slice();tt.splice(Ue+1,0,ke),Ge(tt),ot(ze),it("show")}else{var _e=e.findIndex(function(Xe){var Ye=Xe.key;return Ye===me.key}),lt=ge(se(e,je,me.key),R,F,w),nt=e.slice();nt.splice(_e+1,0,ke),Ge(nt),ot(lt),it("hide")}else je!==e&&(Je(e),Ge(e))},[V,e]),l.useEffect(function(){T||dt()},[T]);var ht=N?rt:e,ut={expandedKeys:V,selectedKeys:a,loadedKeys:r,loadingKeys:u,checkedKeys:i,halfCheckedKeys:E,dragOverNodeKey:W,dropPosition:g,keyEntities:P};return l.createElement(l.Fragment,null,re&&j&&l.createElement("span",{style:ue,"aria-live":"assertive"},Oe(j)),l.createElement("div",null,l.createElement("input",{style:ue,disabled:X===!1||S,tabIndex:X!==!1?he:null,onKeyDown:Ee,onFocus:pe,onBlur:Ce,value:"",onChange:q,"aria-label":"for screen reader"})),l.createElement("div",{className:"".concat(G,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden",border:0,padding:0}},l.createElement("div",{className:"".concat(G,"-indent")},l.createElement("div",{ref:Y,className:"".concat(G,"-indent-unit")}))),l.createElement(m.Z,(0,$.Z)({},We,{data:ht,itemKey:we,height:F,fullHeight:!1,virtual:R,itemHeight:w,scrollWidth:ne,prefixCls:"".concat(G,"-list"),ref:Re,role:"tree",onVisibleChange:function(Ue){Ue.every(function(ze){return we(ze)!==le})&&dt()}}),function(me){var Ue=me.pos,ze=Object.assign({},(I(me.data),me.data)),tt=me.title,_e=me.key,lt=me.isStart,nt=me.isEnd,Xe=(0,K.km)(_e,Ue);delete ze.key,delete ze.children;var Ye=(0,K.H8)(Xe,ut);return l.createElement(de,(0,$.Z)({},ze,Ye,{title:tt,active:!!j&&_e===j.key,pos:Ue,data:me.data,isStart:lt,isEnd:nt,motion:N,motionNodes:_e===le?ft:null,motionType:gt,onMotionStart:be,onMotionEnd:dt,treeNodeRequiredProps:ut,onMouseMove:function(){Se(null)}}))}))}),o=Fe,U=d(10225),Ie=d(17341),Te=d(35381),Me=10,Ze=function(c){(0,Q.Z)(G,c);var te=(0,ie.Z)(G);function G(){var e;(0,Ke.Z)(this,G);for(var h=arguments.length,O=new Array(h),V=0;V<h;V++)O[V]=arguments[V];return e=te.call.apply(te,[this].concat(O)),(0,f.Z)((0,D.Z)(e),"destroyed",!1),(0,f.Z)((0,D.Z)(e),"delayedDragEnterLogic",void 0),(0,f.Z)((0,D.Z)(e),"loadingRetryTimes",{}),(0,f.Z)((0,D.Z)(e),"state",{keyEntities:{},indent:null,selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null,treeData:[],flattenNodes:[],focused:!1,activeKey:null,listChanging:!1,prevProps:null,fieldNames:(0,K.w$)()}),(0,f.Z)((0,D.Z)(e),"dragStartMousePosition",null),(0,f.Z)((0,D.Z)(e),"dragNodeProps",null),(0,f.Z)((0,D.Z)(e),"currentMouseOverDroppableNodeKey",null),(0,f.Z)((0,D.Z)(e),"listRef",l.createRef()),(0,f.Z)((0,D.Z)(e),"onNodeDragStart",function(a,i){var r=e.state,u=r.expandedKeys,E=r.keyEntities,P=e.props.onDragStart,S=i.eventKey;e.dragNodeProps=i,e.dragStartMousePosition={x:a.clientX,y:a.clientY};var T=(0,U._5)(u,S);e.setState({draggingNodeKey:S,dragChildrenKeys:(0,U.wA)(S,E),indent:e.listRef.current.getIndentWidth()}),e.setExpandedKeys(T),window.addEventListener("dragend",e.onWindowDragEnd),P==null||P({event:a,node:(0,K.F)(i)})}),(0,f.Z)((0,D.Z)(e),"onNodeDragEnter",function(a,i){var r=e.state,u=r.expandedKeys,E=r.keyEntities,P=r.dragChildrenKeys,S=r.flattenNodes,T=r.indent,W=e.props,g=W.onDragEnter,N=W.onExpand,F=W.allowDrop,w=W.direction,R=i.pos,ne=i.eventKey;if(e.currentMouseOverDroppableNodeKey!==ne&&(e.currentMouseOverDroppableNodeKey=ne),!e.dragNodeProps){e.resetDragState();return}var X=(0,U.OM)(a,e.dragNodeProps,i,T,e.dragStartMousePosition,F,S,E,u,w),j=X.dropPosition,re=X.dropLevelOffset,he=X.dropTargetKey,Ee=X.dropContainerKey,pe=X.dropTargetPos,Ce=X.dropAllowed,Se=X.dragOverNodeKey;if(P.includes(he)||!Ce){e.resetDragState();return}if(e.delayedDragEnterLogic||(e.delayedDragEnterLogic={}),Object.keys(e.delayedDragEnterLogic).forEach(function(be){clearTimeout(e.delayedDragEnterLogic[be])}),e.dragNodeProps.eventKey!==i.eventKey&&(a.persist(),e.delayedDragEnterLogic[R]=window.setTimeout(function(){if(e.state.draggingNodeKey!==null){var be=(0,xe.Z)(u),Pe=(0,Te.Z)(E,i.eventKey);Pe&&(Pe.children||[]).length&&(be=(0,U.L0)(u,i.eventKey)),e.props.hasOwnProperty("expandedKeys")||e.setExpandedKeys(be),N==null||N(be,{node:(0,K.F)(i),expanded:!0,nativeEvent:a.nativeEvent})}},800)),e.dragNodeProps.eventKey===he&&re===0){e.resetDragState();return}e.setState({dragOverNodeKey:Se,dropPosition:j,dropLevelOffset:re,dropTargetKey:he,dropContainerKey:Ee,dropTargetPos:pe,dropAllowed:Ce}),g==null||g({event:a,node:(0,K.F)(i),expandedKeys:u})}),(0,f.Z)((0,D.Z)(e),"onNodeDragOver",function(a,i){var r=e.state,u=r.dragChildrenKeys,E=r.flattenNodes,P=r.keyEntities,S=r.expandedKeys,T=r.indent,W=e.props,g=W.onDragOver,N=W.allowDrop,F=W.direction;if(e.dragNodeProps){var w=(0,U.OM)(a,e.dragNodeProps,i,T,e.dragStartMousePosition,N,E,P,S,F),R=w.dropPosition,ne=w.dropLevelOffset,X=w.dropTargetKey,j=w.dropContainerKey,re=w.dropTargetPos,he=w.dropAllowed,Ee=w.dragOverNodeKey;u.includes(X)||!he||(e.dragNodeProps.eventKey===X&&ne===0?e.state.dropPosition===null&&e.state.dropLevelOffset===null&&e.state.dropTargetKey===null&&e.state.dropContainerKey===null&&e.state.dropTargetPos===null&&e.state.dropAllowed===!1&&e.state.dragOverNodeKey===null||e.resetDragState():R===e.state.dropPosition&&ne===e.state.dropLevelOffset&&X===e.state.dropTargetKey&&j===e.state.dropContainerKey&&re===e.state.dropTargetPos&&he===e.state.dropAllowed&&Ee===e.state.dragOverNodeKey||e.setState({dropPosition:R,dropLevelOffset:ne,dropTargetKey:X,dropContainerKey:j,dropTargetPos:re,dropAllowed:he,dragOverNodeKey:Ee}),g==null||g({event:a,node:(0,K.F)(i)}))}}),(0,f.Z)((0,D.Z)(e),"onNodeDragLeave",function(a,i){e.currentMouseOverDroppableNodeKey===i.eventKey&&!a.currentTarget.contains(a.relatedTarget)&&(e.resetDragState(),e.currentMouseOverDroppableNodeKey=null);var r=e.props.onDragLeave;r==null||r({event:a,node:(0,K.F)(i)})}),(0,f.Z)((0,D.Z)(e),"onWindowDragEnd",function(a){e.onNodeDragEnd(a,null,!0),window.removeEventListener("dragend",e.onWindowDragEnd)}),(0,f.Z)((0,D.Z)(e),"onNodeDragEnd",function(a,i){var r=e.props.onDragEnd;e.setState({dragOverNodeKey:null}),e.cleanDragState(),r==null||r({event:a,node:(0,K.F)(i)}),e.dragNodeProps=null,window.removeEventListener("dragend",e.onWindowDragEnd)}),(0,f.Z)((0,D.Z)(e),"onNodeDrop",function(a,i){var r,u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,E=e.state,P=E.dragChildrenKeys,S=E.dropPosition,T=E.dropTargetKey,W=E.dropTargetPos,g=E.dropAllowed;if(g){var N=e.props.onDrop;if(e.setState({dragOverNodeKey:null}),e.cleanDragState(),T!==null){var F=(0,A.Z)((0,A.Z)({},(0,K.H8)(T,e.getTreeNodeRequiredProps())),{},{active:((r=e.getActiveItem())===null||r===void 0?void 0:r.key)===T,data:(0,Te.Z)(e.state.keyEntities,T).node}),w=P.includes(T);(0,fe.ZP)(!w,"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.");var R=(0,U.yx)(W),ne={event:a,node:(0,K.F)(F),dragNode:e.dragNodeProps?(0,K.F)(e.dragNodeProps):null,dragNodesKeys:[e.dragNodeProps.eventKey].concat(P),dropToGap:S!==0,dropPosition:S+Number(R[R.length-1])};u||N==null||N(ne),e.dragNodeProps=null}}}),(0,f.Z)((0,D.Z)(e),"cleanDragState",function(){var a=e.state.draggingNodeKey;a!==null&&e.setState({draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),e.dragStartMousePosition=null,e.currentMouseOverDroppableNodeKey=null}),(0,f.Z)((0,D.Z)(e),"triggerExpandActionExpand",function(a,i){var r=e.state,u=r.expandedKeys,E=r.flattenNodes,P=i.expanded,S=i.key,T=i.isLeaf;if(!(T||a.shiftKey||a.metaKey||a.ctrlKey)){var W=E.filter(function(N){return N.key===S})[0],g=(0,K.F)((0,A.Z)((0,A.Z)({},(0,K.H8)(S,e.getTreeNodeRequiredProps())),{},{data:W.data}));e.setExpandedKeys(P?(0,U._5)(u,S):(0,U.L0)(u,S)),e.onNodeExpand(a,g)}}),(0,f.Z)((0,D.Z)(e),"onNodeClick",function(a,i){var r=e.props,u=r.onClick,E=r.expandAction;E==="click"&&e.triggerExpandActionExpand(a,i),u==null||u(a,i)}),(0,f.Z)((0,D.Z)(e),"onNodeDoubleClick",function(a,i){var r=e.props,u=r.onDoubleClick,E=r.expandAction;E==="doubleClick"&&e.triggerExpandActionExpand(a,i),u==null||u(a,i)}),(0,f.Z)((0,D.Z)(e),"onNodeSelect",function(a,i){var r=e.state.selectedKeys,u=e.state,E=u.keyEntities,P=u.fieldNames,S=e.props,T=S.onSelect,W=S.multiple,g=i.selected,N=i[P.key],F=!g;F?W?r=(0,U.L0)(r,N):r=[N]:r=(0,U._5)(r,N);var w=r.map(function(R){var ne=(0,Te.Z)(E,R);return ne?ne.node:null}).filter(Boolean);e.setUncontrolledState({selectedKeys:r}),T==null||T(r,{event:"select",selected:F,node:i,selectedNodes:w,nativeEvent:a.nativeEvent})}),(0,f.Z)((0,D.Z)(e),"onNodeCheck",function(a,i,r){var u=e.state,E=u.keyEntities,P=u.checkedKeys,S=u.halfCheckedKeys,T=e.props,W=T.checkStrictly,g=T.onCheck,N=i.key,F,w={event:"check",node:i,checked:r,nativeEvent:a.nativeEvent};if(W){var R=r?(0,U.L0)(P,N):(0,U._5)(P,N),ne=(0,U._5)(S,N);F={checked:R,halfChecked:ne},w.checkedNodes=R.map(function(pe){return(0,Te.Z)(E,pe)}).filter(Boolean).map(function(pe){return pe.node}),e.setUncontrolledState({checkedKeys:R})}else{var X=(0,Ie.S)([].concat((0,xe.Z)(P),[N]),!0,E),j=X.checkedKeys,re=X.halfCheckedKeys;if(!r){var he=new Set(j);he.delete(N);var Ee=(0,Ie.S)(Array.from(he),{checked:!1,halfCheckedKeys:re},E);j=Ee.checkedKeys,re=Ee.halfCheckedKeys}F=j,w.checkedNodes=[],w.checkedNodesPositions=[],w.halfCheckedKeys=re,j.forEach(function(pe){var Ce=(0,Te.Z)(E,pe);if(Ce){var Se=Ce.node,be=Ce.pos;w.checkedNodes.push(Se),w.checkedNodesPositions.push({node:Se,pos:be})}}),e.setUncontrolledState({checkedKeys:j},!1,{halfCheckedKeys:re})}g==null||g(F,w)}),(0,f.Z)((0,D.Z)(e),"onNodeLoad",function(a){var i,r=a.key,u=e.state.keyEntities,E=(0,Te.Z)(u,r);if(!(E!=null&&(i=E.children)!==null&&i!==void 0&&i.length)){var P=new Promise(function(S,T){e.setState(function(W){var g=W.loadedKeys,N=g===void 0?[]:g,F=W.loadingKeys,w=F===void 0?[]:F,R=e.props,ne=R.loadData,X=R.onLoad;if(!ne||N.includes(r)||w.includes(r))return null;var j=ne(a);return j.then(function(){var re=e.state.loadedKeys,he=(0,U.L0)(re,r);X==null||X(he,{event:"load",node:a}),e.setUncontrolledState({loadedKeys:he}),e.setState(function(Ee){return{loadingKeys:(0,U._5)(Ee.loadingKeys,r)}}),S()}).catch(function(re){if(e.setState(function(Ee){return{loadingKeys:(0,U._5)(Ee.loadingKeys,r)}}),e.loadingRetryTimes[r]=(e.loadingRetryTimes[r]||0)+1,e.loadingRetryTimes[r]>=Me){var he=e.state.loadedKeys;(0,fe.ZP)(!1,"Retry for `loadData` many times but still failed. No more retry."),e.setUncontrolledState({loadedKeys:(0,U.L0)(he,r)}),S()}T(re)}),{loadingKeys:(0,U.L0)(w,r)}})});return P.catch(function(){}),P}}),(0,f.Z)((0,D.Z)(e),"onNodeMouseEnter",function(a,i){var r=e.props.onMouseEnter;r==null||r({event:a,node:i})}),(0,f.Z)((0,D.Z)(e),"onNodeMouseLeave",function(a,i){var r=e.props.onMouseLeave;r==null||r({event:a,node:i})}),(0,f.Z)((0,D.Z)(e),"onNodeContextMenu",function(a,i){var r=e.props.onRightClick;r&&(a.preventDefault(),r({event:a,node:i}))}),(0,f.Z)((0,D.Z)(e),"onFocus",function(){var a=e.props.onFocus;e.setState({focused:!0});for(var i=arguments.length,r=new Array(i),u=0;u<i;u++)r[u]=arguments[u];a==null||a.apply(void 0,r)}),(0,f.Z)((0,D.Z)(e),"onBlur",function(){var a=e.props.onBlur;e.setState({focused:!1}),e.onActiveChange(null);for(var i=arguments.length,r=new Array(i),u=0;u<i;u++)r[u]=arguments[u];a==null||a.apply(void 0,r)}),(0,f.Z)((0,D.Z)(e),"getTreeNodeRequiredProps",function(){var a=e.state,i=a.expandedKeys,r=a.selectedKeys,u=a.loadedKeys,E=a.loadingKeys,P=a.checkedKeys,S=a.halfCheckedKeys,T=a.dragOverNodeKey,W=a.dropPosition,g=a.keyEntities;return{expandedKeys:i||[],selectedKeys:r||[],loadedKeys:u||[],loadingKeys:E||[],checkedKeys:P||[],halfCheckedKeys:S||[],dragOverNodeKey:T,dropPosition:W,keyEntities:g}}),(0,f.Z)((0,D.Z)(e),"setExpandedKeys",function(a){var i=e.state,r=i.treeData,u=i.fieldNames,E=(0,K.oH)(r,a,u);e.setUncontrolledState({expandedKeys:a,flattenNodes:E},!0)}),(0,f.Z)((0,D.Z)(e),"onNodeExpand",function(a,i){var r=e.state.expandedKeys,u=e.state,E=u.listChanging,P=u.fieldNames,S=e.props,T=S.onExpand,W=S.loadData,g=i.expanded,N=i[P.key];if(!E){var F=r.includes(N),w=!g;if((0,fe.ZP)(g&&F||!g&&!F,"Expand state not sync with index check"),r=w?(0,U.L0)(r,N):(0,U._5)(r,N),e.setExpandedKeys(r),T==null||T(r,{node:i,expanded:w,nativeEvent:a.nativeEvent}),w&&W){var R=e.onNodeLoad(i);R&&R.then(function(){var ne=(0,K.oH)(e.state.treeData,r,P);e.setUncontrolledState({flattenNodes:ne})}).catch(function(){var ne=e.state.expandedKeys,X=(0,U._5)(ne,N);e.setExpandedKeys(X)})}}}),(0,f.Z)((0,D.Z)(e),"onListChangeStart",function(){e.setUncontrolledState({listChanging:!0})}),(0,f.Z)((0,D.Z)(e),"onListChangeEnd",function(){setTimeout(function(){e.setUncontrolledState({listChanging:!1})})}),(0,f.Z)((0,D.Z)(e),"onActiveChange",function(a){var i=e.state.activeKey,r=e.props,u=r.onActiveChange,E=r.itemScrollOffset,P=E===void 0?0:E;i!==a&&(e.setState({activeKey:a}),a!==null&&e.scrollTo({key:a,offset:P}),u==null||u(a))}),(0,f.Z)((0,D.Z)(e),"getActiveItem",function(){var a=e.state,i=a.activeKey,r=a.flattenNodes;return i===null?null:r.find(function(u){var E=u.key;return E===i})||null}),(0,f.Z)((0,D.Z)(e),"offsetActiveKey",function(a){var i=e.state,r=i.flattenNodes,u=i.activeKey,E=r.findIndex(function(T){var W=T.key;return W===u});E===-1&&a<0&&(E=r.length),E=(E+a+r.length)%r.length;var P=r[E];if(P){var S=P.key;e.onActiveChange(S)}else e.onActiveChange(null)}),(0,f.Z)((0,D.Z)(e),"onKeyDown",function(a){var i=e.state,r=i.activeKey,u=i.expandedKeys,E=i.checkedKeys,P=i.fieldNames,S=e.props,T=S.onKeyDown,W=S.checkable,g=S.selectable;switch(a.which){case H.Z.UP:{e.offsetActiveKey(-1),a.preventDefault();break}case H.Z.DOWN:{e.offsetActiveKey(1),a.preventDefault();break}}var N=e.getActiveItem();if(N&&N.data){var F=e.getTreeNodeRequiredProps(),w=N.data.isLeaf===!1||!!(N.data[P.children]||[]).length,R=(0,K.F)((0,A.Z)((0,A.Z)({},(0,K.H8)(r,F)),{},{data:N.data,active:!0}));switch(a.which){case H.Z.LEFT:{w&&u.includes(r)?e.onNodeExpand({},R):N.parent&&e.onActiveChange(N.parent.key),a.preventDefault();break}case H.Z.RIGHT:{w&&!u.includes(r)?e.onNodeExpand({},R):N.children&&N.children.length&&e.onActiveChange(N.children[0].key),a.preventDefault();break}case H.Z.ENTER:case H.Z.SPACE:{W&&!R.disabled&&R.checkable!==!1&&!R.disableCheckbox?e.onNodeCheck({},R,!E.includes(r)):!W&&g&&!R.disabled&&R.selectable!==!1&&e.onNodeSelect({},R);break}}}T==null||T(a)}),(0,f.Z)((0,D.Z)(e),"setUncontrolledState",function(a){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;if(!e.destroyed){var u=!1,E=!0,P={};Object.keys(a).forEach(function(S){if(e.props.hasOwnProperty(S)){E=!1;return}u=!0,P[S]=a[S]}),u&&(!i||E)&&e.setState((0,A.Z)((0,A.Z)({},P),r))}}),(0,f.Z)((0,D.Z)(e),"scrollTo",function(a){e.listRef.current.scrollTo(a)}),e}return(0,Z.Z)(G,[{key:"componentDidMount",value:function(){this.destroyed=!1,this.onUpdated()}},{key:"componentDidUpdate",value:function(){this.onUpdated()}},{key:"onUpdated",value:function(){var h=this.props,O=h.activeKey,V=h.itemScrollOffset,a=V===void 0?0:V;O!==void 0&&O!==this.state.activeKey&&(this.setState({activeKey:O}),O!==null&&this.scrollTo({key:O,offset:a}))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("dragend",this.onWindowDragEnd),this.destroyed=!0}},{key:"resetDragState",value:function(){this.setState({dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})}},{key:"render",value:function(){var h=this.state,O=h.focused,V=h.flattenNodes,a=h.keyEntities,i=h.draggingNodeKey,r=h.activeKey,u=h.dropLevelOffset,E=h.dropContainerKey,P=h.dropTargetKey,S=h.dropPosition,T=h.dragOverNodeKey,W=h.indent,g=this.props,N=g.prefixCls,F=g.className,w=g.style,R=g.showLine,ne=g.focusable,X=g.tabIndex,j=X===void 0?0:X,re=g.selectable,he=g.showIcon,Ee=g.icon,pe=g.switcherIcon,Ce=g.draggable,Se=g.checkable,be=g.checkStrictly,Pe=g.disabled,We=g.motion,Re=g.loadData,Y=g.filterTreeNode,b=g.height,De=g.itemHeight,He=g.scrollWidth,Ae=g.virtual,Be=g.titleRender,Qe=g.dropIndicatorRender,je=g.onContextMenu,Je=g.onScroll,at=g.direction,qe=g.rootClassName,rt=g.rootStyle,Ge=(0,J.Z)(this.props,{aria:!0,data:!0}),Ve;Ce&&((0,k.Z)(Ce)==="object"?Ve=Ce:typeof Ce=="function"?Ve={nodeDraggable:Ce}:Ve={});var et={prefixCls:N,selectable:re,showIcon:he,icon:Ee,switcherIcon:pe,draggable:Ve,draggingNodeKey:i,checkable:Se,checkStrictly:be,disabled:Pe,keyEntities:a,dropLevelOffset:u,dropContainerKey:E,dropTargetKey:P,dropPosition:S,dragOverNodeKey:T,indent:W,direction:at,dropIndicatorRender:Qe,loadData:Re,filterTreeNode:Y,titleRender:Be,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop};return l.createElement(s.k.Provider,{value:et},l.createElement("div",{className:z()(N,F,qe,(0,f.Z)((0,f.Z)((0,f.Z)({},"".concat(N,"-show-line"),R),"".concat(N,"-focused"),O),"".concat(N,"-active-focused"),r!==null)),style:rt},l.createElement(o,(0,$.Z)({ref:this.listRef,prefixCls:N,style:w,data:V,disabled:Pe,selectable:re,checkable:!!Se,motion:We,dragging:i!==null,height:b,itemHeight:De,virtual:Ae,focusable:ne,focused:O,tabIndex:j,activeItem:this.getActiveItem(),onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,onActiveChange:this.onActiveChange,onListChangeStart:this.onListChangeStart,onListChangeEnd:this.onListChangeEnd,onContextMenu:je,onScroll:Je,scrollWidth:He},this.getTreeNodeRequiredProps(),Ge))))}}],[{key:"getDerivedStateFromProps",value:function(h,O){var V=O.prevProps,a={prevProps:h};function i(j){return!V&&h.hasOwnProperty(j)||V&&V[j]!==h[j]}var r,u=O.fieldNames;if(i("fieldNames")&&(u=(0,K.w$)(h.fieldNames),a.fieldNames=u),i("treeData")?r=h.treeData:i("children")&&((0,fe.ZP)(!1,"`children` of Tree is deprecated. Please use `treeData` instead."),r=(0,K.zn)(h.children)),r){a.treeData=r;var E=(0,K.I8)(r,{fieldNames:u});a.keyEntities=(0,A.Z)((0,f.Z)({},le,ee),E.keyEntities)}var P=a.keyEntities||O.keyEntities;if(i("expandedKeys")||V&&i("autoExpandParent"))a.expandedKeys=h.autoExpandParent||!V&&h.defaultExpandParent?(0,U.r7)(h.expandedKeys,P):h.expandedKeys;else if(!V&&h.defaultExpandAll){var S=(0,A.Z)({},P);delete S[le];var T=[];Object.keys(S).forEach(function(j){var re=S[j];re.children&&re.children.length&&T.push(re.key)}),a.expandedKeys=T}else!V&&h.defaultExpandedKeys&&(a.expandedKeys=h.autoExpandParent||h.defaultExpandParent?(0,U.r7)(h.defaultExpandedKeys,P):h.defaultExpandedKeys);if(a.expandedKeys||delete a.expandedKeys,r||a.expandedKeys){var W=(0,K.oH)(r||O.treeData,a.expandedKeys||O.expandedKeys,u);a.flattenNodes=W}if(h.selectable&&(i("selectedKeys")?a.selectedKeys=(0,U.BT)(h.selectedKeys,h):!V&&h.defaultSelectedKeys&&(a.selectedKeys=(0,U.BT)(h.defaultSelectedKeys,h))),h.checkable){var g;if(i("checkedKeys")?g=(0,U.E6)(h.checkedKeys)||{}:!V&&h.defaultCheckedKeys?g=(0,U.E6)(h.defaultCheckedKeys)||{}:r&&(g=(0,U.E6)(h.checkedKeys)||{checkedKeys:O.checkedKeys,halfCheckedKeys:O.halfCheckedKeys}),g){var N=g,F=N.checkedKeys,w=F===void 0?[]:F,R=N.halfCheckedKeys,ne=R===void 0?[]:R;if(!h.checkStrictly){var X=(0,Ie.S)(w,!0,P);w=X.checkedKeys,ne=X.halfCheckedKeys}a.checkedKeys=w,a.halfCheckedKeys=ne}}return i("loadedKeys")&&(a.loadedKeys=h.loadedKeys),a}}]),G}(l.Component);(0,f.Z)(Ze,"defaultProps",{prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:M,allowDrop:function(){return!0},expandAction:!1}),(0,f.Z)(Ze,"TreeNode",C.Z);var $e=Ze,Ne=$e},10225:function(Le,ye,d){d.d(ye,{BT:function(){return l},E6:function(){return M},L0:function(){return f},OM:function(){return fe},_5:function(){return ie},r7:function(){return I},wA:function(){return z},yx:function(){return oe}});var $=d(74902),k=d(71002),A=d(80334),xe=d(67294),Ke=d(86128),Z=d(35381),D=d(1089),Q=null;function ie(n,t){if(!n)return[];var v=n.slice(),m=v.indexOf(t);return m>=0&&v.splice(m,1),v}function f(n,t){var v=(n||[]).slice();return v.indexOf(t)===-1&&v.push(t),v}function oe(n){return n.split("-")}function z(n,t){var v=[],m=(0,Z.Z)(t,n);function p(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];C.forEach(function(x){var L=x.key,K=x.children;v.push(L),p(K)})}return p(m.children),v}function H(n){if(n.parent){var t=oe(n.pos);return Number(t[t.length-1])===n.parent.children.length-1}return!1}function J(n){var t=oe(n.pos);return Number(t[t.length-1])===0}function fe(n,t,v,m,p,C,x,L,K,_){var ce,de=n.clientX,ae=n.clientY,se=n.target.getBoundingClientRect(),B=se.top,ue=se.height,q=(_==="rtl"?-1:1)*(((p==null?void 0:p.x)||0)-de),le=(q-12)/m,ve=K.filter(function(Ne){var c;return(c=L[Ne])===null||c===void 0||(c=c.children)===null||c===void 0?void 0:c.length}),ee=(0,Z.Z)(L,v.eventKey);if(ae<B+ue/2){var ke=x.findIndex(function(Ne){return Ne.key===ee.key}),ge=ke<=0?0:ke-1,we=x[ge].key;ee=(0,Z.Z)(L,we)}var Oe=ee.key,Fe=ee,o=ee.key,U=0,Ie=0;if(!ve.includes(Oe))for(var Te=0;Te<le&&H(ee);Te+=1)ee=ee.parent,Ie+=1;var Me=t.data,Ze=ee.node,$e=!0;return J(ee)&&ee.level===0&&ae<B+ue/2&&C({dragNode:Me,dropNode:Ze,dropPosition:-1})&&ee.key===v.eventKey?U=-1:(Fe.children||[]).length&&ve.includes(o)?C({dragNode:Me,dropNode:Ze,dropPosition:0})?U=0:$e=!1:Ie===0?le>-1.5?C({dragNode:Me,dropNode:Ze,dropPosition:1})?U=1:$e=!1:C({dragNode:Me,dropNode:Ze,dropPosition:0})?U=0:C({dragNode:Me,dropNode:Ze,dropPosition:1})?U=1:$e=!1:C({dragNode:Me,dropNode:Ze,dropPosition:1})?U=1:$e=!1,{dropPosition:U,dropLevelOffset:Ie,dropTargetKey:ee.key,dropTargetPos:ee.pos,dragOverNodeKey:o,dropContainerKey:U===0?null:((ce=ee.parent)===null||ce===void 0?void 0:ce.key)||null,dropAllowed:$e}}function l(n,t){if(n){var v=t.multiple;return v?n.slice():n.length?[n[0]]:n}}var s=function(t){return t};function y(n,t){if(!n)return[];var v=t||{},m=v.processProps,p=m===void 0?s:m,C=Array.isArray(n)?n:[n];return C.map(function(x){var L=x.children,K=_objectWithoutProperties(x,Q),_=y(L,t);return React.createElement(TreeNode,_extends({key:K.key},p(K)),_)})}function M(n){if(!n)return null;var t;if(Array.isArray(n))t={checkedKeys:n,halfCheckedKeys:void 0};else if((0,k.Z)(n)==="object")t={checkedKeys:n.checked||void 0,halfCheckedKeys:n.halfChecked||void 0};else return(0,A.ZP)(!1,"`checkedKeys` is not an array or an object"),null;return t}function I(n,t){var v=new Set;function m(p){if(!v.has(p)){var C=(0,Z.Z)(t,p);if(C){v.add(p);var x=C.parent,L=C.node;L.disabled||x&&m(x.key)}}}return(n||[]).forEach(function(p){m(p)}),(0,$.Z)(v)}},17341:function(Le,ye,d){d.d(ye,{S:function(){return D}});var $=d(80334),k=d(35381);function A(Q,ie){var f=new Set;return Q.forEach(function(oe){ie.has(oe)||f.add(oe)}),f}function xe(Q){var ie=Q||{},f=ie.disabled,oe=ie.disableCheckbox,z=ie.checkable;return!!(f||oe)||z===!1}function Ke(Q,ie,f,oe){for(var z=new Set(Q),H=new Set,J=0;J<=f;J+=1){var fe=ie.get(J)||new Set;fe.forEach(function(M){var I=M.key,n=M.node,t=M.children,v=t===void 0?[]:t;z.has(I)&&!oe(n)&&v.filter(function(m){return!oe(m.node)}).forEach(function(m){z.add(m.key)})})}for(var l=new Set,s=f;s>=0;s-=1){var y=ie.get(s)||new Set;y.forEach(function(M){var I=M.parent,n=M.node;if(!(oe(n)||!M.parent||l.has(M.parent.key))){if(oe(M.parent.node)){l.add(I.key);return}var t=!0,v=!1;(I.children||[]).filter(function(m){return!oe(m.node)}).forEach(function(m){var p=m.key,C=z.has(p);t&&!C&&(t=!1),!v&&(C||H.has(p))&&(v=!0)}),t&&z.add(I.key),v&&H.add(I.key),l.add(I.key)}})}return{checkedKeys:Array.from(z),halfCheckedKeys:Array.from(A(H,z))}}function Z(Q,ie,f,oe,z){for(var H=new Set(Q),J=new Set(ie),fe=0;fe<=oe;fe+=1){var l=f.get(fe)||new Set;l.forEach(function(I){var n=I.key,t=I.node,v=I.children,m=v===void 0?[]:v;!H.has(n)&&!J.has(n)&&!z(t)&&m.filter(function(p){return!z(p.node)}).forEach(function(p){H.delete(p.key)})})}J=new Set;for(var s=new Set,y=oe;y>=0;y-=1){var M=f.get(y)||new Set;M.forEach(function(I){var n=I.parent,t=I.node;if(!(z(t)||!I.parent||s.has(I.parent.key))){if(z(I.parent.node)){s.add(n.key);return}var v=!0,m=!1;(n.children||[]).filter(function(p){return!z(p.node)}).forEach(function(p){var C=p.key,x=H.has(C);v&&!x&&(v=!1),!m&&(x||J.has(C))&&(m=!0)}),v||H.delete(n.key),m&&J.add(n.key),s.add(n.key)}})}return{checkedKeys:Array.from(H),halfCheckedKeys:Array.from(A(J,H))}}function D(Q,ie,f,oe){var z=[],H;oe?H=oe:H=xe;var J=new Set(Q.filter(function(y){var M=!!(0,k.Z)(f,y);return M||z.push(y),M})),fe=new Map,l=0;Object.keys(f).forEach(function(y){var M=f[y],I=M.level,n=fe.get(I);n||(n=new Set,fe.set(I,n)),n.add(M),l=Math.max(l,I)}),(0,$.ZP)(!z.length,"Tree missing follow keys: ".concat(z.slice(0,100).map(function(y){return"'".concat(y,"'")}).join(", ")));var s;return ie===!0?s=Ke(J,fe,l,H):s=Z(J,ie.halfCheckedKeys,fe,l,H),s}},35381:function(Le,ye,d){d.d(ye,{Z:function(){return $}});function $(k,A){return k[A]}},1089:function(Le,ye,d){d.d(ye,{F:function(){return I},H8:function(){return M},I8:function(){return y},km:function(){return z},oH:function(){return l},w$:function(){return H},zn:function(){return fe}});var $=d(71002),k=d(74902),A=d(1413),xe=d(91),Ke=d(50344),Z=d(98423),D=d(80334),Q=d(35381),ie=["children"];function f(n,t){return"".concat(n,"-").concat(t)}function oe(n){return n&&n.type&&n.type.isTreeNode}function z(n,t){return n!=null?n:t}function H(n){var t=n||{},v=t.title,m=t._title,p=t.key,C=t.children,x=v||"title";return{title:x,_title:m||[x],key:p||"key",children:C||"children"}}function J(n,t){var v=new Map;function m(p){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";(p||[]).forEach(function(x){var L=x[t.key],K=x[t.children];warning(L!=null,"Tree node must have a certain key: [".concat(C).concat(L,"]"));var _=String(L);warning(!v.has(_)||L===null||L===void 0,"Same 'key' exist in the Tree: ".concat(_)),v.set(_,!0),m(K,"".concat(C).concat(_," > "))})}m(n)}function fe(n){function t(v){var m=(0,Ke.Z)(v);return m.map(function(p){if(!oe(p))return(0,D.ZP)(!p,"Tree/TreeNode can only accept TreeNode as children."),null;var C=p.key,x=p.props,L=x.children,K=(0,xe.Z)(x,ie),_=(0,A.Z)({key:C},K),ce=t(L);return ce.length&&(_.children=ce),_}).filter(function(p){return p})}return t(n)}function l(n,t,v){var m=H(v),p=m._title,C=m.key,x=m.children,L=new Set(t===!0?[]:t),K=[];function _(ce){var de=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return ce.map(function(ae,se){for(var B=f(de?de.pos:"0",se),ue=z(ae[C],B),q,le=0;le<p.length;le+=1){var ve=p[le];if(ae[ve]!==void 0){q=ae[ve];break}}var ee=Object.assign((0,Z.Z)(ae,[].concat((0,k.Z)(p),[C,x])),{title:q,key:ue,parent:de,pos:B,children:null,data:ae,isStart:[].concat((0,k.Z)(de?de.isStart:[]),[se===0]),isEnd:[].concat((0,k.Z)(de?de.isEnd:[]),[se===ce.length-1])});return K.push(ee),t===!0||L.has(ue)?ee.children=_(ae[x]||[],ee):ee.children=[],ee})}return _(n),K}function s(n,t,v){var m={};(0,$.Z)(v)==="object"?m=v:m={externalGetKey:v},m=m||{};var p=m,C=p.childrenPropName,x=p.externalGetKey,L=p.fieldNames,K=H(L),_=K.key,ce=K.children,de=C||ce,ae;x?typeof x=="string"?ae=function(ue){return ue[x]}:typeof x=="function"&&(ae=function(ue){return x(ue)}):ae=function(ue,q){return z(ue[_],q)};function se(B,ue,q,le){var ve=B?B[de]:n,ee=B?f(q.pos,ue):"0",ke=B?[].concat((0,k.Z)(le),[B]):[];if(B){var ge=ae(B,ee),we={node:B,index:ue,pos:ee,key:ge,parentPos:q.node?q.pos:null,level:q.level+1,nodes:ke};t(we)}ve&&ve.forEach(function(Oe,Fe){se(Oe,Fe,{node:B,pos:ee,level:q?q.level+1:-1},ke)})}se(null)}function y(n){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},v=t.initWrapper,m=t.processEntity,p=t.onProcessFinished,C=t.externalGetKey,x=t.childrenPropName,L=t.fieldNames,K=arguments.length>2?arguments[2]:void 0,_=C||K,ce={},de={},ae={posEntities:ce,keyEntities:de};return v&&(ae=v(ae)||ae),s(n,function(se){var B=se.node,ue=se.index,q=se.pos,le=se.key,ve=se.parentPos,ee=se.level,ke=se.nodes,ge={node:B,nodes:ke,index:ue,key:le,pos:q,level:ee},we=z(le,q);ce[q]=ge,de[we]=ge,ge.parent=ce[ve],ge.parent&&(ge.parent.children=ge.parent.children||[],ge.parent.children.push(ge)),m&&m(ge,ae)},{externalGetKey:_,childrenPropName:x,fieldNames:L}),p&&p(ae),ae}function M(n,t){var v=t.expandedKeys,m=t.selectedKeys,p=t.loadedKeys,C=t.loadingKeys,x=t.checkedKeys,L=t.halfCheckedKeys,K=t.dragOverNodeKey,_=t.dropPosition,ce=t.keyEntities,de=(0,Q.Z)(ce,n),ae={eventKey:n,expanded:v.indexOf(n)!==-1,selected:m.indexOf(n)!==-1,loaded:p.indexOf(n)!==-1,loading:C.indexOf(n)!==-1,checked:x.indexOf(n)!==-1,halfChecked:L.indexOf(n)!==-1,pos:String(de?de.pos:""),dragOver:K===n&&_===0,dragOverGapTop:K===n&&_===-1,dragOverGapBottom:K===n&&_===1};return ae}function I(n){var t=n.data,v=n.expanded,m=n.selected,p=n.checked,C=n.loaded,x=n.loading,L=n.halfChecked,K=n.dragOver,_=n.dragOverGapTop,ce=n.dragOverGapBottom,de=n.pos,ae=n.active,se=n.eventKey,B=(0,A.Z)((0,A.Z)({},t),{},{expanded:v,selected:m,checked:p,loaded:C,loading:x,halfChecked:L,dragOver:K,dragOverGapTop:_,dragOverGapBottom:ce,pos:de,active:ae,key:se});return"props"in B||Object.defineProperty(B,"props",{get:function(){return(0,D.ZP)(!1,"Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`."),n}}),B}},64019:function(Le,ye,d){d.d(ye,{Z:function(){return k}});var $=d(73935);function k(A,xe,Ke,Z){var D=$.unstable_batchedUpdates?function(ie){$.unstable_batchedUpdates(Ke,ie)}:Ke;return A!=null&&A.addEventListener&&A.addEventListener(xe,D,Z),{remove:function(){A!=null&&A.removeEventListener&&A.removeEventListener(xe,D,Z)}}}}}]);
