(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1471],{27363:function(te,w){"use strict";var l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};w.Z=l},71471:function(te,w,l){"use strict";l.d(w,{Z:function(){return Vt}});var o=l(67294),N=l(74902),k=l(87462),le=l(27363),B=l(93771),E=function(n,r){return o.createElement(B.Z,(0,k.Z)({},n,{ref:r,icon:le.Z}))},h=o.forwardRef(E),T=h,J=l(93967),ee=l.n(J),ne=l(9220),F=l(50344),g=l(8410),oe=l(21770),de=l(98423),Z=l(42550),re=l(79370),Pe=l(53124),Ke=l(10110),be=l(83062),Xe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"},Ge=Xe,Je=function(n,r){return o.createElement(B.Z,(0,k.Z)({},n,{ref:r,icon:Ge}))},Qe=o.forwardRef(Je),Ye=Qe,$e=l(15105),qe=l(96159),_e=l(2961),Me=l(14747),et=l(83559),tt=l(65409),nt=l(11568);const ot=(e,n,r,t)=>{const{titleMarginBottom:s,fontWeightStrong:a}=t;return{marginBottom:s,color:r,fontWeight:a,fontSize:e,lineHeight:n}},rt=e=>{const n=[1,2,3,4,5],r={};return n.forEach(t=>{r[`
      h${t}&,
      div&-h${t},
      div&-h${t} > textarea,
      h${t}
    `]=ot(e[`fontSizeHeading${t}`],e[`lineHeightHeading${t}`],e.colorTextHeading,e)}),r},st=e=>{const{componentCls:n}=e;return{"a&, a":Object.assign(Object.assign({},(0,Me.Nd)(e)),{userSelect:"text",[`&[disabled], &${n}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},lt=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:tt.EV[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:600},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),it=e=>{const{componentCls:n,paddingSM:r}=e,t=r;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:e.calc(e.paddingSM).mul(-1).equal(),marginTop:e.calc(t).mul(-1).equal(),marginBottom:`calc(1em - ${(0,nt.bf)(t)})`},[`${n}-edit-content-confirm`]:{position:"absolute",insetInlineEnd:e.calc(e.marginXS).add(2).equal(),insetBlockEnd:e.marginXS,color:e.colorIcon,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},at=e=>({[`${e.componentCls}-copy-success`]:{"\n    &,\n    &:hover,\n    &:focus":{color:e.colorSuccess}},[`${e.componentCls}-copy-icon-only`]:{marginInlineStart:0}}),ct=()=>({"\n  a&-ellipsis,\n  span&-ellipsis\n  ":{display:"inline-block",maxWidth:"100%"},"&-ellipsis-single-line":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"},"> code":{paddingBlock:0,maxWidth:"calc(100% - 1.2em)",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"bottom",boxSizing:"content-box"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),ut=e=>{const{componentCls:n,titleMarginTop:r}=e;return{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,[`&${n}-secondary`]:{color:e.colorTextDescription},[`&${n}-success`]:{color:e.colorSuccessText},[`&${n}-warning`]:{color:e.colorWarningText},[`&${n}-danger`]:{color:e.colorErrorText,"a&:active, a&:focus":{color:e.colorErrorTextActive},"a&:hover":{color:e.colorErrorTextHover}},[`&${n}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},"\n        div&,\n        p\n      ":{marginBottom:"1em"}},rt(e)),{[`
      & + h1${n},
      & + h2${n},
      & + h3${n},
      & + h4${n},
      & + h5${n}
      `]:{marginTop:r},"\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5":{"\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        ":{marginTop:r}}}),lt(e)),st(e)),{[`
        ${n}-expand,
        ${n}-collapse,
        ${n}-edit,
        ${n}-copy
      `]:Object.assign(Object.assign({},(0,Me.Nd)(e)),{marginInlineStart:e.marginXXS})}),it(e)),at(e)),ct()),{"&-rtl":{direction:"rtl"}})}},dt=()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"});var Ae=(0,et.I$)("Typography",e=>[ut(e)],dt),pt=e=>{const{prefixCls:n,"aria-label":r,className:t,style:s,direction:a,maxLength:y,autoSize:S=!0,value:c,onSave:u,onCancel:p,onEnd:b,component:f,enterIcon:L=o.createElement(Ye,null)}=e,P=o.useRef(null),$=o.useRef(!1),H=o.useRef(null),[m,W]=o.useState(c);o.useEffect(()=>{W(c)},[c]),o.useEffect(()=>{var v;if(!((v=P.current)===null||v===void 0)&&v.resizableTextArea){const{textArea:C}=P.current.resizableTextArea;C.focus();const{length:O}=C.value;C.setSelectionRange(O,O)}},[]);const Q=({target:v})=>{W(v.value.replace(/[\n\r]/g,""))},R=()=>{$.current=!0},j=()=>{$.current=!1},A=({keyCode:v})=>{$.current||(H.current=v)},x=()=>{u(m.trim())},Y=({keyCode:v,ctrlKey:C,altKey:O,metaKey:X,shiftKey:M})=>{H.current!==v||$.current||C||O||X||M||(v===$e.Z.ENTER?(x(),b==null||b()):v===$e.Z.ESC&&p())},ae=()=>{x()},[se,V,ce]=Ae(n),K=ee()(n,`${n}-edit-content`,{[`${n}-rtl`]:a==="rtl",[`${n}-${f}`]:!!f},t,V,ce);return se(o.createElement("div",{className:K,style:s},o.createElement(_e.Z,{ref:P,maxLength:y,value:m,onChange:Q,onKeyDown:A,onKeyUp:Y,onCompositionStart:R,onCompositionEnd:j,onBlur:ae,"aria-label":r,rows:1,autoSize:S}),L!==null?(0,qe.Tm)(L,{className:`${n}-edit-content-confirm`}):null))},ft=l(20640),mt=l.n(ft),gt=l(66680),yt=(e,n=!1)=>n&&e==null?[]:Array.isArray(e)?e:[e],vt=function(e,n,r,t){function s(a){return a instanceof r?a:new r(function(y){y(a)})}return new(r||(r=Promise))(function(a,y){function S(p){try{u(t.next(p))}catch(b){y(b)}}function c(p){try{u(t.throw(p))}catch(b){y(b)}}function u(p){p.done?a(p.value):s(p.value).then(S,c)}u((t=t.apply(e,n||[])).next())})},bt=({copyConfig:e,children:n})=>{const[r,t]=o.useState(!1),[s,a]=o.useState(!1),y=o.useRef(null),S=()=>{y.current&&clearTimeout(y.current)},c={};e.format&&(c.format=e.format),o.useEffect(()=>S,[]);const u=(0,gt.Z)(p=>vt(void 0,void 0,void 0,function*(){var b;p==null||p.preventDefault(),p==null||p.stopPropagation(),a(!0);try{const f=typeof e.text=="function"?yield e.text():e.text;mt()(f||yt(n,!0).join("")||"",c),a(!1),t(!0),S(),y.current=setTimeout(()=>{t(!1)},3e3),(b=e.onCopy)===null||b===void 0||b.call(e,p)}catch(f){throw a(!1),f}}));return{copied:r,copyLoading:s,onClick:u}};function Ee(e,n){return o.useMemo(()=>{const r=!!e;return[r,Object.assign(Object.assign({},n),r&&typeof e=="object"?e:null)]},[e])}var Et=e=>{const n=(0,o.useRef)(void 0);return(0,o.useEffect)(()=>{n.current=e}),n.current},ht=(e,n,r)=>(0,o.useMemo)(()=>e===!0?{title:n!=null?n:r}:(0,o.isValidElement)(e)?{title:e}:typeof e=="object"?Object.assign({title:n!=null?n:r},e):{title:e},[e,n,r]),xt=function(e,n){var r={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(r[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,t=Object.getOwnPropertySymbols(e);s<t.length;s++)n.indexOf(t[s])<0&&Object.prototype.propertyIsEnumerable.call(e,t[s])&&(r[t[s]]=e[t[s]]);return r},De=o.forwardRef((e,n)=>{const{prefixCls:r,component:t="article",className:s,rootClassName:a,setContentRef:y,children:S,direction:c,style:u}=e,p=xt(e,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:b,direction:f,className:L,style:P}=(0,Pe.dj)("typography"),$=c!=null?c:f,H=y?(0,Z.sQ)(n,y):n,m=b("typography",r),[W,Q,R]=Ae(m),j=ee()(m,L,{[`${m}-rtl`]:$==="rtl"},s,a,Q,R),A=Object.assign(Object.assign({},P),u);return W(o.createElement(t,Object.assign({className:j,style:A,ref:H},p),S))}),St=l(35918),Ot=l(48820),Ct=function(n,r){return o.createElement(B.Z,(0,k.Z)({},n,{ref:r,icon:Ot.Z}))},wt=o.forwardRef(Ct),Tt=wt,Rt=l(19267);function Ne(e){return e===!1?[!1,!1]:Array.isArray(e)?e:[e]}function he(e,n,r){return e===!0||e===void 0?n:e||r&&n}function jt(e){const n=document.createElement("em");e.appendChild(n);const r=e.getBoundingClientRect(),t=n.getBoundingClientRect();return e.removeChild(n),r.left>t.left||t.right>r.right||r.top>t.top||t.bottom>r.bottom}const xe=e=>["string","number"].includes(typeof e);var It=({prefixCls:e,copied:n,locale:r,iconOnly:t,tooltips:s,icon:a,tabIndex:y,onCopy:S,loading:c})=>{const u=Ne(s),p=Ne(a),{copied:b,copy:f}=r!=null?r:{},L=n?b:f,P=he(u[n?1:0],L),$=typeof P=="string"?P:L;return o.createElement(be.Z,{title:P},o.createElement("button",{type:"button",className:ee()(`${e}-copy`,{[`${e}-copy-success`]:n,[`${e}-copy-icon-only`]:t}),onClick:S,"aria-label":$,tabIndex:y},n?he(p[1],o.createElement(St.Z,null),!0):he(p[0],c?o.createElement(Rt.Z,null):o.createElement(Tt,null),!0)))};const pe=o.forwardRef(({style:e,children:n},r)=>{const t=o.useRef(null);return o.useImperativeHandle(r,()=>({isExceed:()=>{const s=t.current;return s.scrollHeight>s.clientHeight},getHeight:()=>t.current.clientHeight})),o.createElement("span",{"aria-hidden":!0,ref:t,style:Object.assign({position:"fixed",display:"block",left:0,top:0,pointerEvents:"none",backgroundColor:"rgba(255, 0, 0, 0.65)"},e)},n)}),Lt=e=>e.reduce((n,r)=>n+(xe(r)?String(r).length:1),0);function Ze(e,n){let r=0;const t=[];for(let s=0;s<e.length;s+=1){if(r===n)return t;const a=e[s],S=xe(a)?String(a).length:1,c=r+S;if(c>n){const u=n-r;return t.push(String(a).slice(0,u)),t}t.push(a),r=c}return e}const Se=0,Oe=1,Ce=2,we=3,Be=4,fe={display:"-webkit-box",overflow:"hidden",WebkitBoxOrient:"vertical"};function Pt(e){const{enableMeasure:n,width:r,text:t,children:s,rows:a,expanded:y,miscDeps:S,onEllipsis:c}=e,u=o.useMemo(()=>(0,F.Z)(t),[t]),p=o.useMemo(()=>Lt(u),[t]),b=o.useMemo(()=>s(u,!1),[t]),[f,L]=o.useState(null),P=o.useRef(null),$=o.useRef(null),H=o.useRef(null),m=o.useRef(null),W=o.useRef(null),[Q,R]=o.useState(!1),[j,A]=o.useState(Se),[x,Y]=o.useState(0),[ae,se]=o.useState(null);(0,g.Z)(()=>{A(n&&r&&p?Oe:Se)},[r,t,a,n,u]),(0,g.Z)(()=>{var v,C,O,X;if(j===Oe){A(Ce);const M=$.current&&getComputedStyle($.current).whiteSpace;se(M)}else if(j===Ce){const M=!!(!((v=H.current)===null||v===void 0)&&v.isExceed());A(M?we:Be),L(M?[0,p]:null),R(M);const q=((C=H.current)===null||C===void 0?void 0:C.getHeight())||0,Te=a===1?0:((O=m.current)===null||O===void 0?void 0:O.getHeight())||0,ge=((X=W.current)===null||X===void 0?void 0:X.getHeight())||0,Re=Math.max(q,Te+ge);Y(Re+1),c(M)}},[j]);const V=f?Math.ceil((f[0]+f[1])/2):0;(0,g.Z)(()=>{var v;const[C,O]=f||[0,0];if(C!==O){const M=(((v=P.current)===null||v===void 0?void 0:v.getHeight())||0)>x;let q=V;O-C===1&&(q=M?C:O),L(M?[C,q]:[q,O])}},[f,V]);const ce=o.useMemo(()=>{if(!n)return s(u,!1);if(j!==we||!f||f[0]!==f[1]){const v=s(u,!1);return[Be,Se].includes(j)?v:o.createElement("span",{style:Object.assign(Object.assign({},fe),{WebkitLineClamp:a})},v)}return s(y?u:Ze(u,f[0]),Q)},[y,j,f,u].concat((0,N.Z)(S))),K={width:r,margin:0,padding:0,whiteSpace:ae==="nowrap"?"normal":"inherit"};return o.createElement(o.Fragment,null,ce,j===Ce&&o.createElement(o.Fragment,null,o.createElement(pe,{style:Object.assign(Object.assign(Object.assign({},K),fe),{WebkitLineClamp:a}),ref:H},b),o.createElement(pe,{style:Object.assign(Object.assign(Object.assign({},K),fe),{WebkitLineClamp:a-1}),ref:m},b),o.createElement(pe,{style:Object.assign(Object.assign(Object.assign({},K),fe),{WebkitLineClamp:1}),ref:W},s([],!0))),j===we&&f&&f[0]!==f[1]&&o.createElement(pe,{style:Object.assign(Object.assign({},K),{top:400}),ref:P},s(Ze(u,V),!0)),j===Oe&&o.createElement("span",{style:{whiteSpace:"inherit"},ref:$}))}var $t=({enableEllipsis:e,isEllipsis:n,children:r,tooltipProps:t})=>!(t!=null&&t.title)||!e?r:o.createElement(be.Z,Object.assign({open:n?void 0:!1},t),r),Mt=function(e,n){var r={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(r[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,t=Object.getOwnPropertySymbols(e);s<t.length;s++)n.indexOf(t[s])<0&&Object.prototype.propertyIsEnumerable.call(e,t[s])&&(r[t[s]]=e[t[s]]);return r};function At({mark:e,code:n,underline:r,delete:t,strong:s,keyboard:a,italic:y},S){let c=S;function u(p,b){b&&(c=o.createElement(p,{},c))}return u("strong",s),u("u",r),u("del",t),u("code",n),u("mark",e),u("kbd",a),u("i",y),c}const Dt="...",He=["delete","mark","code","underline","strong","keyboard","italic"];var me=o.forwardRef((e,n)=>{var r;const{prefixCls:t,className:s,style:a,type:y,disabled:S,children:c,ellipsis:u,editable:p,copyable:b,component:f,title:L}=e,P=Mt(e,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:$,direction:H}=o.useContext(Pe.E_),[m]=(0,Ke.Z)("Text"),W=o.useRef(null),Q=o.useRef(null),R=$("typography",t),j=(0,de.Z)(P,He),[A,x]=Ee(p),[Y,ae]=(0,oe.Z)(!1,{value:x.editing}),{triggerType:se=["icon"]}=x,V=i=>{var d;i&&((d=x.onStart)===null||d===void 0||d.call(x)),ae(i)},ce=Et(Y);(0,g.Z)(()=>{var i;!Y&&ce&&((i=Q.current)===null||i===void 0||i.focus())},[Y]);const K=i=>{i==null||i.preventDefault(),V(!0)},v=i=>{var d;(d=x.onChange)===null||d===void 0||d.call(x,i),V(!1)},C=()=>{var i;(i=x.onCancel)===null||i===void 0||i.call(x),V(!1)},[O,X]=Ee(b),{copied:M,copyLoading:q,onClick:Te}=bt({copyConfig:X,children:c}),[ge,Re]=o.useState(!1),[We,Kt]=o.useState(!1),[ze,Xt]=o.useState(!1),[Ue,Gt]=o.useState(!1),[Jt,Qt]=o.useState(!0),[_,I]=Ee(u,{expandable:!1,symbol:i=>i?m==null?void 0:m.collapse:m==null?void 0:m.expand}),[G,Yt]=(0,oe.Z)(I.defaultExpanded||!1,{value:I.expanded}),D=_&&(!G||I.expandable==="collapsible"),{rows:ue=1}=I,ye=o.useMemo(()=>D&&(I.suffix!==void 0||I.onEllipsis||I.expandable||A||O),[D,I,A,O]);(0,g.Z)(()=>{_&&!ye&&(Re((0,re.G)("webkitLineClamp")),Kt((0,re.G)("textOverflow")))},[ye,_]);const[z,qt]=o.useState(D),ke=o.useMemo(()=>ye?!1:ue===1?We:ge,[ye,We,ge]);(0,g.Z)(()=>{qt(ke&&D)},[ke,D]);const Fe=D&&(z?Ue:ze),_t=D&&ue===1&&z,je=D&&ue>1&&z,en=(i,d)=>{var U;Yt(d.expanded),(U=I.onExpand)===null||U===void 0||U.call(I,i,d)},[Ve,tn]=o.useState(0),nn=({offsetWidth:i})=>{tn(i)},on=i=>{var d;Xt(i),ze!==i&&((d=I.onEllipsis)===null||d===void 0||d.call(I,i))};o.useEffect(()=>{const i=W.current;if(_&&z&&i){const d=jt(i);Ue!==d&&Gt(d)}},[_,z,c,je,Jt,Ve]),o.useEffect(()=>{const i=W.current;if(typeof IntersectionObserver=="undefined"||!i||!z||!D)return;const d=new IntersectionObserver(()=>{Qt(!!i.offsetParent)});return d.observe(i),()=>{d.disconnect()}},[z,D]);const Ie=ht(I.tooltip,x.text,c),ve=o.useMemo(()=>{if(!(!_||z))return[x.text,c,L,Ie.title].find(xe)},[_,z,L,Ie.title,Fe]);if(Y)return o.createElement(pt,{value:(r=x.text)!==null&&r!==void 0?r:typeof c=="string"?c:"",onSave:v,onCancel:C,onEnd:x.onEnd,prefixCls:R,className:s,style:a,direction:H,component:f,maxLength:x.maxLength,autoSize:x.autoSize,enterIcon:x.enterIcon});const rn=()=>{const{expandable:i,symbol:d}=I;return i?o.createElement("button",{type:"button",key:"expand",className:`${R}-${G?"collapse":"expand"}`,onClick:U=>en(U,{expanded:!G}),"aria-label":G?m.collapse:m==null?void 0:m.expand},typeof d=="function"?d(G):d):null},sn=()=>{if(!A)return;const{icon:i,tooltip:d,tabIndex:U}=x,Le=(0,F.Z)(d)[0]||(m==null?void 0:m.edit),un=typeof Le=="string"?Le:"";return se.includes("icon")?o.createElement(be.Z,{key:"edit",title:d===!1?"":Le},o.createElement("button",{type:"button",ref:Q,className:`${R}-edit`,onClick:K,"aria-label":un,tabIndex:U},i||o.createElement(T,{role:"button"}))):null},ln=()=>O?o.createElement(It,Object.assign({key:"copy"},X,{prefixCls:R,copied:M,locale:m,onCopy:Te,loading:q,iconOnly:c==null})):null,an=i=>[i&&rn(),sn(),ln()],cn=i=>[i&&!G&&o.createElement("span",{"aria-hidden":!0,key:"ellipsis"},Dt),I.suffix,an(i)];return o.createElement(ne.Z,{onResize:nn,disabled:!D},i=>o.createElement($t,{tooltipProps:Ie,enableEllipsis:D,isEllipsis:Fe},o.createElement(De,Object.assign({className:ee()({[`${R}-${y}`]:y,[`${R}-disabled`]:S,[`${R}-ellipsis`]:_,[`${R}-ellipsis-single-line`]:_t,[`${R}-ellipsis-multiple-line`]:je},s),prefixCls:t,style:Object.assign(Object.assign({},a),{WebkitLineClamp:je?ue:void 0}),component:f,ref:(0,Z.sQ)(i,W,n),direction:H,onClick:se.includes("text")?K:void 0,"aria-label":ve==null?void 0:ve.toString(),title:L},j),o.createElement(Pt,{enableMeasure:D&&!z,text:c,rows:ue,width:Ve,onEllipsis:on,expanded:G,miscDeps:[M,G,q,A,O,m].concat((0,N.Z)(He.map(d=>e[d])))},(d,U)=>At(e,o.createElement(o.Fragment,null,d.length>0&&U&&!G&&ve?o.createElement("span",{key:"show-content","aria-hidden":!0},d):d,cn(U)))))))}),Nt=function(e,n){var r={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(r[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,t=Object.getOwnPropertySymbols(e);s<t.length;s++)n.indexOf(t[s])<0&&Object.prototype.propertyIsEnumerable.call(e,t[s])&&(r[t[s]]=e[t[s]]);return r},Zt=o.forwardRef((e,n)=>{var{ellipsis:r,rel:t}=e,s=Nt(e,["ellipsis","rel"]);const a=Object.assign(Object.assign({},s),{rel:t===void 0&&s.target==="_blank"?"noopener noreferrer":t});return delete a.navigate,o.createElement(me,Object.assign({},a,{ref:n,ellipsis:!!r,component:"a"}))}),Bt=o.forwardRef((e,n)=>o.createElement(me,Object.assign({ref:n},e,{component:"div"}))),Ht=function(e,n){var r={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(r[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,t=Object.getOwnPropertySymbols(e);s<t.length;s++)n.indexOf(t[s])<0&&Object.prototype.propertyIsEnumerable.call(e,t[s])&&(r[t[s]]=e[t[s]]);return r};const Wt=(e,n)=>{var{ellipsis:r}=e,t=Ht(e,["ellipsis"]);const s=o.useMemo(()=>r&&typeof r=="object"?(0,de.Z)(r,["expandable","rows"]):r,[r]);return o.createElement(me,Object.assign({ref:n},t,{ellipsis:s,component:"span"}))};var zt=o.forwardRef(Wt),Ut=function(e,n){var r={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(r[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,t=Object.getOwnPropertySymbols(e);s<t.length;s++)n.indexOf(t[s])<0&&Object.prototype.propertyIsEnumerable.call(e,t[s])&&(r[t[s]]=e[t[s]]);return r};const kt=[1,2,3,4,5];var Ft=o.forwardRef((e,n)=>{const{level:r=1}=e,t=Ut(e,["level"]),s=kt.includes(r)?`h${r}`:"h1";return o.createElement(me,Object.assign({ref:n},t,{component:s}))});const ie=De;ie.Text=zt,ie.Link=Zt,ie.Title=Ft,ie.Paragraph=Bt;var Vt=ie},20640:function(te,w,l){"use strict";var o=l(11742),N={"text/plain":"Text","text/html":"Url",default:"Text"},k="Copy to clipboard: #{key}, Enter";function le(E){var h=(/mac os x/i.test(navigator.userAgent)?"\u2318":"Ctrl")+"+C";return E.replace(/#{\s*key\s*}/g,h)}function B(E,h){var T,J,ee,ne,F,g,oe=!1;h||(h={}),T=h.debug||!1;try{ee=o(),ne=document.createRange(),F=document.getSelection(),g=document.createElement("span"),g.textContent=E,g.ariaHidden="true",g.style.all="unset",g.style.position="fixed",g.style.top=0,g.style.clip="rect(0, 0, 0, 0)",g.style.whiteSpace="pre",g.style.webkitUserSelect="text",g.style.MozUserSelect="text",g.style.msUserSelect="text",g.style.userSelect="text",g.addEventListener("copy",function(Z){if(Z.stopPropagation(),h.format)if(Z.preventDefault(),typeof Z.clipboardData=="undefined"){T&&console.warn("unable to use e.clipboardData"),T&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var re=N[h.format]||N.default;window.clipboardData.setData(re,E)}else Z.clipboardData.clearData(),Z.clipboardData.setData(h.format,E);h.onCopy&&(Z.preventDefault(),h.onCopy(Z.clipboardData))}),document.body.appendChild(g),ne.selectNodeContents(g),F.addRange(ne);var de=document.execCommand("copy");if(!de)throw new Error("copy command was unsuccessful");oe=!0}catch(Z){T&&console.error("unable to copy using execCommand: ",Z),T&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(h.format||"text",E),h.onCopy&&h.onCopy(window.clipboardData),oe=!0}catch(re){T&&console.error("unable to copy using clipboardData: ",re),T&&console.error("falling back to prompt"),J=le("message"in h?h.message:k),window.prompt(J,E)}}finally{F&&(typeof F.removeRange=="function"?F.removeRange(ne):F.removeAllRanges()),g&&document.body.removeChild(g),ee()}return oe}te.exports=B},79370:function(te,w,l){"use strict";l.d(w,{G:function(){return le}});var o=l(98924),N=function(E){if((0,o.Z)()&&window.document.documentElement){var h=Array.isArray(E)?E:[E],T=window.document.documentElement;return h.some(function(J){return J in T.style})}return!1},k=function(E,h){if(!N(E))return!1;var T=document.createElement("div"),J=T.style[E];return T.style[E]=h,T.style[E]!==J};function le(B,E){return!Array.isArray(B)&&E!==void 0?k(B,E):N(B)}},11742:function(te){te.exports=function(){var w=document.getSelection();if(!w.rangeCount)return function(){};for(var l=document.activeElement,o=[],N=0;N<w.rangeCount;N++)o.push(w.getRangeAt(N));switch(l.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":l.blur();break;default:l=null;break}return w.removeAllRanges(),function(){w.type==="Caret"&&w.removeAllRanges(),w.rangeCount||o.forEach(function(k){w.addRange(k)}),l&&l.focus()}}}}]);
