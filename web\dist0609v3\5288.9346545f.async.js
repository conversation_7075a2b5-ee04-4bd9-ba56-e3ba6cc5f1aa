(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5288],{47046:function(b,o){"use strict";var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"};o.Z=e},75573:function(b,o){"use strict";var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"};o.Z=e},39055:function(b,o){"use strict";var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"};o.Z=e},38095:function(b,o,e){"use strict";e.d(o,{Z:function(){return i}});var n=e(1413),r=e(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M117 368h231v64H117zm559 0h241v64H676zm-264 0h200v64H412zm0 224h200v64H412zm264 0h241v64H676zm-559 0h231v64H117zm295-160V179h-64v666h64V592zm264-64V179h-64v666h64V432z"}}]},name:"borderless-table",theme:"outlined"},s=a,l=e(91146),c=function(m,v){return r.createElement(l.Z,(0,n.Z)((0,n.Z)({},m),{},{ref:v,icon:s}))},f=r.forwardRef(c),i=f},89035:function(b,o,e){"use strict";e.d(o,{Z:function(){return i}});var n=e(1413),r=e(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M516 673c0 4.4 3.4 8 7.5 8h185c4.1 0 7.5-3.6 7.5-8v-48c0-4.4-3.4-8-7.5-8h-185c-4.1 0-7.5 3.6-7.5 8v48zm-194.9 6.1l192-161c3.8-3.2 3.8-9.1 0-12.3l-192-160.9A7.95 7.95 0 00308 351v62.7c0 2.4 1 4.6 2.9 6.1L420.7 512l-109.8 92.2a8.1 8.1 0 00-2.9 6.1V673c0 6.8 7.9 10.5 13.1 6.1zM880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"code",theme:"outlined"},s=a,l=e(91146),c=function(m,v){return r.createElement(l.Z,(0,n.Z)((0,n.Z)({},m),{},{ref:v,icon:s}))},f=r.forwardRef(c),i=f},85175:function(b,o,e){"use strict";var n=e(1413),r=e(67294),a=e(48820),s=e(91146),l=function(i,h){return r.createElement(s.Z,(0,n.Z)((0,n.Z)({},i),{},{ref:h,icon:a.Z}))},c=r.forwardRef(l);o.Z=c},82061:function(b,o,e){"use strict";var n=e(1413),r=e(67294),a=e(47046),s=e(91146),l=function(i,h){return r.createElement(s.Z,(0,n.Z)((0,n.Z)({},i),{},{ref:h,icon:a.Z}))},c=r.forwardRef(l);o.Z=c},50336:function(b,o,e){"use strict";e.d(o,{Z:function(){return i}});var n=e(1413),r=e(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M476 399.1c0-3.9-3.1-7.1-7-7.1h-42c-3.8 0-7 3.2-7 7.1V484h-84.5c-4.1 0-7.5 3.1-7.5 7v42c0 3.8 3.4 7 7.5 7H420v84.9c0 3.9 3.2 7.1 7 7.1h42c3.9 0 7-3.2 7-7.1V540h84.5c4.1 0 7.5-3.2 7.5-7v-42c0-3.9-3.4-7-7.5-7H476v-84.9zM560.5 704h-225c-4.1 0-7.5 3.2-7.5 7v42c0 3.8 3.4 7 7.5 7h225c4.1 0 7.5-3.2 7.5-7v-42c0-3.8-3.4-7-7.5-7zm-7.1-502.6c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v704c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V397.3c0-8.5-3.4-16.6-9.4-22.6L553.4 201.4zM664 888H232V264h282.2L664 413.8V888zm190.2-581.4L611.3 72.9c-6-5.7-13.9-8.9-22.2-8.9H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h277l219 210.6V824c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V329.6c0-8.7-3.5-17-9.8-23z"}}]},name:"diff",theme:"outlined"},s=a,l=e(91146),c=function(m,v){return r.createElement(l.Z,(0,n.Z)((0,n.Z)({},m),{},{ref:v,icon:s}))},f=r.forwardRef(c),i=f},34804:function(b,o,e){"use strict";var n=e(1413),r=e(67294),a=e(66023),s=e(91146),l=function(i,h){return r.createElement(s.Z,(0,n.Z)((0,n.Z)({},i),{},{ref:h,icon:a.Z}))},c=r.forwardRef(l);o.Z=c},69753:function(b,o,e){"use strict";var n=e(1413),r=e(67294),a=e(49495),s=e(91146),l=function(i,h){return r.createElement(s.Z,(0,n.Z)((0,n.Z)({},i),{},{ref:h,icon:a.Z}))},c=r.forwardRef(l);o.Z=c},55725:function(b,o,e){"use strict";var n=e(1413),r=e(67294),a=e(85118),s=e(91146),l=function(i,h){return r.createElement(s.Z,(0,n.Z)((0,n.Z)({},i),{},{ref:h,icon:a.Z}))},c=r.forwardRef(l);o.Z=c},9641:function(b,o,e){"use strict";e.d(o,{Z:function(){return i}});var n=e(1413),r=e(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M752 100c-61.8 0-112 50.2-112 112 0 47.7 29.9 88.5 72 104.6v27.6L512 601.4 312 344.2v-27.6c42.1-16.1 72-56.9 72-104.6 0-61.8-50.2-112-112-112s-112 50.2-112 112c0 50.6 33.8 93.5 80 107.3v34.4c0 9.7 3.3 19.3 9.3 27L476 672.3v33.6c-44.2 15-76 56.9-76 106.1 0 61.8 50.2 112 112 112s112-50.2 112-112c0-49.2-31.8-91-76-106.1v-33.6l226.7-291.6c6-7.7 9.3-17.3 9.3-27v-34.4c46.2-13.8 80-56.7 80-107.3 0-61.8-50.2-112-112-112zM224 212a48.01 48.01 0 0196 0 48.01 48.01 0 01-96 0zm336 600a48.01 48.01 0 01-96 0 48.01 48.01 0 0196 0zm192-552a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"fork",theme:"outlined"},s=a,l=e(91146),c=function(m,v){return r.createElement(l.Z,(0,n.Z)((0,n.Z)({},m),{},{ref:v,icon:s}))},f=r.forwardRef(c),i=f},64082:function(b,o,e){"use strict";e.d(o,{Z:function(){return i}});var n=e(1413),r=e(67294),a={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M885.2 446.3l-.2-.8-112.2-285.1c-5-16.1-19.9-27.2-36.8-27.2H281.2c-17 0-32.1 11.3-36.9 27.6L139.4 443l-.3.7-.2.8c-1.3 4.9-1.7 9.9-1 14.8-.1 1.6-.2 3.2-.2 4.8V830a60.9 60.9 0 0060.8 60.8h627.2c33.5 0 60.8-27.3 60.9-60.8V464.1c0-1.3 0-2.6-.1-3.7.4-4.9 0-9.6-1.3-14.1zm-295.8-43l-.3 15.7c-.8 44.9-31.8 75.1-77.1 75.1-22.1 0-41.1-7.1-54.8-20.6S436 441.2 435.6 419l-.3-15.7H229.5L309 210h399.2l81.7 193.3H589.4zm-375 76.8h157.3c24.3 57.1 76 90.8 140.4 90.8 33.7 0 65-9.4 90.3-27.2 22.2-15.6 39.5-37.4 50.7-63.6h156.5V814H214.4V480.1z"}}]},name:"inbox",theme:"outlined"},s=a,l=e(91146),c=function(m,v){return r.createElement(l.Z,(0,n.Z)((0,n.Z)({},m),{},{ref:v,icon:s}))},f=r.forwardRef(c),i=f},63572:function(b,o,e){"use strict";e.d(o,{Z:function(){return i}});var n=e(1413),r=e(67294),a={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M416 176H255.54v425.62c0 105.3-36.16 134.71-99.1 134.71-29.5 0-56.05-5.05-76.72-12.14L63 848.79C92.48 858.91 137.73 865 173.13 865 317.63 865 416 797.16 416 602.66zm349.49-16C610.26 160 512 248.13 512 364.6c0 100.32 75.67 163.13 185.7 203.64 79.57 28.36 111.03 53.7 111.03 95.22 0 45.57-36.36 74.96-105.13 74.96-63.87 0-121.85-21.31-161.15-42.58v-.04L512 822.43C549.36 843.73 619.12 865 694.74 865 876.52 865 961 767.75 961 653.3c0-97.25-54.04-160.04-170.94-204.63-86.47-34.44-122.81-53.67-122.81-97.23 0-34.45 31.45-65.84 96.3-65.84 63.83 0 107.73 21.45 133.3 34.64l38.34-128.19C895.1 174.46 841.11 160 765.5 160"}}]},name:"java-script",theme:"outlined"},s=a,l=e(91146),c=function(m,v){return r.createElement(l.Z,(0,n.Z)((0,n.Z)({},m),{},{ref:v,icon:s}))},f=r.forwardRef(c),i=f},94149:function(b,o,e){"use strict";e.d(o,{Z:function(){return i}});var n=e(1413),r=e(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"}}]},name:"lock",theme:"outlined"},s=a,l=e(91146),c=function(m,v){return r.createElement(l.Z,(0,n.Z)((0,n.Z)({},m),{},{ref:v,icon:s}))},f=r.forwardRef(c),i=f},5603:function(b,o,e){"use strict";e.d(o,{Z:function(){return i}});var n=e(1413),r=e(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 112c-3.8 0-7.7.7-11.6 2.3L292 345.9H128c-8.8 0-16 7.4-16 16.6v299c0 9.2 7.2 16.6 16 16.6h101.7c-3.7 11.6-5.7 23.9-5.7 36.4 0 65.9 53.8 119.5 120 119.5 55.4 0 102.1-37.6 115.9-88.4l408.6 164.2c3.9 1.5 7.8 2.3 11.6 2.3 16.9 0 32-14.2 32-33.2V145.2C912 126.2 897 112 880 112zM344 762.3c-26.5 0-48-21.4-48-47.8 0-11.2 3.9-21.9 11-30.4l84.9 34.1c-2 24.6-22.7 44.1-47.9 44.1zm496 58.4L318.8 611.3l-12.9-5.2H184V417.9h121.9l12.9-5.2L840 203.3v617.4z"}}]},name:"notification",theme:"outlined"},s=a,l=e(91146),c=function(m,v){return r.createElement(l.Z,(0,n.Z)((0,n.Z)({},m),{},{ref:v,icon:s}))},f=r.forwardRef(c),i=f},51042:function(b,o,e){"use strict";var n=e(1413),r=e(67294),a=e(42110),s=e(91146),l=function(i,h){return r.createElement(s.Z,(0,n.Z)((0,n.Z)({},i),{},{ref:h,icon:a.Z}))},c=r.forwardRef(l);o.Z=c},14079:function(b,o,e){"use strict";e.d(o,{Z:function(){return i}});var n=e(1413),r=e(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 161H699.2c-49.1 0-97.1 14.1-138.4 40.7L512 233l-48.8-31.3A255.2 255.2 0 00324.8 161H96c-17.7 0-32 14.3-32 32v568c0 17.7 14.3 32 32 32h228.8c49.1 0 97.1 14.1 138.4 40.7l44.4 28.6c1.3.8 2.8 1.3 4.3 1.3s3-.4 4.3-1.3l44.4-28.6C602 807.1 650.1 793 699.2 793H928c17.7 0 32-14.3 32-32V193c0-17.7-14.3-32-32-32zM324.8 721H136V233h188.8c35.4 0 69.8 10.1 99.5 29.2l48.8 31.3 6.9 4.5v462c-47.6-25.6-100.8-39-155.2-39zm563.2 0H699.2c-54.4 0-107.6 13.4-155.2 39V298l6.9-4.5 48.8-31.3c29.7-19.1 64.1-29.2 99.5-29.2H888v488zM396.9 361H211.1c-3.9 0-7.1 3.4-7.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c.1-4.1-3.1-7.5-7-7.5zm223.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c0-4.1-3.2-7.5-7.1-7.5H627.1c-3.9 0-7.1 3.4-7.1 7.5zM396.9 501H211.1c-3.9 0-7.1 3.4-7.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c.1-4.1-3.1-7.5-7-7.5zm416 0H627.1c-3.9 0-7.1 3.4-7.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c.1-4.1-3.1-7.5-7-7.5z"}}]},name:"read",theme:"outlined"},s=a,l=e(91146),c=function(m,v){return r.createElement(l.Z,(0,n.Z)((0,n.Z)({},m),{},{ref:v,icon:s}))},f=r.forwardRef(c),i=f},75750:function(b,o,e){"use strict";e.d(o,{Z:function(){return i}});var n=e(1413),r=e(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"},s=a,l=e(91146),c=function(m,v){return r.createElement(l.Z,(0,n.Z)((0,n.Z)({},m),{},{ref:v,icon:s}))},f=r.forwardRef(c),i=f},41441:function(b,o,e){"use strict";var n=e(1413),r=e(67294),a=e(39055),s=e(91146),l=function(i,h){return r.createElement(s.Z,(0,n.Z)((0,n.Z)({},i),{},{ref:h,icon:a.Z}))},c=r.forwardRef(l);o.Z=c},3355:function(b,o,e){"use strict";e.d(o,{Z:function(){return i}});var n=e(1413),r=e(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 464H332V240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v68c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-68c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zm-40 376H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"}}]},name:"unlock",theme:"outlined"},s=a,l=e(91146),c=function(m,v){return r.createElement(l.Z,(0,n.Z)((0,n.Z)({},m),{},{ref:v,icon:s}))},f=r.forwardRef(c),i=f},88484:function(b,o,e){"use strict";e.d(o,{Z:function(){return i}});var n=e(1413),r=e(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"upload",theme:"outlined"},s=a,l=e(91146),c=function(m,v){return r.createElement(l.Z,(0,n.Z)((0,n.Z)({},m),{},{ref:v,icon:s}))},f=r.forwardRef(c),i=f},15746:function(b,o,e){"use strict";var n=e(21584);o.Z=n.Z},96074:function(b,o,e){"use strict";e.d(o,{Z:function(){return V}});var n=e(67294),r=e(93967),a=e.n(r),s=e(53124),l=e(98675),c=e(11568),f=e(14747),i=e(83559),h=e(83262);const m=g=>{const{componentCls:O}=g;return{[O]:{"&-horizontal":{[`&${O}`]:{"&-sm":{marginBlock:g.marginXS},"&-md":{marginBlock:g.margin}}}}}},v=g=>{const{componentCls:O,sizePaddingEdgeHorizontal:y,colorSplit:E,lineWidth:I,textPaddingInline:w,orientationMargin:R,verticalMarginInline:L}=g;return{[O]:Object.assign(Object.assign({},(0,f.Wf)(g)),{borderBlockStart:`${(0,c.bf)(I)} solid ${E}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:L,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${(0,c.bf)(I)} solid ${E}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${(0,c.bf)(g.marginLG)} 0`},[`&-horizontal${O}-with-text`]:{display:"flex",alignItems:"center",margin:`${(0,c.bf)(g.dividerHorizontalWithTextGutterMargin)} 0`,color:g.colorTextHeading,fontWeight:500,fontSize:g.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${E}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${(0,c.bf)(I)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${O}-with-text-start`]:{"&::before":{width:`calc(${R} * 100%)`},"&::after":{width:`calc(100% - ${R} * 100%)`}},[`&-horizontal${O}-with-text-end`]:{"&::before":{width:`calc(100% - ${R} * 100%)`},"&::after":{width:`calc(${R} * 100%)`}},[`${O}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:w},"&-dashed":{background:"none",borderColor:E,borderStyle:"dashed",borderWidth:`${(0,c.bf)(I)} 0 0`},[`&-horizontal${O}-with-text${O}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${O}-dashed`]:{borderInlineStartWidth:I,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:E,borderStyle:"dotted",borderWidth:`${(0,c.bf)(I)} 0 0`},[`&-horizontal${O}-with-text${O}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${O}-dotted`]:{borderInlineStartWidth:I,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${O}-with-text`]:{color:g.colorText,fontWeight:"normal",fontSize:g.fontSize},[`&-horizontal${O}-with-text-start${O}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${O}-inner-text`]:{paddingInlineStart:y}},[`&-horizontal${O}-with-text-end${O}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${O}-inner-text`]:{paddingInlineEnd:y}}})}},$=g=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:g.marginXS});var Y=(0,i.I$)("Divider",g=>{const O=(0,h.IX)(g,{dividerHorizontalWithTextGutterMargin:g.margin,sizePaddingEdgeHorizontal:0});return[v(O),m(O)]},$,{unitless:{orientationMargin:!0}}),U=function(g,O){var y={};for(var E in g)Object.prototype.hasOwnProperty.call(g,E)&&O.indexOf(E)<0&&(y[E]=g[E]);if(g!=null&&typeof Object.getOwnPropertySymbols=="function")for(var I=0,E=Object.getOwnPropertySymbols(g);I<E.length;I++)O.indexOf(E[I])<0&&Object.prototype.propertyIsEnumerable.call(g,E[I])&&(y[E[I]]=g[E[I]]);return y};const k={small:"sm",middle:"md"};var V=g=>{const{getPrefixCls:O,direction:y,className:E,style:I}=(0,s.dj)("divider"),{prefixCls:w,type:R="horizontal",orientation:L="center",orientationMargin:M,className:q,rootClassName:ee,children:X,dashed:J,variant:G="solid",plain:t,style:p,size:C}=g,u=U(g,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),d=O("divider",w),[z,S,B]=Y(d),D=(0,l.Z)(C),H=k[D],P=!!X,T=n.useMemo(()=>L==="left"?y==="rtl"?"end":"start":L==="right"?y==="rtl"?"start":"end":L,[y,L]),_=T==="start"&&M!=null,j=T==="end"&&M!=null,N=a()(d,E,S,B,`${d}-${R}`,{[`${d}-with-text`]:P,[`${d}-with-text-${T}`]:P,[`${d}-dashed`]:!!J,[`${d}-${G}`]:G!=="solid",[`${d}-plain`]:!!t,[`${d}-rtl`]:y==="rtl",[`${d}-no-default-orientation-margin-start`]:_,[`${d}-no-default-orientation-margin-end`]:j,[`${d}-${H}`]:!!H},q,ee),Z=n.useMemo(()=>typeof M=="number"?M:/^\d+$/.test(M)?Number(M):M,[M]),K={marginInlineStart:_?Z:void 0,marginInlineEnd:j?Z:void 0};return z(n.createElement("div",Object.assign({className:N,style:Object.assign(Object.assign({},I),p)},u,{role:"separator"}),X&&R!=="vertical"&&n.createElement("span",{className:`${d}-inner-text`,style:K},X)))}},71230:function(b,o,e){"use strict";var n=e(17621);o.Z=n.Z},66309:function(b,o,e){"use strict";e.d(o,{Z:function(){return G}});var n=e(67294),r=e(93967),a=e.n(r),s=e(98423),l=e(98787),c=e(69760),f=e(96159),i=e(45353),h=e(53124),m=e(11568),v=e(15063),$=e(14747),Y=e(83262),U=e(83559);const k=t=>{const{paddingXXS:p,lineWidth:C,tagPaddingHorizontal:u,componentCls:d,calc:z}=t,S=z(u).sub(C).equal(),B=z(p).sub(C).equal();return{[d]:Object.assign(Object.assign({},(0,$.Wf)(t)),{display:"inline-block",height:"auto",marginInlineEnd:t.marginXS,paddingInline:S,fontSize:t.tagFontSize,lineHeight:t.tagLineHeight,whiteSpace:"nowrap",background:t.defaultBg,border:`${(0,m.bf)(t.lineWidth)} ${t.lineType} ${t.colorBorder}`,borderRadius:t.borderRadiusSM,opacity:1,transition:`all ${t.motionDurationMid}`,textAlign:"start",position:"relative",[`&${d}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:t.defaultColor},[`${d}-close-icon`]:{marginInlineStart:B,fontSize:t.tagIconSize,color:t.colorIcon,cursor:"pointer",transition:`all ${t.motionDurationMid}`,"&:hover":{color:t.colorTextHeading}},[`&${d}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${t.iconCls}-close, ${t.iconCls}-close:hover`]:{color:t.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${d}-checkable-checked):hover`]:{color:t.colorPrimary,backgroundColor:t.colorFillSecondary},"&:active, &-checked":{color:t.colorTextLightSolid},"&-checked":{backgroundColor:t.colorPrimary,"&:hover":{backgroundColor:t.colorPrimaryHover}},"&:active":{backgroundColor:t.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${t.iconCls} + span, > span + ${t.iconCls}`]:{marginInlineStart:S}}),[`${d}-borderless`]:{borderColor:"transparent",background:t.tagBorderlessBg}}},F=t=>{const{lineWidth:p,fontSizeIcon:C,calc:u}=t,d=t.fontSizeSM;return(0,Y.IX)(t,{tagFontSize:d,tagLineHeight:(0,m.bf)(u(t.lineHeightSM).mul(d).equal()),tagIconSize:u(C).sub(u(p).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:t.defaultBg})},V=t=>({defaultBg:new v.t(t.colorFillQuaternary).onBackground(t.colorBgContainer).toHexString(),defaultColor:t.colorText});var g=(0,U.I$)("Tag",t=>{const p=F(t);return k(p)},V),O=function(t,p){var C={};for(var u in t)Object.prototype.hasOwnProperty.call(t,u)&&p.indexOf(u)<0&&(C[u]=t[u]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var d=0,u=Object.getOwnPropertySymbols(t);d<u.length;d++)p.indexOf(u[d])<0&&Object.prototype.propertyIsEnumerable.call(t,u[d])&&(C[u[d]]=t[u[d]]);return C},E=n.forwardRef((t,p)=>{const{prefixCls:C,style:u,className:d,checked:z,onChange:S,onClick:B}=t,D=O(t,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:H,tag:P}=n.useContext(h.E_),T=Q=>{S==null||S(!z),B==null||B(Q)},_=H("tag",C),[j,N,Z]=g(_),K=a()(_,`${_}-checkable`,{[`${_}-checkable-checked`]:z},P==null?void 0:P.className,d,N,Z);return j(n.createElement("span",Object.assign({},D,{ref:p,style:Object.assign(Object.assign({},u),P==null?void 0:P.style),className:K,onClick:T})))}),I=e(98719);const w=t=>(0,I.Z)(t,(p,{textColor:C,lightBorderColor:u,lightColor:d,darkColor:z})=>({[`${t.componentCls}${t.componentCls}-${p}`]:{color:C,background:d,borderColor:u,"&-inverse":{color:t.colorTextLightSolid,background:z,borderColor:z},[`&${t.componentCls}-borderless`]:{borderColor:"transparent"}}}));var R=(0,U.bk)(["Tag","preset"],t=>{const p=F(t);return w(p)},V);function L(t){return typeof t!="string"?t:t.charAt(0).toUpperCase()+t.slice(1)}const M=(t,p,C)=>{const u=L(C);return{[`${t.componentCls}${t.componentCls}-${p}`]:{color:t[`color${C}`],background:t[`color${u}Bg`],borderColor:t[`color${u}Border`],[`&${t.componentCls}-borderless`]:{borderColor:"transparent"}}}};var q=(0,U.bk)(["Tag","status"],t=>{const p=F(t);return[M(p,"success","Success"),M(p,"processing","Info"),M(p,"error","Error"),M(p,"warning","Warning")]},V),ee=function(t,p){var C={};for(var u in t)Object.prototype.hasOwnProperty.call(t,u)&&p.indexOf(u)<0&&(C[u]=t[u]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var d=0,u=Object.getOwnPropertySymbols(t);d<u.length;d++)p.indexOf(u[d])<0&&Object.prototype.propertyIsEnumerable.call(t,u[d])&&(C[u[d]]=t[u[d]]);return C};const J=n.forwardRef((t,p)=>{const{prefixCls:C,className:u,rootClassName:d,style:z,children:S,icon:B,color:D,onClose:H,bordered:P=!0,visible:T}=t,_=ee(t,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:j,direction:N,tag:Z}=n.useContext(h.E_),[K,Q]=n.useState(!0),se=(0,s.Z)(_,["closeIcon","closable"]);n.useEffect(()=>{T!==void 0&&Q(T)},[T]);const re=(0,l.o2)(D),ae=(0,l.yT)(D),te=re||ae,de=Object.assign(Object.assign({backgroundColor:D&&!te?D:void 0},Z==null?void 0:Z.style),z),x=j("tag",C),[ue,fe,ve]=g(x),he=a()(x,Z==null?void 0:Z.className,{[`${x}-${D}`]:te,[`${x}-has-color`]:D&&!te,[`${x}-hidden`]:!K,[`${x}-rtl`]:N==="rtl",[`${x}-borderless`]:!P},u,d,fe,ve),oe=W=>{W.stopPropagation(),H==null||H(W),!W.defaultPrevented&&Q(!1)},[,me]=(0,c.Z)((0,c.w)(t),(0,c.w)(Z),{closable:!1,closeIconRender:W=>{const be=n.createElement("span",{className:`${x}-close-icon`,onClick:oe},W);return(0,f.wm)(W,be,A=>({onClick:ie=>{var ne;(ne=A==null?void 0:A.onClick)===null||ne===void 0||ne.call(A,ie),oe(ie)},className:a()(A==null?void 0:A.className,`${x}-close-icon`)}))}}),ge=typeof _.onClick=="function"||S&&S.type==="a",le=B||null,Oe=le?n.createElement(n.Fragment,null,le,S&&n.createElement("span",null,S)):S,ce=n.createElement("span",Object.assign({},se,{ref:p,className:he,style:de}),Oe,me,re&&n.createElement(R,{key:"preset",prefixCls:x}),ae&&n.createElement(q,{key:"status",prefixCls:x}));return ue(ge?n.createElement(i.Z,{component:"Tag"},ce):ce)});J.CheckableTag=E;var G=J},64599:function(b,o,e){var n=e(96263);function r(a,s){var l=typeof Symbol!="undefined"&&a[Symbol.iterator]||a["@@iterator"];if(!l){if(Array.isArray(a)||(l=n(a))||s&&a&&typeof a.length=="number"){l&&(a=l);var c=0,f=function(){};return{s:f,n:function(){return c>=a.length?{done:!0}:{done:!1,value:a[c++]}},e:function($){throw $},f}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var i=!0,h=!1,m;return{s:function(){l=l.call(a)},n:function(){var $=l.next();return i=$.done,$},e:function($){h=!0,m=$},f:function(){try{!i&&l.return!=null&&l.return()}finally{if(h)throw m}}}}b.exports=r,b.exports.__esModule=!0,b.exports.default=b.exports}}]);
