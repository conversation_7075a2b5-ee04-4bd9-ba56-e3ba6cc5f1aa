"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7528],{7528:function(Ue,x,l){l.d(x,{$L:function(){return ae},$y:function(){return se},BW:function(){return Oe},Cl:function(){return $e},Dd:function(){return he},Dw:function(){return Pe},G8:function(){return pe},GN:function(){return ve},Jt:function(){return Se},Lk:function(){return Ce},NV:function(){return ue},Pm:function(){return Ie},Qj:function(){return ie},Qz:function(){return je},WH:function(){return oe},Zo:function(){return le},_$:function(){return ge},_7:function(){return Re},_I:function(){return _e},aX:function(){return Ge},ae:function(){return me},an:function(){return ke},bK:function(){return we},e5:function(){return ne},eH:function(){return Te},ei:function(){return qe},hW:function(){return te},jy:function(){return de},ln:function(){return ye},pV:function(){return De},qF:function(){return fe},rw:function(){return be},u1:function(){return Ee},v:function(){return Be},xM:function(){return Me},yF:function(){return ce},yc:function(){return Le}});var Y=l(15009),a=l.n(Y),ee=l(97857),s=l.n(ee),re=l(99289),i=l.n(re),p=l(84226);function te(r){return c.apply(this,arguments)}function c(){return c=i()(a()().mark(function r(t){return a()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return u.abrupt("return",(0,p.request)("/api/devops/project/list",s()({method:"GET"},t||{})));case 1:case"end":return u.stop()}},r)})),c.apply(this,arguments)}function ne(r,t){return o.apply(this,arguments)}function o(){return o=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/project/detail",s()({method:"POST",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),o.apply(this,arguments)}function ae(r,t){return m.apply(this,arguments)}function m(){return m=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/project/add",s()({method:"POST",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),m.apply(this,arguments)}function Fe(r,t){return _.apply(this,arguments)}function _(){return _=_asyncToGenerator(_regeneratorRuntime().mark(function r(t,n){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",request("/api/devops/project/remove",_objectSpread({method:"DELETE",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),_.apply(this,arguments)}function We(r,t){return h.apply(this,arguments)}function h(){return h=_asyncToGenerator(_regeneratorRuntime().mark(function r(t,n){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",request("/api/devops/project/search",_objectSpread({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),h.apply(this,arguments)}function ue(r,t){return d.apply(this,arguments)}function d(){return d=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/project/edit",s()({method:"PUT",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),d.apply(this,arguments)}function se(r,t){return f.apply(this,arguments)}function f(){return f=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/repo/listbranch",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),f.apply(this,arguments)}function Ke(r,t){return g.apply(this,arguments)}function g(){return g=_asyncToGenerator(_regeneratorRuntime().mark(function r(t,n){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",request("/api/devops/repo/branch",_objectSpread({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),g.apply(this,arguments)}function ie(r,t){return w.apply(this,arguments)}function w(){return w=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/repo/branch/add",s()({method:"POST",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),w.apply(this,arguments)}function pe(r,t){return v.apply(this,arguments)}function v(){return v=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/repo/branch/remove",s()({method:"DELETE",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),v.apply(this,arguments)}function le(r,t){return b.apply(this,arguments)}function b(){return b=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/repo/branch/removeMerged",s()({method:"DELETE",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),b.apply(this,arguments)}function ce(r,t){return y.apply(this,arguments)}function y(){return y=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/repo/tree",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),y.apply(this,arguments)}function oe(r,t){return T.apply(this,arguments)}function T(){return T=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/repo/file",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),T.apply(this,arguments)}function me(r,t){return E.apply(this,arguments)}function E(){return E=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/repo/file/new",s()({method:"POST",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),E.apply(this,arguments)}function _e(r,t){return j.apply(this,arguments)}function j(){return j=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/repo/file/remove",s()({method:"DELETE",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),j.apply(this,arguments)}function he(r,t){return P.apply(this,arguments)}function P(){return P=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/repo/file/update",s()({method:"PUT",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),P.apply(this,arguments)}function de(r,t){return q.apply(this,arguments)}function q(){return q=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/repo/blob",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),q.apply(this,arguments)}function fe(r,t){return $.apply(this,arguments)}function $(){return $=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/commit/list",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),$.apply(this,arguments)}function Ae(r,t){return k.apply(this,arguments)}function k(){return k=_asyncToGenerator(_regeneratorRuntime().mark(function r(t,n){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",request("/api/devops/commit/show",_objectSpread({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),k.apply(this,arguments)}function ge(r,t){return M.apply(this,arguments)}function M(){return M=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/repo/compare",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),M.apply(this,arguments)}function we(r,t){return G.apply(this,arguments)}function G(){return G=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/mergerequest/add",s()({method:"POST",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),G.apply(this,arguments)}function ve(r,t){return R.apply(this,arguments)}function R(){return R=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/mergerequest/list",s()({method:"POST",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),R.apply(this,arguments)}function be(r,t){return D.apply(this,arguments)}function D(){return D=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/repo/tags/list",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),D.apply(this,arguments)}function ye(r,t){return B.apply(this,arguments)}function B(){return B=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/repo/tags/add",s()({method:"POST",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),B.apply(this,arguments)}function Te(r,t){return O.apply(this,arguments)}function O(){return O=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/project/listmembers",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),O.apply(this,arguments)}function Ee(r,t){return S.apply(this,arguments)}function S(){return S=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/project/listmilestones",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),S.apply(this,arguments)}function je(r,t){return I.apply(this,arguments)}function I(){return I=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/project/milestones/issues",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),I.apply(this,arguments)}function Pe(r,t){return C.apply(this,arguments)}function C(){return C=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/project/milestones/get",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),C.apply(this,arguments)}function qe(r,t){return L.apply(this,arguments)}function L(){return L=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/project/milestones/reqs",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),L.apply(this,arguments)}function $e(r,t){return U.apply(this,arguments)}function U(){return U=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/project/milestones/add",s()({method:"POST",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),U.apply(this,arguments)}function ke(r,t){return F.apply(this,arguments)}function F(){return F=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/project/milestones/update",s()({method:"PUT",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),F.apply(this,arguments)}function Me(r,t){return W.apply(this,arguments)}function W(){return W=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/project/boards/list",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),W.apply(this,arguments)}function Ge(r,t){return K.apply(this,arguments)}function K(){return K=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/project/boards/add",s()({method:"POST",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),K.apply(this,arguments)}function Ne(r,t){return A.apply(this,arguments)}function A(){return A=_asyncToGenerator(_regeneratorRuntime().mark(function r(t,n){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",request("/api/devops/project/boards/remove",_objectSpread({method:"DELETE",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),A.apply(this,arguments)}function Re(r){return N.apply(this,arguments)}function N(){return N=i()(a()().mark(function r(t){return a()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return u.abrupt("return",(0,p.request)("/api/devops/issues/list",s()({method:"GET"},t||{})));case 1:case"end":return u.stop()}},r)})),N.apply(this,arguments)}function De(r,t){return J.apply(this,arguments)}function J(){return J=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/issues/get",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),J.apply(this,arguments)}function Be(r,t){return H.apply(this,arguments)}function H(){return H=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/issues/add",s()({method:"POST",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),H.apply(this,arguments)}function Oe(r,t){return Q.apply(this,arguments)}function Q(){return Q=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/issues/update",s()({method:"PUT",data:t},n||{})));case 1:case"end":return e.stop()}},r)})),Q.apply(this,arguments)}function Se(r,t){return V.apply(this,arguments)}function V(){return V=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/pipelines/list",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),V.apply(this,arguments)}function Ie(r,t){return z.apply(this,arguments)}function z(){return z=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/pipelines/get",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),z.apply(this,arguments)}function Ce(r,t){return X.apply(this,arguments)}function X(){return X=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/pipelines/listjobs",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),X.apply(this,arguments)}function Le(r,t){return Z.apply(this,arguments)}function Z(){return Z=i()(a()().mark(function r(t,n){return a()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,p.request)("/api/devops/events/list",s()({method:"GET",params:t},n||{})));case 1:case"end":return e.stop()}},r)})),Z.apply(this,arguments)}}}]);
