"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2069],{48820:function(<PERSON>,Pe){var f={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"};Pe.Z=f},42110:function(Fe,Pe){var f={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"};Pe.Z=f},11941:function(Fe,Pe,f){f.d(Pe,{Z:function(){return Ka}});var a=f(67294),Zt=f(62208),zt=f(35872),ce=f(87462),Nt=f(42110),_t=f(93771),Ot=function(t,n){return a.createElement(_t.Z,(0,ce.Z)({},t,{ref:n,icon:Nt.Z}))},Bt=a.forwardRef(Ot),Dt=Bt,At=f(93967),Y=f.n(At),X=f(4942),ee=f(1413),L=f(97685),Be=f(71002),Ie=f(91),Ye=f(21770),Ht=f(31131),Le=(0,a.createContext)(null),Qe=f(74902),De=f(9220),Wt=f(66680),Gt=f(42550),Je=f(75164),jt=function(t){var n=t.activeTabOffset,r=t.horizontal,i=t.rtl,l=t.indicator,c=l===void 0?{}:l,o=c.size,s=c.align,d=s===void 0?"center":s,g=(0,a.useState)(),b=(0,L.Z)(g,2),S=b[0],M=b[1],H=(0,a.useRef)(),E=a.useCallback(function(m){return typeof o=="function"?o(m):typeof o=="number"?o:m},[o]);function O(){Je.Z.cancel(H.current)}return(0,a.useEffect)(function(){var m={};if(n)if(r){m.width=E(n.width);var h=i?"right":"left";d==="start"&&(m[h]=n[h]),d==="center"&&(m[h]=n[h]+n.width/2,m.transform=i?"translateX(50%)":"translateX(-50%)"),d==="end"&&(m[h]=n[h]+n.width,m.transform="translateX(-100%)")}else m.height=E(n.height),d==="start"&&(m.top=n.top),d==="center"&&(m.top=n.top+n.height/2,m.transform="translateY(-50%)"),d==="end"&&(m.top=n.top+n.height,m.transform="translateY(-100%)");return O(),H.current=(0,Je.Z)(function(){var R=S&&m&&Object.keys(m).every(function(N){var B=m[N],D=S[N];return typeof B=="number"&&typeof D=="number"?Math.round(B)===Math.round(D):B===D});R||M(m)}),O},[JSON.stringify(n),r,i,d,E]),{style:S}},kt=jt,qe={width:0,height:0,left:0,top:0};function Kt(e,t,n){return(0,a.useMemo)(function(){for(var r,i=new Map,l=t.get((r=e[0])===null||r===void 0?void 0:r.key)||qe,c=l.left+l.width,o=0;o<e.length;o+=1){var s=e[o].key,d=t.get(s);if(!d){var g;d=t.get((g=e[o-1])===null||g===void 0?void 0:g.key)||qe}var b=i.get(s)||(0,ee.Z)({},d);b.right=c-b.left-b.width,i.set(s,b)}return i},[e.map(function(r){return r.key}).join("_"),t,n])}function et(e,t){var n=a.useRef(e),r=a.useState({}),i=(0,L.Z)(r,2),l=i[1];function c(o){var s=typeof o=="function"?o(n.current):o;s!==n.current&&t(s,n.current),n.current=s,l({})}return[n.current,c]}var Vt=.1,tt=.01,Me=20,at=Math.pow(.995,Me);function Xt(e,t){var n=(0,a.useState)(),r=(0,L.Z)(n,2),i=r[0],l=r[1],c=(0,a.useState)(0),o=(0,L.Z)(c,2),s=o[0],d=o[1],g=(0,a.useState)(0),b=(0,L.Z)(g,2),S=b[0],M=b[1],H=(0,a.useState)(),E=(0,L.Z)(H,2),O=E[0],m=E[1],h=(0,a.useRef)();function R(T){var w=T.touches[0],v=w.screenX,C=w.screenY;l({x:v,y:C}),window.clearInterval(h.current)}function N(T){if(i){var w=T.touches[0],v=w.screenX,C=w.screenY;l({x:v,y:C});var p=v-i.x,x=C-i.y;t(p,x);var j=Date.now();d(j),M(j-s),m({x:p,y:x})}}function B(){if(i&&(l(null),m(null),O)){var T=O.x/S,w=O.y/S,v=Math.abs(T),C=Math.abs(w);if(Math.max(v,C)<Vt)return;var p=T,x=w;h.current=window.setInterval(function(){if(Math.abs(p)<tt&&Math.abs(x)<tt){window.clearInterval(h.current);return}p*=at,x*=at,t(p*Me,x*Me)},Me)}}var D=(0,a.useRef)();function G(T){var w=T.deltaX,v=T.deltaY,C=0,p=Math.abs(w),x=Math.abs(v);p===x?C=D.current==="x"?w:v:p>x?(C=w,D.current="x"):(C=v,D.current="y"),t(-C,-C)&&T.preventDefault()}var _=(0,a.useRef)(null);_.current={onTouchStart:R,onTouchMove:N,onTouchEnd:B,onWheel:G},a.useEffect(function(){function T(p){_.current.onTouchStart(p)}function w(p){_.current.onTouchMove(p)}function v(p){_.current.onTouchEnd(p)}function C(p){_.current.onWheel(p)}return document.addEventListener("touchmove",w,{passive:!1}),document.addEventListener("touchend",v,{passive:!0}),e.current.addEventListener("touchstart",T,{passive:!0}),e.current.addEventListener("wheel",C,{passive:!1}),function(){document.removeEventListener("touchmove",w),document.removeEventListener("touchend",v)}},[])}var Ut=f(8410);function nt(e){var t=(0,a.useState)(0),n=(0,L.Z)(t,2),r=n[0],i=n[1],l=(0,a.useRef)(0),c=(0,a.useRef)();return c.current=e,(0,Ut.o)(function(){var o;(o=c.current)===null||o===void 0||o.call(c)},[r]),function(){l.current===r&&(l.current+=1,i(l.current))}}function Ft(e){var t=(0,a.useRef)([]),n=(0,a.useState)({}),r=(0,L.Z)(n,2),i=r[1],l=(0,a.useRef)(typeof e=="function"?e():e),c=nt(function(){var s=l.current;t.current.forEach(function(d){s=d(s)}),t.current=[],l.current=s,i({})});function o(s){t.current.push(s),c()}return[l.current,o]}var rt={width:0,height:0,left:0,top:0,right:0};function Yt(e,t,n,r,i,l,c){var o=c.tabs,s=c.tabPosition,d=c.rtl,g,b,S;return["top","bottom"].includes(s)?(g="width",b=d?"right":"left",S=Math.abs(n)):(g="height",b="top",S=-n),(0,a.useMemo)(function(){if(!o.length)return[0,0];for(var M=o.length,H=M,E=0;E<M;E+=1){var O=e.get(o[E].key)||rt;if(Math.floor(O[b]+O[g])>Math.floor(S+t)){H=E-1;break}}for(var m=0,h=M-1;h>=0;h-=1){var R=e.get(o[h].key)||rt;if(R[b]<S){m=h+1;break}}return m>=H?[0,0]:[m,H]},[e,t,r,i,l,S,s,o.map(function(M){return M.key}).join("_"),d])}function it(e){var t;return e instanceof Map?(t={},e.forEach(function(n,r){t[r]=n})):t=e,JSON.stringify(t)}var Qt="TABS_DQ";function ot(e){return String(e).replace(/"/g,Qt)}function Ae(e,t,n,r){return!(!n||r||e===!1||e===void 0&&(t===!1||t===null))}var Jt=a.forwardRef(function(e,t){var n=e.prefixCls,r=e.editable,i=e.locale,l=e.style;return!r||r.showAdd===!1?null:a.createElement("button",{ref:t,type:"button",className:"".concat(n,"-nav-add"),style:l,"aria-label":(i==null?void 0:i.addAriaLabel)||"Add tab",onClick:function(o){r.onEdit("add",{event:o})}},r.addIcon||"+")}),lt=Jt,qt=a.forwardRef(function(e,t){var n=e.position,r=e.prefixCls,i=e.extra;if(!i)return null;var l,c={};return(0,Be.Z)(i)==="object"&&!a.isValidElement(i)?c=i:c.right=i,n==="right"&&(l=c.right),n==="left"&&(l=c.left),l?a.createElement("div",{className:"".concat(r,"-extra-content"),ref:t},l):null}),ct=qt,ea=f(29171),st=f(72512),ue=f(15105),ta=a.forwardRef(function(e,t){var n=e.prefixCls,r=e.id,i=e.tabs,l=e.locale,c=e.mobile,o=e.more,s=o===void 0?{}:o,d=e.style,g=e.className,b=e.editable,S=e.tabBarGutter,M=e.rtl,H=e.removeAriaLabel,E=e.onTabClick,O=e.getPopupContainer,m=e.popupClassName,h=(0,a.useState)(!1),R=(0,L.Z)(h,2),N=R[0],B=R[1],D=(0,a.useState)(null),G=(0,L.Z)(D,2),_=G[0],T=G[1],w=s.icon,v=w===void 0?"More":w,C="".concat(r,"-more-popup"),p="".concat(n,"-dropdown"),x=_!==null?"".concat(C,"-").concat(_):null,j=l==null?void 0:l.dropdownAriaLabel;function k(P,W){P.preventDefault(),P.stopPropagation(),b.onEdit("remove",{key:W,event:P})}var U=a.createElement(st.ZP,{onClick:function(W){var K=W.key,V=W.domEvent;E(K,V),B(!1)},prefixCls:"".concat(p,"-menu"),id:C,tabIndex:-1,role:"listbox","aria-activedescendant":x,selectedKeys:[_],"aria-label":j!==void 0?j:"expanded dropdown"},i.map(function(P){var W=P.closable,K=P.disabled,V=P.closeIcon,J=P.key,ie=P.label,q=Ae(W,V,b,K);return a.createElement(st.sN,{key:J,id:"".concat(C,"-").concat(J),role:"option","aria-controls":r&&"".concat(r,"-panel-").concat(J),disabled:K},a.createElement("span",null,ie),q&&a.createElement("button",{type:"button","aria-label":H||"remove",tabIndex:0,className:"".concat(p,"-menu-item-remove"),onClick:function(fe){fe.stopPropagation(),k(fe,J)}},V||b.removeIcon||"\xD7"))}));function Q(P){for(var W=i.filter(function(q){return!q.disabled}),K=W.findIndex(function(q){return q.key===_})||0,V=W.length,J=0;J<V;J+=1){K=(K+P+V)%V;var ie=W[K];if(!ie.disabled){T(ie.key);return}}}function $(P){var W=P.which;if(!N){[ue.Z.DOWN,ue.Z.SPACE,ue.Z.ENTER].includes(W)&&(B(!0),P.preventDefault());return}switch(W){case ue.Z.UP:Q(-1),P.preventDefault();break;case ue.Z.DOWN:Q(1),P.preventDefault();break;case ue.Z.ESC:B(!1);break;case ue.Z.SPACE:case ue.Z.ENTER:_!==null&&E(_,P);break}}(0,a.useEffect)(function(){var P=document.getElementById(x);P&&P.scrollIntoView&&P.scrollIntoView(!1)},[_]),(0,a.useEffect)(function(){N||T(null)},[N]);var re=(0,X.Z)({},M?"marginRight":"marginLeft",S);i.length||(re.visibility="hidden",re.order=1);var te=Y()((0,X.Z)({},"".concat(p,"-rtl"),M)),F=c?null:a.createElement(ea.Z,(0,ce.Z)({prefixCls:p,overlay:U,visible:i.length?N:!1,onVisibleChange:B,overlayClassName:Y()(te,m),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:O},s),a.createElement("button",{type:"button",className:"".concat(n,"-nav-more"),style:re,"aria-haspopup":"listbox","aria-controls":C,id:"".concat(r,"-more"),"aria-expanded":N,onKeyDown:$},v));return a.createElement("div",{className:Y()("".concat(n,"-nav-operations"),g),style:d,ref:t},F,a.createElement(lt,{prefixCls:n,locale:l,editable:b}))}),aa=a.memo(ta,function(e,t){return t.tabMoving}),na=function(t){var n=t.prefixCls,r=t.id,i=t.active,l=t.focus,c=t.tab,o=c.key,s=c.label,d=c.disabled,g=c.closeIcon,b=c.icon,S=t.closable,M=t.renderWrapper,H=t.removeAriaLabel,E=t.editable,O=t.onClick,m=t.onFocus,h=t.onBlur,R=t.onKeyDown,N=t.onMouseDown,B=t.onMouseUp,D=t.style,G=t.tabCount,_=t.currentPosition,T="".concat(n,"-tab"),w=Ae(S,g,E,d);function v(k){d||O(k)}function C(k){k.preventDefault(),k.stopPropagation(),E.onEdit("remove",{key:o,event:k})}var p=a.useMemo(function(){return b&&typeof s=="string"?a.createElement("span",null,s):s},[s,b]),x=a.useRef(null);a.useEffect(function(){l&&x.current&&x.current.focus()},[l]);var j=a.createElement("div",{key:o,"data-node-key":ot(o),className:Y()(T,(0,X.Z)((0,X.Z)((0,X.Z)((0,X.Z)({},"".concat(T,"-with-remove"),w),"".concat(T,"-active"),i),"".concat(T,"-disabled"),d),"".concat(T,"-focus"),l)),style:D,onClick:v},a.createElement("div",{ref:x,role:"tab","aria-selected":i,id:r&&"".concat(r,"-tab-").concat(o),className:"".concat(T,"-btn"),"aria-controls":r&&"".concat(r,"-panel-").concat(o),"aria-disabled":d,tabIndex:d?null:i?0:-1,onClick:function(U){U.stopPropagation(),v(U)},onKeyDown:R,onMouseDown:N,onMouseUp:B,onFocus:m,onBlur:h},l&&a.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(_," of ").concat(G)),b&&a.createElement("span",{className:"".concat(T,"-icon")},b),s&&p),w&&a.createElement("button",{type:"button",role:"tab","aria-label":H||"remove",tabIndex:i?0:-1,className:"".concat(T,"-remove"),onClick:function(U){U.stopPropagation(),C(U)}},g||E.removeIcon||"\xD7"));return M?M(j):j},ra=na,ia=function(t,n){var r=t.offsetWidth,i=t.offsetHeight,l=t.offsetTop,c=t.offsetLeft,o=t.getBoundingClientRect(),s=o.width,d=o.height,g=o.left,b=o.top;return Math.abs(s-r)<1?[s,d,g-n.left,b-n.top]:[r,i,c,l]},pe=function(t){var n=t.current||{},r=n.offsetWidth,i=r===void 0?0:r,l=n.offsetHeight,c=l===void 0?0:l;if(t.current){var o=t.current.getBoundingClientRect(),s=o.width,d=o.height;if(Math.abs(s-i)<1)return[s,d]}return[i,c]},Ze=function(t,n){return t[n?0:1]},oa=a.forwardRef(function(e,t){var n=e.className,r=e.style,i=e.id,l=e.animated,c=e.activeKey,o=e.rtl,s=e.extra,d=e.editable,g=e.locale,b=e.tabPosition,S=e.tabBarGutter,M=e.children,H=e.onTabClick,E=e.onTabScroll,O=e.indicator,m=a.useContext(Le),h=m.prefixCls,R=m.tabs,N=(0,a.useRef)(null),B=(0,a.useRef)(null),D=(0,a.useRef)(null),G=(0,a.useRef)(null),_=(0,a.useRef)(null),T=(0,a.useRef)(null),w=(0,a.useRef)(null),v=b==="top"||b==="bottom",C=et(0,function(I,u){v&&E&&E({direction:I>u?"left":"right"})}),p=(0,L.Z)(C,2),x=p[0],j=p[1],k=et(0,function(I,u){!v&&E&&E({direction:I>u?"top":"bottom"})}),U=(0,L.Z)(k,2),Q=U[0],$=U[1],re=(0,a.useState)([0,0]),te=(0,L.Z)(re,2),F=te[0],P=te[1],W=(0,a.useState)([0,0]),K=(0,L.Z)(W,2),V=K[0],J=K[1],ie=(0,a.useState)([0,0]),q=(0,L.Z)(ie,2),$e=q[0],fe=q[1],ye=(0,a.useState)([0,0]),Se=(0,L.Z)(ye,2),Z=Se[0],se=Se[1],ge=Ft(new Map),mt=(0,L.Z)(ge,2),Va=mt[0],Xa=mt[1],ze=Kt(R,Va,V[0]),He=Ze(F,v),Ee=Ze(V,v),We=Ze($e,v),gt=Ze(Z,v),ht=Math.floor(He)<Math.floor(Ee+We),le=ht?He-gt:He-We,Ua="".concat(h,"-nav-operations-hidden"),be=0,he=0;v&&o?(be=0,he=Math.max(0,Ee-le)):(be=Math.min(0,le-Ee),he=0);function Ge(I){return I<be?be:I>he?he:I}var je=(0,a.useRef)(null),Fa=(0,a.useState)(),pt=(0,L.Z)(Fa,2),Ne=pt[0],$t=pt[1];function ke(){$t(Date.now())}function Ke(){je.current&&clearTimeout(je.current)}Xt(G,function(I,u){function z(A,oe){A(function(ae){var xe=Ge(ae+oe);return xe})}return ht?(v?z(j,I):z($,u),Ke(),ke(),!0):!1}),(0,a.useEffect)(function(){return Ke(),Ne&&(je.current=setTimeout(function(){$t(0)},100)),Ke},[Ne]);var Ya=Yt(ze,le,v?x:Q,Ee,We,gt,(0,ee.Z)((0,ee.Z)({},e),{},{tabs:R})),yt=(0,L.Z)(Ya,2),Qa=yt[0],Ja=yt[1],St=(0,Wt.Z)(function(){var I=arguments.length>0&&arguments[0]!==void 0?arguments[0]:c,u=ze.get(I)||{width:0,height:0,left:0,right:0,top:0};if(v){var z=x;o?u.right<x?z=u.right:u.right+u.width>x+le&&(z=u.right+u.width-le):u.left<-x?z=-u.left:u.left+u.width>-x+le&&(z=-(u.left+u.width-le)),$(0),j(Ge(z))}else{var A=Q;u.top<-Q?A=-u.top:u.top+u.height>-Q+le&&(A=-(u.top+u.height-le)),j(0),$(Ge(A))}}),qa=(0,a.useState)(),Ct=(0,L.Z)(qa,2),me=Ct[0],Re=Ct[1],en=(0,a.useState)(!1),xt=(0,L.Z)(en,2),tn=xt[0],Tt=xt[1],de=R.filter(function(I){return!I.disabled}).map(function(I){return I.key}),Ce=function(u){var z=de.indexOf(me||c),A=de.length,oe=(z+u+A)%A,ae=de[oe];Re(ae)},an=function(u){var z=u.code,A=o&&v,oe=de[0],ae=de[de.length-1];switch(z){case"ArrowLeft":{v&&Ce(A?1:-1);break}case"ArrowRight":{v&&Ce(A?-1:1);break}case"ArrowUp":{u.preventDefault(),v||Ce(-1);break}case"ArrowDown":{u.preventDefault(),v||Ce(1);break}case"Home":{u.preventDefault(),Re(oe);break}case"End":{u.preventDefault(),Re(ae);break}case"Enter":case"Space":{u.preventDefault(),H(me!=null?me:c,u);break}case"Backspace":case"Delete":{var xe=de.indexOf(me),ne=R.find(function(Te){return Te.key===me}),Ue=Ae(ne==null?void 0:ne.closable,ne==null?void 0:ne.closeIcon,d,ne==null?void 0:ne.disabled);Ue&&(u.preventDefault(),u.stopPropagation(),d.onEdit("remove",{key:me,event:u}),xe===de.length-1?Ce(-1):Ce(1));break}}},_e={};v?_e[o?"marginRight":"marginLeft"]=S:_e.marginTop=S;var Pt=R.map(function(I,u){var z=I.key;return a.createElement(ra,{id:i,prefixCls:h,key:z,tab:I,style:u===0?void 0:_e,closable:I.closable,editable:d,active:z===c,focus:z===me,renderWrapper:M,removeAriaLabel:g==null?void 0:g.removeAriaLabel,tabCount:de.length,currentPosition:u+1,onClick:function(oe){H(z,oe)},onKeyDown:an,onFocus:function(){tn||Re(z),St(z),ke(),G.current&&(o||(G.current.scrollLeft=0),G.current.scrollTop=0)},onBlur:function(){Re(void 0)},onMouseDown:function(){Tt(!0)},onMouseUp:function(){Tt(!1)}})}),Et=function(){return Xa(function(){var u,z=new Map,A=(u=_.current)===null||u===void 0?void 0:u.getBoundingClientRect();return R.forEach(function(oe){var ae,xe=oe.key,ne=(ae=_.current)===null||ae===void 0?void 0:ae.querySelector('[data-node-key="'.concat(ot(xe),'"]'));if(ne){var Ue=ia(ne,A),Te=(0,L.Z)(Ue,4),cn=Te[0],sn=Te[1],dn=Te[2],un=Te[3];z.set(xe,{width:cn,height:sn,left:dn,top:un})}}),z})};(0,a.useEffect)(function(){Et()},[R.map(function(I){return I.key}).join("_")]);var Oe=nt(function(){var I=pe(N),u=pe(B),z=pe(D);P([I[0]-u[0]-z[0],I[1]-u[1]-z[1]]);var A=pe(w);fe(A);var oe=pe(T);se(oe);var ae=pe(_);J([ae[0]-A[0],ae[1]-A[1]]),Et()}),nn=R.slice(0,Qa),rn=R.slice(Ja+1),Rt=[].concat((0,Qe.Z)(nn),(0,Qe.Z)(rn)),wt=ze.get(c),on=kt({activeTabOffset:wt,horizontal:v,indicator:O,rtl:o}),ln=on.style;(0,a.useEffect)(function(){St()},[c,be,he,it(wt),it(ze),v]),(0,a.useEffect)(function(){Oe()},[o]);var It=!!Rt.length,we="".concat(h,"-nav-wrap"),Ve,Xe,Lt,Mt;return v?o?(Xe=x>0,Ve=x!==he):(Ve=x<0,Xe=x!==be):(Lt=Q<0,Mt=Q!==be),a.createElement(De.Z,{onResize:Oe},a.createElement("div",{ref:(0,Gt.x1)(t,N),role:"tablist","aria-orientation":v?"horizontal":"vertical",className:Y()("".concat(h,"-nav"),n),style:r,onKeyDown:function(){ke()}},a.createElement(ct,{ref:B,position:"left",extra:s,prefixCls:h}),a.createElement(De.Z,{onResize:Oe},a.createElement("div",{className:Y()(we,(0,X.Z)((0,X.Z)((0,X.Z)((0,X.Z)({},"".concat(we,"-ping-left"),Ve),"".concat(we,"-ping-right"),Xe),"".concat(we,"-ping-top"),Lt),"".concat(we,"-ping-bottom"),Mt)),ref:G},a.createElement(De.Z,{onResize:Oe},a.createElement("div",{ref:_,className:"".concat(h,"-nav-list"),style:{transform:"translate(".concat(x,"px, ").concat(Q,"px)"),transition:Ne?"none":void 0}},Pt,a.createElement(lt,{ref:w,prefixCls:h,locale:g,editable:d,style:(0,ee.Z)((0,ee.Z)({},Pt.length===0?void 0:_e),{},{visibility:It?"hidden":null})}),a.createElement("div",{className:Y()("".concat(h,"-ink-bar"),(0,X.Z)({},"".concat(h,"-ink-bar-animated"),l.inkBar)),style:ln}))))),a.createElement(aa,(0,ce.Z)({},e,{removeAriaLabel:g==null?void 0:g.removeAriaLabel,ref:T,prefixCls:h,tabs:Rt,className:!It&&Ua,tabMoving:!!Ne})),a.createElement(ct,{ref:D,position:"right",extra:s,prefixCls:h})))}),dt=oa,la=a.forwardRef(function(e,t){var n=e.prefixCls,r=e.className,i=e.style,l=e.id,c=e.active,o=e.tabKey,s=e.children;return a.createElement("div",{id:l&&"".concat(l,"-panel-").concat(o),role:"tabpanel",tabIndex:c?0:-1,"aria-labelledby":l&&"".concat(l,"-tab-").concat(o),"aria-hidden":!c,style:i,className:Y()(n,c&&"".concat(n,"-active"),r),ref:t},s)}),ut=la,ca=["renderTabBar"],sa=["label","key"],da=function(t){var n=t.renderTabBar,r=(0,Ie.Z)(t,ca),i=a.useContext(Le),l=i.tabs;if(n){var c=(0,ee.Z)((0,ee.Z)({},r),{},{panes:l.map(function(o){var s=o.label,d=o.key,g=(0,Ie.Z)(o,sa);return a.createElement(ut,(0,ce.Z)({tab:s,key:d,tabKey:d},g))})});return n(c,dt)}return a.createElement(dt,r)},ua=da,va=f(29372),fa=["key","forceRender","style","className","destroyInactiveTabPane"],ba=function(t){var n=t.id,r=t.activeKey,i=t.animated,l=t.tabPosition,c=t.destroyInactiveTabPane,o=a.useContext(Le),s=o.prefixCls,d=o.tabs,g=i.tabPane,b="".concat(s,"-tabpane");return a.createElement("div",{className:Y()("".concat(s,"-content-holder"))},a.createElement("div",{className:Y()("".concat(s,"-content"),"".concat(s,"-content-").concat(l),(0,X.Z)({},"".concat(s,"-content-animated"),g))},d.map(function(S){var M=S.key,H=S.forceRender,E=S.style,O=S.className,m=S.destroyInactiveTabPane,h=(0,Ie.Z)(S,fa),R=M===r;return a.createElement(va.ZP,(0,ce.Z)({key:M,visible:R,forceRender:H,removeOnLeave:!!(c||m),leavedClassName:"".concat(b,"-hidden")},i.tabPaneMotion),function(N,B){var D=N.style,G=N.className;return a.createElement(ut,(0,ce.Z)({},h,{prefixCls:b,id:n,tabKey:M,animated:g,active:R,style:(0,ee.Z)((0,ee.Z)({},E),D),className:Y()(O,G),ref:B}))})})))},ma=ba,vn=f(80334);function ga(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{inkBar:!0,tabPane:!1},t;return e===!1?t={inkBar:!1,tabPane:!1}:e===!0?t={inkBar:!0,tabPane:!1}:t=(0,ee.Z)({inkBar:!0},(0,Be.Z)(e)==="object"?e:{}),t.tabPaneMotion&&t.tabPane===void 0&&(t.tabPane=!0),!t.tabPaneMotion&&t.tabPane&&(t.tabPane=!1),t}var ha=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],vt=0,pa=a.forwardRef(function(e,t){var n=e.id,r=e.prefixCls,i=r===void 0?"rc-tabs":r,l=e.className,c=e.items,o=e.direction,s=e.activeKey,d=e.defaultActiveKey,g=e.editable,b=e.animated,S=e.tabPosition,M=S===void 0?"top":S,H=e.tabBarGutter,E=e.tabBarStyle,O=e.tabBarExtraContent,m=e.locale,h=e.more,R=e.destroyInactiveTabPane,N=e.renderTabBar,B=e.onChange,D=e.onTabClick,G=e.onTabScroll,_=e.getPopupContainer,T=e.popupClassName,w=e.indicator,v=(0,Ie.Z)(e,ha),C=a.useMemo(function(){return(c||[]).filter(function(Z){return Z&&(0,Be.Z)(Z)==="object"&&"key"in Z})},[c]),p=o==="rtl",x=ga(b),j=(0,a.useState)(!1),k=(0,L.Z)(j,2),U=k[0],Q=k[1];(0,a.useEffect)(function(){Q((0,Ht.Z)())},[]);var $=(0,Ye.Z)(function(){var Z;return(Z=C[0])===null||Z===void 0?void 0:Z.key},{value:s,defaultValue:d}),re=(0,L.Z)($,2),te=re[0],F=re[1],P=(0,a.useState)(function(){return C.findIndex(function(Z){return Z.key===te})}),W=(0,L.Z)(P,2),K=W[0],V=W[1];(0,a.useEffect)(function(){var Z=C.findIndex(function(ge){return ge.key===te});if(Z===-1){var se;Z=Math.max(0,Math.min(K,C.length-1)),F((se=C[Z])===null||se===void 0?void 0:se.key)}V(Z)},[C.map(function(Z){return Z.key}).join("_"),te,K]);var J=(0,Ye.Z)(null,{value:n}),ie=(0,L.Z)(J,2),q=ie[0],$e=ie[1];(0,a.useEffect)(function(){n||($e("rc-tabs-".concat(vt)),vt+=1)},[]);function fe(Z,se){D==null||D(Z,se);var ge=Z!==te;F(Z),ge&&(B==null||B(Z))}var ye={id:q,activeKey:te,animated:x,tabPosition:M,rtl:p,mobile:U},Se=(0,ee.Z)((0,ee.Z)({},ye),{},{editable:g,locale:m,more:h,tabBarGutter:H,onTabClick:fe,onTabScroll:G,extra:O,style:E,panes:null,getPopupContainer:_,popupClassName:T,indicator:w});return a.createElement(Le.Provider,{value:{tabs:C,prefixCls:i}},a.createElement("div",(0,ce.Z)({ref:t,id:n,className:Y()(i,"".concat(i,"-").concat(M),(0,X.Z)((0,X.Z)((0,X.Z)({},"".concat(i,"-mobile"),U),"".concat(i,"-editable"),g),"".concat(i,"-rtl"),p),l)},v),a.createElement(ua,(0,ce.Z)({},Se,{renderTabBar:N})),a.createElement(ma,(0,ce.Z)({destroyInactiveTabPane:R},ye,{animated:x}))))}),$a=pa,ya=$a,Sa=f(53124),Ca=f(35792),xa=f(98675),Ta=f(33603);const Pa={motionAppear:!1,motionEnter:!0,motionLeave:!0};function Ea(e,t={inkBar:!0,tabPane:!1}){let n;return t===!1?n={inkBar:!1,tabPane:!1}:t===!0?n={inkBar:!0,tabPane:!0}:n=Object.assign({inkBar:!0},typeof t=="object"?t:{}),n.tabPane&&(n.tabPaneMotion=Object.assign(Object.assign({},Pa),{motionName:(0,Ta.m)(e,"switch")})),n}var Ra=f(50344),wa=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};function Ia(e){return e.filter(t=>t)}function La(e,t){if(e)return e.map(r=>{var i;const l=(i=r.destroyOnHidden)!==null&&i!==void 0?i:r.destroyInactiveTabPane;return Object.assign(Object.assign({},r),{destroyInactiveTabPane:l})});const n=(0,Ra.Z)(t).map(r=>{if(a.isValidElement(r)){const{key:i,props:l}=r,c=l||{},{tab:o}=c,s=wa(c,["tab"]);return Object.assign(Object.assign({key:String(i)},s),{label:o})}return null});return Ia(n)}var y=f(11568),ve=f(14747),Ma=f(83559),Za=f(83262),ft=f(67771),za=e=>{const{componentCls:t,motionDurationSlow:n}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${n}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${n}`}}}}},[(0,ft.oN)(e,"slide-up"),(0,ft.oN)(e,"slide-down")]]};const Na=e=>{const{componentCls:t,tabsCardPadding:n,cardBg:r,cardGutter:i,colorBorderSecondary:l,itemSelectedColor:c}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:n,background:r,border:`${(0,y.bf)(e.lineWidth)} ${e.lineType} ${l}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:c,background:e.colorBgContainer},[`${t}-tab-focus:has(${t}-tab-btn:focus-visible)`]:(0,ve.oN)(e,-3),[`& ${t}-tab${t}-tab-focus ${t}-tab-btn:focus-visible`]:{outline:"none"},[`${t}-ink-bar`]:{visibility:"hidden"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:(0,y.bf)(i)}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${(0,y.bf)(e.borderRadiusLG)} ${(0,y.bf)(e.borderRadiusLG)} 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${(0,y.bf)(e.borderRadiusLG)} ${(0,y.bf)(e.borderRadiusLG)}`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:(0,y.bf)(i)}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,y.bf)(e.borderRadiusLG)} 0 0 ${(0,y.bf)(e.borderRadiusLG)}`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,y.bf)(e.borderRadiusLG)} ${(0,y.bf)(e.borderRadiusLG)} 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},_a=e=>{const{componentCls:t,itemHoverColor:n,dropdownEdgeChildVerticalPadding:r}=e;return{[`${t}-dropdown`]:Object.assign(Object.assign({},(0,ve.Wf)(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${(0,y.bf)(r)} 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":Object.assign(Object.assign({},ve.vS),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${(0,y.bf)(e.paddingXXS)} ${(0,y.bf)(e.paddingSM)}`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},Oa=e=>{const{componentCls:t,margin:n,colorBorderSecondary:r,horizontalMargin:i,verticalItemPadding:l,verticalItemMargin:c,calc:o}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:i,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${(0,y.bf)(e.lineWidth)} ${e.lineType} ${r}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},
            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,
        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:n,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:o(e.controlHeight).mul(1.25).equal(),[`${t}-tab`]:{padding:l,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:c},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:(0,y.bf)(o(e.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:`${(0,y.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:o(e.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:`${(0,y.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},Ba=e=>{const{componentCls:t,cardPaddingSM:n,cardPaddingLG:r,cardHeightSM:i,cardHeightLG:l,horizontalItemPaddingSM:c,horizontalItemPaddingLG:o}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:c,fontSize:e.titleFontSizeSM}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:o,fontSize:e.titleFontSizeLG,lineHeight:e.lineHeightLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:n},[`${t}-nav-add`]:{minWidth:i,minHeight:i}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${(0,y.bf)(e.borderRadius)} ${(0,y.bf)(e.borderRadius)}`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${(0,y.bf)(e.borderRadius)} ${(0,y.bf)(e.borderRadius)} 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,y.bf)(e.borderRadius)} ${(0,y.bf)(e.borderRadius)} 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,y.bf)(e.borderRadius)} 0 0 ${(0,y.bf)(e.borderRadius)}`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:r},[`${t}-nav-add`]:{minWidth:l,minHeight:l}}}}}},Da=e=>{const{componentCls:t,itemActiveColor:n,itemHoverColor:r,iconCls:i,tabsHorizontalItemMargin:l,horizontalItemPadding:c,itemSelectedColor:o,itemColor:s}=e,d=`${t}-tab`;return{[d]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:c,fontSize:e.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:s,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:n}},"&-btn":{outline:"none",transition:`all ${e.motionDurationSlow}`,[`${d}-icon:not(:last-child)`]:{marginInlineEnd:e.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:e.calc(e.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},(0,ve.Qy)(e)),"&:hover":{color:r},[`&${d}-active ${d}-btn`]:{color:o,textShadow:e.tabsActiveTextShadow},[`&${d}-focus ${d}-btn:focus-visible`]:(0,ve.oN)(e),[`&${d}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${d}-disabled ${d}-btn, &${d}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${d}-remove ${i}`]:{margin:0},[`${i}:not(:last-child)`]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${d} + ${d}`]:{margin:{_skip_check_:!0,value:l}}}},Aa=e=>{const{componentCls:t,tabsHorizontalItemMarginRTL:n,iconCls:r,cardGutter:i,calc:l}=e;return{[`${t}-rtl`]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:n},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[r]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:(0,y.bf)(e.marginSM)}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:(0,y.bf)(e.marginXS)},marginLeft:{_skip_check_:!0,value:(0,y.bf)(l(e.marginXXS).mul(-1).equal())},[r]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:i},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},Ha=e=>{const{componentCls:t,tabsCardPadding:n,cardHeight:r,cardGutter:i,itemHoverColor:l,itemActiveColor:c,colorBorderSecondary:o}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,ve.Wf)(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:n,background:"transparent",border:0,color:e.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.calc(e.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:Object.assign({minWidth:r,minHeight:r,marginLeft:{_skip_check_:!0,value:i},background:"transparent",border:`${(0,y.bf)(e.lineWidth)} ${e.lineType} ${o}`,borderRadius:`${(0,y.bf)(e.borderRadiusLG)} ${(0,y.bf)(e.borderRadiusLG)} 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:l},"&:active, &:focus:not(:focus-visible)":{color:c}},(0,ve.Qy)(e,-3))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.inkBarColor,pointerEvents:"none"}}),Da(e)),{[`${t}-content`]:{position:"relative",width:"100%"},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:Object.assign(Object.assign({},(0,ve.Qy)(e)),{"&-hidden":{display:"none"}})}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping']) > ${t}-nav-list`]:{margin:"auto"}}}}}},Wa=e=>{const{cardHeight:t,cardHeightSM:n,cardHeightLG:r,controlHeight:i,controlHeightLG:l}=e,c=t||l,o=n||i,s=r||l+8;return{zIndexPopup:e.zIndexPopupBase+50,cardBg:e.colorFillAlter,cardHeight:c,cardHeightSM:o,cardHeightLG:s,cardPadding:`${(c-e.fontHeight)/2-e.lineWidth}px ${e.padding}px`,cardPaddingSM:`${(o-e.fontHeight)/2-e.lineWidth}px ${e.paddingXS}px`,cardPaddingLG:`${(s-e.fontHeightLG)/2-e.lineWidth}px ${e.padding}px`,titleFontSize:e.fontSize,titleFontSizeLG:e.fontSizeLG,titleFontSizeSM:e.fontSize,inkBarColor:e.colorPrimary,horizontalMargin:`0 0 ${e.margin}px 0`,horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:`${e.paddingSM}px 0`,horizontalItemPaddingSM:`${e.paddingXS}px 0`,horizontalItemPaddingLG:`${e.padding}px 0`,verticalItemPadding:`${e.paddingXS}px ${e.paddingLG}px`,verticalItemMargin:`${e.margin}px 0 0 0`,itemColor:e.colorText,itemSelectedColor:e.colorPrimary,itemHoverColor:e.colorPrimaryHover,itemActiveColor:e.colorPrimaryActive,cardGutter:e.marginXXS/2}};var Ga=(0,Ma.I$)("Tabs",e=>{const t=(0,Za.IX)(e,{tabsCardPadding:e.cardPadding,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:`0 0 0 ${(0,y.bf)(e.horizontalItemGutter)}`,tabsHorizontalItemMarginRTL:`0 0 0 ${(0,y.bf)(e.horizontalItemGutter)}`});return[Ba(t),Aa(t),Oa(t),_a(t),Na(t),Ha(t),za(t)]},Wa),ja=()=>null,ka=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};const bt=e=>{var t,n,r,i,l,c,o,s,d,g,b;const{type:S,className:M,rootClassName:H,size:E,onEdit:O,hideAdd:m,centered:h,addIcon:R,removeIcon:N,moreIcon:B,more:D,popupClassName:G,children:_,items:T,animated:w,style:v,indicatorSize:C,indicator:p,destroyInactiveTabPane:x,destroyOnHidden:j}=e,k=ka(e,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator","destroyInactiveTabPane","destroyOnHidden"]),{prefixCls:U}=k,{direction:Q,tabs:$,getPrefixCls:re,getPopupContainer:te}=a.useContext(Sa.E_),F=re("tabs",U),P=(0,Ca.Z)(F),[W,K,V]=Ga(F,P);let J;S==="editable-card"&&(J={onEdit:(Z,{key:se,event:ge})=>{O==null||O(Z==="add"?ge:se,Z)},removeIcon:(t=N!=null?N:$==null?void 0:$.removeIcon)!==null&&t!==void 0?t:a.createElement(Zt.Z,null),addIcon:(R!=null?R:$==null?void 0:$.addIcon)||a.createElement(Dt,null),showAdd:m!==!0});const ie=re(),q=(0,xa.Z)(E),$e=La(T,_),fe=Ea(F,w),ye=Object.assign(Object.assign({},$==null?void 0:$.style),v),Se={align:(n=p==null?void 0:p.align)!==null&&n!==void 0?n:(r=$==null?void 0:$.indicator)===null||r===void 0?void 0:r.align,size:(o=(l=(i=p==null?void 0:p.size)!==null&&i!==void 0?i:C)!==null&&l!==void 0?l:(c=$==null?void 0:$.indicator)===null||c===void 0?void 0:c.size)!==null&&o!==void 0?o:$==null?void 0:$.indicatorSize};return W(a.createElement(ya,Object.assign({direction:Q,getPopupContainer:te},k,{items:$e,className:Y()({[`${F}-${q}`]:q,[`${F}-card`]:["card","editable-card"].includes(S),[`${F}-editable-card`]:S==="editable-card",[`${F}-centered`]:h},$==null?void 0:$.className,M,H,K,V,P),popupClassName:Y()(G,K,V,P),style:ye,editable:J,more:Object.assign({icon:(b=(g=(d=(s=$==null?void 0:$.more)===null||s===void 0?void 0:s.icon)!==null&&d!==void 0?d:$==null?void 0:$.moreIcon)!==null&&g!==void 0?g:B)!==null&&b!==void 0?b:a.createElement(zt.Z,null),transitionName:`${ie}-slide-up`},D),prefixCls:F,animated:fe,indicator:Se,destroyInactiveTabPane:j!=null?j:x})))};bt.TabPane=ja;var Ka=bt}}]);
