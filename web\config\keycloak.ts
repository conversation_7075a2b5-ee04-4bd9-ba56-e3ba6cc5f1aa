import Keycloak from 'keycloak-js';

// 检测是否支持Web Crypto API
const isWebCryptoSupported = () => {
    if (typeof window === 'undefined') return false;

    // 基本检查
    const hasWebCrypto = window.crypto && window.crypto.subtle;

    // 检查是否在安全上下文中
    const isSecure = window.isSecureContext ||
                    window.location.protocol === 'https:' ||
                    window.location.hostname === 'localhost' ||
                    window.location.hostname === '127.0.0.1';

    // 检查代理头（通过meta标签或其他方式）
    const hasProxyHeaders = checkForProxyHeaders();

    // 如果有代理头指示HTTPS，则认为支持
    if (hasProxyHeaders && hasWebCrypto) {
        console.log('检测到HTTPS代理头，启用Web Crypto API支持');
        return true;
    }

    return hasWebCrypto && isSecure;
};

// 检测是否为HTTPS环境（包括代理情况）
const isSecureContext = () => {
    if (typeof window === 'undefined') return false;

    const isHTTPS = window.location.protocol === 'https:';
    const isLocalhost = window.location.hostname === 'localhost' ||
                       window.location.hostname === '127.0.0.1';
    const isSecureCtx = window.isSecureContext;
    const hasProxyHeaders = checkForProxyHeaders();

    return isHTTPS || isLocalhost || isSecureCtx || hasProxyHeaders;
};

// 检查代理头指示
const checkForProxyHeaders = () => {
    if (typeof window === 'undefined') return false;

    // 方法1：检查meta标签
    const metaProto = document.querySelector('meta[name="x-forwarded-proto"]');
    if (metaProto && metaProto.getAttribute('content') === 'https') {
        return true;
    }

    // 方法2：检查自定义属性
    const secureContext = document.querySelector('meta[name="x-secure-context"]');
    if (secureContext && secureContext.getAttribute('content') === 'true') {
        return true;
    }

    // 方法3：通过全局变量
    if ((window as any).__SECURE_CONTEXT__ === true) {
        return true;
    }

    return false;
};

const keycloakConfig = {
    url: 'https://*************:9088', // 使用远端Keycloak服务器
    realm: 'dev_xh_key',
    clientId: 'sulei_01',
    // 根据环境动态设置PKCE方法
    pkceMethod: isWebCryptoSupported() ? 'S256' : undefined,
    checkLoginIframe: false,
    // 移除onLoad配置，让服务层控制
};

const keycloak = new Keycloak(keycloakConfig);

export default keycloak;
export { isWebCryptoSupported, isSecureContext };