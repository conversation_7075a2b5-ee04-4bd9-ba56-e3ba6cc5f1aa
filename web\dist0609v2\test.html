<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
    <style>
        #main {
            width: 500px;
            height: 500px;
            margin: 0 auto;
        }
    </style></head>
<body>
    <div id="main"></div>
    <script type="text/javascript">
        // 基于准备好的 DOM，初始化 echarts 实例
        var myChart = echarts.init(document.getElementById('main'));

        // 构建数据
        var nodes = [];
        var links = [];
        var categories = [
            {name: '计算节点'},
            {name: '存储节点'},
            {name: '管理节点'}
        ];

        for (var i = 1; i <= 50; i++) {
            var nodeCategory = Math.floor(Math.random() * 3); // 随机分配节点类别
            nodes.push({
                name: '节点' + i,
                symbolSize: 30,
                category: categories[nodeCategory].name
            });
            // 随机添加连接
            if (i > 1) {
                links.push({source: '节点' + (Math.floor(Math.random() * (i - 1)) + 1), target: '节点' + i});
            }
        }

        // 指定图表的配置项和数据
        var option = {
            tooltip: {},
            legend: [{data: categories.map(function (c) { return c.name; })}],
            series : [
                {
                    name: '智算中心集群',
                    type: 'graph',
                    layout: 'force',
                    data: nodes,
                    links: links,
                    categories: categories,
                    roam: true,
                    label: {
                        normal: {
                            show: true,
                            position: 'right'
                        }
                    },
                    force: {
                        repulsion: 1500,
                        edgeLength: [100, 200]
                    },
                    focusNodeAdjacency: true
                }
            ]
        };

        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
    </script>
</body>
</html>