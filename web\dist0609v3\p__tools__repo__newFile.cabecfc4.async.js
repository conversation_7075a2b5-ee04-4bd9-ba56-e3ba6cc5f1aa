"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4876],{89035:function(V,D,e){e.d(D,{Z:function(){return g}});var d=e(1413),j=e(67294),A={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M516 673c0 4.4 3.4 8 7.5 8h185c4.1 0 7.5-3.6 7.5-8v-48c0-4.4-3.4-8-7.5-8h-185c-4.1 0-7.5 3.6-7.5 8v48zm-194.9 6.1l192-161c3.8-3.2 3.8-9.1 0-12.3l-192-160.9A7.95 7.95 0 00308 351v62.7c0 2.4 1 4.6 2.9 6.1L420.7 512l-109.8 92.2a8.1 8.1 0 00-2.9 6.1V673c0 6.8 7.9 10.5 13.1 6.1zM880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"code",theme:"outlined"},$=A,K=e(91146),E=function(F,l){return j.createElement(K.Z,(0,d.Z)((0,d.Z)({},F),{},{ref:l,icon:$}))},R=j.forwardRef(E),g=R},85425:function(V,D,e){var d=e(5574),j=e.n(d),A=e(20057),$=e(85673),K=e(84226),E=e(31622),R=e(67294),g=e(85893),C=function(l){var w=l.proName,v=l.currentPath,b=l.customItems,m=(0,A.TH)(),u=(0,A.s0)(),M=(0,K.useIntl)(),n=(0,R.useState)((0,E.gh)()||"dark"),r=j()(n,2),s=r[0],a=r[1];(0,R.useEffect)(function(){a((0,E.gh)());var O=new MutationObserver(function(t){t.forEach(function(c){c.attributeName==="data-theme"&&a((0,E.gh)())})});return O.observe(document.body,{attributes:!0}),function(){O.disconnect()}},[]);var i=function(){if(b)return b;var t=m.pathname,c=[{path:"/tools",breadcrumbName:"\u5DE5\u5177\u5957\u4EF6"},{path:"/tools/repo",breadcrumbName:"\u4EE3\u7801\u4ED3\u5E93"}];if(t.startsWith("/tools/repo/newProject"))return[].concat(c,[{path:"/tools/repo/newProject",breadcrumbName:"\u65B0\u5EFA\u9879\u76EE"}]);if(t.startsWith("/tools/repo/files/newFile"))return[].concat(c,[{path:"/tools/repo/files",breadcrumbName:"\u6587\u4EF6\u7BA1\u7406"},{path:"/tools/repo/files/newFile",breadcrumbName:"\u65B0\u5EFA\u6587\u4EF6"}]);if(t.startsWith("/tools/repo/files")){var h=[].concat(c,[{path:"/tools/repo/files",breadcrumbName:"\u6587\u4EF6\u7BA1\u7406"}]);if(w&&h.push({path:"/tools/repo/files",breadcrumbName:w}),v){var L=v.split("/").filter(Boolean),_="";L.forEach(function(P,W){_+="/".concat(P),h.push({path:"/tools/repo/files?path=".concat(_),breadcrumbName:P})})}return h}else{if(t.startsWith("/tools/repo/branch"))return[].concat(c,[{path:"/tools/repo/branch",breadcrumbName:"\u5206\u652F\u7BA1\u7406"}]);if(t.startsWith("/tools/repo/commits"))return[].concat(c,[{path:"/tools/repo/commits",breadcrumbName:"\u63D0\u4EA4\u7BA1\u7406"}]);if(t.startsWith("/tools/repo/compare"))return[].concat(c,[{path:"/tools/repo/compare",breadcrumbName:"\u6BD4\u8F83\u4FEE\u8BA2\u7248\u672C"}]);if(t.startsWith("/tools/repo/newTag"))return[].concat(c,[{path:"/tools/repo/tags",breadcrumbName:"\u6807\u7B7E"},{path:"/tools/repo/newTag",breadcrumbName:"\u65B0\u5EFA\u6807\u7B7E"}]);if(t.startsWith("/tools/repo/tags"))return[].concat(c,[{path:"/tools/repo/tags",breadcrumbName:"\u6807\u7B7E"}])}return c};return(0,g.jsx)($.Z,{separator:"/",style:{fontSize:"14px",marginBottom:"16px",color:s==="light"?"#666":"#aaa"},items:i(),itemRender:function(t,c,h){var L=h.indexOf(t)===h.length-1;return L?(0,g.jsx)("span",{style:{color:s==="light"?"#333":"#fff"},children:t.breadcrumbName}):(0,g.jsx)("a",{onClick:function(){return t.path?u(t.path):null},style:{color:s==="light"?"#666":"#aaa"},children:t.breadcrumbName})}})};D.Z=C},70746:function(V,D,e){e.r(D);var d=e(15009),j=e.n(d),A=e(19632),$=e.n(A),K=e(99289),E=e.n(K),R=e(5574),g=e.n(R),C=e(67294),F=e(84017),l=e(55102),w=e(71471),v=e(2453),b=e(71230),m=e(15746),u=e(78957),M=e(34041),n=e(83622),r=e(96074),s=e(20057),a=e(7528),i=e(89035),O=e(85425),t=e(85893),c=l.Z.TextArea,h=w.Z.Text,L=function(){var P=(0,s.TH)(),W=(0,s.s0)(),H=(0,C.useState)(""),B=g()(H,2),Z=B[0],J=B[1],N=(0,C.useState)(""),o=g()(N,2),p=o[0],T=o[1],k=(0,C.useState)("\u6DFB\u52A0\u65B0\u6587\u4EF6"),X=g()(k,2),G=X[0],Q=X[1],ee=(0,C.useState)([]),te=g()(ee,2),ie=te[0],de=te[1],re=(0,C.useState)(""),ne=g()(re,2),ue=ne[0],pe=ne[1],Ee=(0,C.useState)(null),he=g()(Ee,2),we=he[0],Oe=he[1],Ce=(0,C.useState)({}),_e=g()(Ce,2),z=_e[0],ce=_e[1],Pe=(0,C.useState)(!1),ve=g()(Pe,2),je=ve[0],ae=ve[1],ye=(0,C.useState)(!1),be=g()(ye,2),se=be[0],De=be[1],Me=[{value:"text",label:"Plain text"},{value:"markdown",label:"Markdown"},{value:"html",label:"HTML"},{value:"javascript",label:"JavaScript"},{value:"python",label:"Python"},{value:"java",label:"Java"},{value:"c",label:"C"},{value:"cpp",label:"C++"},{value:"csharp",label:"C#"},{value:"go",label:"Go"},{value:"ruby",label:"Ruby"},{value:"php",label:"PHP"},{value:"rust",label:"Rust"},{value:"swift",label:"Swift"},{value:"typescript",label:"TypeScript"},{value:"json",label:"JSON"},{value:"yaml",label:"YAML"},{value:"dockerfile",label:"Dockerfile"},{value:"gitignore",label:".gitignore"}];(0,C.useEffect)(function(){var U=function(){var S=E()(j()().mark(function x(){var f,q,fe,I,Y,oe,ge,le;return j()().wrap(function(y){for(;;)switch(y.prev=y.next){case 0:if(ae(!0),y.prev=1,f=new URLSearchParams(P.search),q=f.get("proid"),!(P.state&&P.state.proId)){y.next=8;break}ce({proId:P.state.proId,proName:P.state.proName,defaultBranch:P.state.defaultBranch}),y.next=21;break;case 8:if(!q){y.next=17;break}return fe=parseInt(q),y.next=12,(0,a.hW)();case 12:I=y.sent,Y=I.find(function(me){return me.id===fe}),Y?ce({proId:Y.id,proName:Y.name,defaultBranch:Y.default_branch}):(v.ZP.error("\u672A\u627E\u5230\u9879\u76EE\u4FE1\u606F"),W("/tools/repo")),y.next=21;break;case 17:return y.next=19,(0,a.hW)();case 19:oe=y.sent,oe&&oe.length>0?(ge=$()(oe).sort(function(me,$e){var Be=new Date(me.last_activity_at||0),Te=new Date($e.last_activity_at||0);return Te.getTime()-Be.getTime()}),le=ge[0],ce({proId:le.id,proName:le.name,defaultBranch:le.default_branch})):(v.ZP.error("\u672A\u627E\u5230\u9879\u76EE\u4FE1\u606F"),W("/tools/repo"));case 21:y.next=27;break;case 23:y.prev=23,y.t0=y.catch(1),console.error("\u83B7\u53D6\u9879\u76EE\u4FE1\u606F\u5931\u8D25:",y.t0),v.ZP.error("\u83B7\u53D6\u9879\u76EE\u4FE1\u606F\u5931\u8D25");case 27:return y.prev=27,ae(!1),y.finish(27);case 30:case"end":return y.stop()}},x,null,[[1,23,27,30]])}));return function(){return S.apply(this,arguments)}}();U()},[P,W]),(0,C.useEffect)(function(){var U=function(){var S=E()(j()().mark(function x(){var f,q;return j()().wrap(function(I){for(;;)switch(I.prev=I.next){case 0:if(z.proId){I.next=2;break}return I.abrupt("return");case 2:return I.prev=2,I.next=5,(0,a.$y)({id:z.proId});case 5:f=I.sent,de(f),z.defaultBranch?pe(z.defaultBranch):f.length>0&&(q=f.find(function(Y){return Y.default})||f[0],pe(q.name)),I.next=14;break;case 10:I.prev=10,I.t0=I.catch(2),console.error("\u83B7\u53D6\u5206\u652F\u4FE1\u606F\u5931\u8D25:",I.t0),v.ZP.error("\u83B7\u53D6\u5206\u652F\u4FE1\u606F\u5931\u8D25");case 14:case"end":return I.stop()}},x,null,[[2,10]])}));return function(){return S.apply(this,arguments)}}();U()},[z.proId,z.defaultBranch]);var Se=function(S){Oe(S);var x="";switch(S){case"markdown":x=`# Title

## Subtitle

Content goes here.

- List item 1
- List item 2
- List item 3

\`\`\`code
code block
\`\`\``;break;case"html":x=`<!DOCTYPE html>
<html>
<head>
  <title>Title</title>
</head>
<body>
  <h1>Hello World</h1>
</body>
</html>`;break;case"javascript":x=`// JavaScript code
function helloWorld() {
  console.log("Hello, World!");
}

helloWorld();`;break;case"python":x=`# Python code
def hello_world():
    print("Hello, World!")

if __name__ == "__main__":
    hello_world()`;break;case"java":x=`public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}`;break;case"json":x=`{
  "name": "project",
  "version": "1.0.0",
  "description": "A sample project",
  "main": "index.js",
  "scripts": {
    "test": "echo \\"Error: no test specified\\" && exit 1"
  },
  "author": "",
  "license": "ISC"
}`;break;case"gitignore":x=`# Logs
logs
*.log

# Dependency directories
node_modules/

# Build outputs
dist/
build/

# Environment variables
.env
.env.local

# IDE specific files
.idea/
.vscode/
*.swp
*.swo`;break;default:x=""}T(x)},xe=function(){var U=E()(j()().mark(function S(){return j()().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:if(Z){f.next=3;break}return v.ZP.error("\u8BF7\u8F93\u5165\u6587\u4EF6\u540D"),f.abrupt("return");case 3:if(ue){f.next=6;break}return v.ZP.error("\u8BF7\u9009\u62E9\u76EE\u6807\u5206\u652F"),f.abrupt("return");case 6:if(z.proId){f.next=9;break}return v.ZP.error("\u9879\u76EEID\u4E0D\u5B58\u5728"),f.abrupt("return");case 9:return f.prev=9,ae(!0),f.next=13,(0,a.ae)({id:z.proId,file_path:Z,branch:ue,content:p,commit_message:G});case 13:v.ZP.success("\u6587\u4EF6\u521B\u5EFA\u6210\u529F"),W("/tools/repo/files?proid=".concat(z.proId)),f.next=21;break;case 17:f.prev=17,f.t0=f.catch(9),console.error("\u521B\u5EFA\u6587\u4EF6\u5931\u8D25:",f.t0),v.ZP.error("\u521B\u5EFA\u6587\u4EF6\u5931\u8D25");case 21:return f.prev=21,ae(!1),f.finish(21);case 24:case"end":return f.stop()}},S,null,[[9,17,21,24]])}));return function(){return U.apply(this,arguments)}}(),Ie=function(){W("/tools/repo/files?proid=".concat(z.proId))};return(0,t.jsxs)(F._z,{header:{title:"",breadcrumb:{}},children:[(0,t.jsx)(O.Z,{customItems:[{path:"/tools",breadcrumbName:"\u5DE5\u5177\u5957\u4EF6"},{path:"/tools/repo",breadcrumbName:"\u4EE3\u7801"},{path:"/tools/repo/files",breadcrumbName:"\u6587\u4EF6\u7BA1\u7406"},{path:"",breadcrumbName:"\u65B0\u5EFA\u6587\u4EF6"}]}),(0,t.jsx)(w.Z.Title,{level:2,children:"\u65B0\u5EFA\u6587\u4EF6"}),(0,t.jsxs)(b.Z,{gutter:[16,16],align:"middle",children:[(0,t.jsx)(m.Z,{children:(0,t.jsxs)(u.Z,{children:[(0,t.jsx)(i.Z,{}),(0,t.jsx)(h,{strong:!0,children:z.defaultBranch}),(0,t.jsx)(h,{children:"/"})]})}),(0,t.jsx)(m.Z,{flex:"auto",children:(0,t.jsx)(l.Z,{placeholder:"\u6587\u4EF6\u540D",value:Z,onChange:function(S){return J(S.target.value)},style:{width:"100%"}})}),(0,t.jsx)(m.Z,{children:(0,t.jsx)(M.Z,{placeholder:"\u9009\u62E9\u6A21\u677F\u7C7B\u578B",style:{width:200},onChange:function(S){return Se(S)},options:Me,allowClear:!0,suffixIcon:(0,t.jsx)("span",{children:"\u25BC"})})}),(0,t.jsx)(m.Z,{children:(0,t.jsx)(n.ZP,{type:se?"primary":"default",onClick:function(){return De(!se)},children:"\u4E0D\u6362\u884C"})})]}),(0,t.jsx)(r.Z,{}),(0,t.jsx)(b.Z,{children:(0,t.jsx)(m.Z,{span:24,children:(0,t.jsxs)("div",{style:{position:"relative"},children:[(0,t.jsx)("div",{style:{position:"absolute",left:0,top:0,bottom:0,width:"30px",backgroundColor:"#f5f5f5",borderRight:"1px solid #e8e8e8",textAlign:"right",paddingRight:"8px",color:"#999"},children:"1"}),(0,t.jsx)(c,{value:p,onChange:function(S){return T(S.target.value)},style:{width:"100%",minHeight:"400px",paddingLeft:"40px",fontFamily:"monospace",whiteSpace:se?"normal":"pre",overflowX:se?"auto":"scroll"},placeholder:"\u5728\u6B64\u5904\u8F93\u5165\u6587\u4EF6\u5185\u5BB9..."})]})})}),(0,t.jsx)(r.Z,{}),(0,t.jsxs)(b.Z,{gutter:[16,16],children:[(0,t.jsxs)(m.Z,{span:24,children:[(0,t.jsx)(h,{strong:!0,children:"\u63D0\u4EA4\u4FE1\u606F"}),(0,t.jsx)(l.Z,{value:G,onChange:function(S){return Q(S.target.value)},placeholder:"\u6DFB\u52A0\u65B0\u6587\u4EF6",style:{width:"100%"}})]}),(0,t.jsxs)(m.Z,{span:24,children:[(0,t.jsx)(h,{strong:!0,children:"\u76EE\u6807\u5206\u652F"}),(0,t.jsx)(l.Z,{value:ue,style:{width:"100%"}})]})]}),(0,t.jsxs)(b.Z,{justify:"space-between",style:{marginTop:24},children:[(0,t.jsx)(m.Z,{children:(0,t.jsx)(n.ZP,{onClick:Ie,children:"\u53D6\u6D88"})}),(0,t.jsx)(m.Z,{children:(0,t.jsx)(n.ZP,{type:"primary",onClick:xe,loading:je,children:"\u63D0\u4EA4\u66F4\u6539"})})]})]})};D.default=L},15746:function(V,D,e){var d=e(21584);D.Z=d.Z},96074:function(V,D,e){e.d(D,{Z:function(){return M}});var d=e(67294),j=e(93967),A=e.n(j),$=e(53124),K=e(98675),E=e(11568),R=e(14747),g=e(83559),C=e(83262);const F=n=>{const{componentCls:r}=n;return{[r]:{"&-horizontal":{[`&${r}`]:{"&-sm":{marginBlock:n.marginXS},"&-md":{marginBlock:n.margin}}}}}},l=n=>{const{componentCls:r,sizePaddingEdgeHorizontal:s,colorSplit:a,lineWidth:i,textPaddingInline:O,orientationMargin:t,verticalMarginInline:c}=n;return{[r]:Object.assign(Object.assign({},(0,R.Wf)(n)),{borderBlockStart:`${(0,E.bf)(i)} solid ${a}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:c,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${(0,E.bf)(i)} solid ${a}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${(0,E.bf)(n.marginLG)} 0`},[`&-horizontal${r}-with-text`]:{display:"flex",alignItems:"center",margin:`${(0,E.bf)(n.dividerHorizontalWithTextGutterMargin)} 0`,color:n.colorTextHeading,fontWeight:500,fontSize:n.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${a}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${(0,E.bf)(i)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${r}-with-text-start`]:{"&::before":{width:`calc(${t} * 100%)`},"&::after":{width:`calc(100% - ${t} * 100%)`}},[`&-horizontal${r}-with-text-end`]:{"&::before":{width:`calc(100% - ${t} * 100%)`},"&::after":{width:`calc(${t} * 100%)`}},[`${r}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:O},"&-dashed":{background:"none",borderColor:a,borderStyle:"dashed",borderWidth:`${(0,E.bf)(i)} 0 0`},[`&-horizontal${r}-with-text${r}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${r}-dashed`]:{borderInlineStartWidth:i,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:a,borderStyle:"dotted",borderWidth:`${(0,E.bf)(i)} 0 0`},[`&-horizontal${r}-with-text${r}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${r}-dotted`]:{borderInlineStartWidth:i,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${r}-with-text`]:{color:n.colorText,fontWeight:"normal",fontSize:n.fontSize},[`&-horizontal${r}-with-text-start${r}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${r}-inner-text`]:{paddingInlineStart:s}},[`&-horizontal${r}-with-text-end${r}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${r}-inner-text`]:{paddingInlineEnd:s}}})}},w=n=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:n.marginXS});var v=(0,g.I$)("Divider",n=>{const r=(0,C.IX)(n,{dividerHorizontalWithTextGutterMargin:n.margin,sizePaddingEdgeHorizontal:0});return[l(r),F(r)]},w,{unitless:{orientationMargin:!0}}),b=function(n,r){var s={};for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&r.indexOf(a)<0&&(s[a]=n[a]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,a=Object.getOwnPropertySymbols(n);i<a.length;i++)r.indexOf(a[i])<0&&Object.prototype.propertyIsEnumerable.call(n,a[i])&&(s[a[i]]=n[a[i]]);return s};const m={small:"sm",middle:"md"};var M=n=>{const{getPrefixCls:r,direction:s,className:a,style:i}=(0,$.dj)("divider"),{prefixCls:O,type:t="horizontal",orientation:c="center",orientationMargin:h,className:L,rootClassName:_,children:P,dashed:W,variant:H="solid",plain:B,style:Z,size:J}=n,N=b(n,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),o=r("divider",O),[p,T,k]=v(o),X=(0,K.Z)(J),G=m[X],Q=!!P,ee=d.useMemo(()=>c==="left"?s==="rtl"?"end":"start":c==="right"?s==="rtl"?"start":"end":c,[s,c]),te=ee==="start"&&h!=null,ie=ee==="end"&&h!=null,de=A()(o,a,T,k,`${o}-${t}`,{[`${o}-with-text`]:Q,[`${o}-with-text-${ee}`]:Q,[`${o}-dashed`]:!!W,[`${o}-${H}`]:H!=="solid",[`${o}-plain`]:!!B,[`${o}-rtl`]:s==="rtl",[`${o}-no-default-orientation-margin-start`]:te,[`${o}-no-default-orientation-margin-end`]:ie,[`${o}-${G}`]:!!G},L,_),re=d.useMemo(()=>typeof h=="number"?h:/^\d+$/.test(h)?Number(h):h,[h]),ne={marginInlineStart:te?re:void 0,marginInlineEnd:ie?re:void 0};return p(d.createElement("div",Object.assign({className:de,style:Object.assign(Object.assign({},i),Z)},N,{role:"separator"}),P&&t!=="vertical"&&d.createElement("span",{className:`${o}-inner-text`,style:ne},P)))}},99134:function(V,D,e){var d=e(67294);const j=(0,d.createContext)({});D.Z=j},21584:function(V,D,e){var d=e(67294),j=e(93967),A=e.n(j),$=e(53124),K=e(99134),E=e(6999),R=function(l,w){var v={};for(var b in l)Object.prototype.hasOwnProperty.call(l,b)&&w.indexOf(b)<0&&(v[b]=l[b]);if(l!=null&&typeof Object.getOwnPropertySymbols=="function")for(var m=0,b=Object.getOwnPropertySymbols(l);m<b.length;m++)w.indexOf(b[m])<0&&Object.prototype.propertyIsEnumerable.call(l,b[m])&&(v[b[m]]=l[b[m]]);return v};function g(l){return typeof l=="number"?`${l} ${l} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(l)?`0 0 ${l}`:l}const C=["xs","sm","md","lg","xl","xxl"],F=d.forwardRef((l,w)=>{const{getPrefixCls:v,direction:b}=d.useContext($.E_),{gutter:m,wrap:u}=d.useContext(K.Z),{prefixCls:M,span:n,order:r,offset:s,push:a,pull:i,className:O,children:t,flex:c,style:h}=l,L=R(l,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),_=v("col",M),[P,W,H]=(0,E.cG)(_),B={};let Z={};C.forEach(o=>{let p={};const T=l[o];typeof T=="number"?p.span=T:typeof T=="object"&&(p=T||{}),delete L[o],Z=Object.assign(Object.assign({},Z),{[`${_}-${o}-${p.span}`]:p.span!==void 0,[`${_}-${o}-order-${p.order}`]:p.order||p.order===0,[`${_}-${o}-offset-${p.offset}`]:p.offset||p.offset===0,[`${_}-${o}-push-${p.push}`]:p.push||p.push===0,[`${_}-${o}-pull-${p.pull}`]:p.pull||p.pull===0,[`${_}-rtl`]:b==="rtl"}),p.flex&&(Z[`${_}-${o}-flex`]=!0,B[`--${_}-${o}-flex`]=g(p.flex))});const J=A()(_,{[`${_}-${n}`]:n!==void 0,[`${_}-order-${r}`]:r,[`${_}-offset-${s}`]:s,[`${_}-push-${a}`]:a,[`${_}-pull-${i}`]:i},O,Z,W,H),N={};if(m&&m[0]>0){const o=m[0]/2;N.paddingLeft=o,N.paddingRight=o}return c&&(N.flex=g(c),u===!1&&!N.minWidth&&(N.minWidth=0)),P(d.createElement("div",Object.assign({},L,{style:Object.assign(Object.assign(Object.assign({},N),h),B),className:J,ref:w}),t))});D.Z=F},17621:function(V,D,e){e.d(D,{Z:function(){return m}});var d=e(67294),j=e(93967),A=e.n(j),$=e(74443),K=e(53124),E=e(25378);function R(u,M){const n=[void 0,void 0],r=Array.isArray(u)?u:[u,void 0],s=M||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return r.forEach((a,i)=>{if(typeof a=="object"&&a!==null)for(let O=0;O<$.c4.length;O++){const t=$.c4[O];if(s[t]&&a[t]!==void 0){n[i]=a[t];break}}else n[i]=a}),n}var g=e(99134),C=e(6999),F=function(u,M){var n={};for(var r in u)Object.prototype.hasOwnProperty.call(u,r)&&M.indexOf(r)<0&&(n[r]=u[r]);if(u!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(u);s<r.length;s++)M.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(u,r[s])&&(n[r[s]]=u[r[s]]);return n};const l=null,w=null;function v(u,M){const[n,r]=d.useState(typeof u=="string"?u:""),s=()=>{if(typeof u=="string"&&r(u),typeof u=="object")for(let a=0;a<$.c4.length;a++){const i=$.c4[a];if(!M||!M[i])continue;const O=u[i];if(O!==void 0){r(O);return}}};return d.useEffect(()=>{s()},[JSON.stringify(u),M]),n}var m=d.forwardRef((u,M)=>{const{prefixCls:n,justify:r,align:s,className:a,style:i,children:O,gutter:t=0,wrap:c}=u,h=F(u,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:L,direction:_}=d.useContext(K.E_),P=(0,E.Z)(!0,null),W=v(s,P),H=v(r,P),B=L("row",n),[Z,J,N]=(0,C.VM)(B),o=R(t,P),p=A()(B,{[`${B}-no-wrap`]:c===!1,[`${B}-${H}`]:H,[`${B}-${W}`]:W,[`${B}-rtl`]:_==="rtl"},a,J,N),T={},k=o[0]!=null&&o[0]>0?o[0]/-2:void 0;k&&(T.marginLeft=k,T.marginRight=k);const[X,G]=o;T.rowGap=G;const Q=d.useMemo(()=>({gutter:[X,G],wrap:c}),[X,G,c]);return Z(d.createElement(g.Z.Provider,{value:Q},d.createElement("div",Object.assign({},h,{className:p,style:Object.assign(Object.assign({},T),i),ref:M}),O)))})},71230:function(V,D,e){var d=e(17621);D.Z=d.Z}}]);
