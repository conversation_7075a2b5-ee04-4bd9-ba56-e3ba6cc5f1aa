"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7647],{20926:function(ge,U,t){t.r(U);var a=t(15009),W=t.n(a),Q=t(52677),K=t.n(Q),J=t(97857),N=t.n(J),Y=t(99289),B=t.n(Y),q=t(5574),ee=t.n(q),F=t(67294),oe=t(40056),ne=t(74330),E=t(84226),T=t(18883),f=t(85893),D=function(){var te=(0,F.useState)(null),Z=ee()(te,2),z=Z[0],H=Z[1],k=(0,E.useModel)("@@initialState"),G=k.setInitialState;return(0,F.useEffect)(function(){var re=function(){var se=B()(W()().mark(function ae(){var g,b,w,A,V,S,R,L,X,_,d,C,o,n,r;return W()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,console.log("=== \u5904\u7406Keycloak\u56DE\u8C03\u5F00\u59CB ==="),console.log("\u5F53\u524DURL:",window.location.href),g=new URLSearchParams(window.location.search),b=new URLSearchParams(window.location.hash.substring(1)),w=g.get("code")||b.get("code"),A=g.get("state")||b.get("state"),V=g.get("session_state")||b.get("session_state"),S=g.get("error")||b.get("error"),R=g.get("error_description")||b.get("error_description"),console.log("\u56DE\u8C03\u53C2\u6570:",{code:w?"\u5B58\u5728":"\u4E0D\u5B58\u5728",state:A,sessionState:V,error:S,errorDescription:R}),!S){e.next=13;break}throw new Error("Keycloak\u9519\u8BEF: ".concat(S," - ").concat(R||"\u672A\u77E5\u9519\u8BEF"));case 13:if(!w){e.next=40;break}return console.log("\u68C0\u6D4B\u5230\u6388\u6743\u7801\uFF0C\u5F00\u59CB\u521D\u59CB\u5316Keycloak"),e.next=17,T.ZP.init({onLoad:"login-required",checkLoginIframe:!1,redirectUri:window.location.origin+"/auth/callback",silentCheckSsoRedirectUri:window.location.origin+"/silent-check-sso.html",pkceMethod:"S256"});case 17:if(L=e.sent,console.log("Keycloak\u521D\u59CB\u5316\u7ED3\u679C:",L),!(L&&T.ZP.tokenParsed)){e.next=37;break}console.log("Keycloak\u8BA4\u8BC1\u6210\u529F\uFF0C\u8BBE\u7F6E\u7528\u6237\u72B6\u6001"),_=T.ZP.tokenParsed,d={id:_.sub||"",username:_.preferred_username||"",email:_.email||"",name:_.name||_.preferred_username||"",roles:((X=_.realm_access)===null||X===void 0?void 0:X.roles)||[]},console.log("\u63D0\u53D6\u7684\u7528\u6237\u4FE1\u606F:",d),C=d.roles,o="user",C.includes("owner")?o="owner":C.includes("admin")?o="admin":C.includes("maintainer")&&(o="maintainer"),console.log("\u7528\u6237\u89D2\u8272\u4FE1\u606F:",{roles:C,access:o}),console.log("\u5F00\u59CB\u66F4\u65B0\u5168\u5C40\u72B6\u6001..."),G(function(l){return N()(N()({},l),{},{currentUser:{userid:d.id,name:d.name,username:d.username,email:d.email,avatar:"https://ui-avatars.com/api/?name=".concat(encodeURIComponent(d.name||d.username),"&background=1890ff&color=fff"),roles:C,access:o}})}),console.log("\u5168\u5C40\u72B6\u6001\u66F4\u65B0\u5B8C\u6210"),n=sessionStorage.getItem("auth_redirect_path")||"/Console/projects",sessionStorage.removeItem("auth_redirect_path"),console.log("\u8BA4\u8BC1\u6210\u529F\uFF0C\u51C6\u5907\u91CD\u5B9A\u5411\u5230:",n),setTimeout(function(){console.log("\u6267\u884C\u91CD\u5B9A\u5411\u5230:",n),E.history.replace(n)},500),e.next=38;break;case 37:throw new Error("Keycloak\u8BA4\u8BC1\u5931\u8D25\u6216token\u89E3\u6790\u5931\u8D25");case 38:e.next=43;break;case 40:console.log("\u6CA1\u6709\u6388\u6743\u7801\uFF0C\u91CD\u5B9A\u5411\u5230\u767B\u5F55\u9875\u9762"),H("\u65E0\u6548\u7684\u56DE\u8C03\u8BBF\u95EE\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"),setTimeout(function(){E.history.replace("/user/login")},2e3);case 43:e.next=53;break;case 45:e.prev=45,e.t0=e.catch(0),console.error("\u56DE\u8C03\u5904\u7406\u5931\u8D25:",e.t0),console.error("\u9519\u8BEF\u8BE6\u60C5:",{message:e.t0===null||e.t0===void 0?void 0:e.t0.message,stack:e.t0===null||e.t0===void 0?void 0:e.t0.stack,type:K()(e.t0)}),r="\u672A\u77E5\u9519\u8BEF",e.t0&&K()(e.t0)==="object"?e.t0.message?r=e.t0.message:e.t0.toString&&typeof e.t0.toString=="function"&&(r=e.t0.toString()):typeof e.t0=="string"&&(r=e.t0),H("\u56DE\u8C03\u5904\u7406\u5931\u8D25: ".concat(r)),setTimeout(function(){E.history.replace("/user/login")},3e3);case 53:case"end":return e.stop()}},ae,null,[[0,45]])}));return function(){return se.apply(this,arguments)}}();re()},[G]),(0,f.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",flexDirection:"column",padding:"20px",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",color:"white"},children:z?(0,f.jsx)(oe.Z,{message:"\u8BA4\u8BC1\u9519\u8BEF",description:z,type:"error",showIcon:!0,style:{marginBottom:16}}):(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(ne.Z,{size:"large",style:{color:"white"}}),(0,f.jsx)("div",{style:{marginTop:24,fontSize:"18px",fontWeight:500,textAlign:"center"},children:"\u767B\u5F55\u6210\u529F\uFF01\u6B63\u5728\u8DF3\u8F6C..."}),(0,f.jsx)("div",{style:{marginTop:8,fontSize:"14px",opacity:.8,textAlign:"center"},children:"\u8BF7\u7A0D\u5019\uFF0C\u5373\u5C06\u8FDB\u5165\u7CFB\u7EDF"})]})})};U.default=D},40056:function(ge,U,t){t.d(U,{Z:function(){return C}});var a=t(67294),W=t(19735),Q=t(17012),K=t(62208),J=t(29950),N=t(1558),Y=t(93967),B=t.n(Y),q=t(29372),ee=t(64217),F=t(42550),oe=t(96159),ne=t(53124),E=t(11568),T=t(14747),f=t(83559);const D=(o,n,r,s,e)=>({background:o,border:`${(0,E.bf)(s.lineWidth)} ${s.lineType} ${n}`,[`${e}-icon`]:{color:r}}),le=o=>{const{componentCls:n,motionDurationSlow:r,marginXS:s,marginSM:e,fontSize:l,fontSizeLG:u,lineHeight:m,borderRadiusLG:v,motionEaseInOutCirc:p,withDescriptionIconSize:y,colorText:I,colorTextHeading:O,withDescriptionPadding:M,defaultPadding:i}=o;return{[n]:Object.assign(Object.assign({},(0,T.Wf)(o)),{position:"relative",display:"flex",alignItems:"center",padding:i,wordWrap:"break-word",borderRadius:v,[`&${n}-rtl`]:{direction:"rtl"},[`${n}-content`]:{flex:1,minWidth:0},[`${n}-icon`]:{marginInlineEnd:s,lineHeight:0},"&-description":{display:"none",fontSize:l,lineHeight:m},"&-message":{color:O},[`&${n}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${r} ${p}, opacity ${r} ${p},
        padding-top ${r} ${p}, padding-bottom ${r} ${p},
        margin-bottom ${r} ${p}`},[`&${n}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${n}-with-description`]:{alignItems:"flex-start",padding:M,[`${n}-icon`]:{marginInlineEnd:e,fontSize:y,lineHeight:0},[`${n}-message`]:{display:"block",marginBottom:s,color:O,fontSize:u},[`${n}-description`]:{display:"block",color:I}},[`${n}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},te=o=>{const{componentCls:n,colorSuccess:r,colorSuccessBorder:s,colorSuccessBg:e,colorWarning:l,colorWarningBorder:u,colorWarningBg:m,colorError:v,colorErrorBorder:p,colorErrorBg:y,colorInfo:I,colorInfoBorder:O,colorInfoBg:M}=o;return{[n]:{"&-success":D(e,s,r,o,n),"&-info":D(M,O,I,o,n),"&-warning":D(m,u,l,o,n),"&-error":Object.assign(Object.assign({},D(y,p,v,o,n)),{[`${n}-description > pre`]:{margin:0,padding:0}})}}},Z=o=>{const{componentCls:n,iconCls:r,motionDurationMid:s,marginXS:e,fontSizeIcon:l,colorIcon:u,colorIconHover:m}=o;return{[n]:{"&-action":{marginInlineStart:e},[`${n}-close-icon`]:{marginInlineStart:e,padding:0,overflow:"hidden",fontSize:l,lineHeight:(0,E.bf)(l),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${r}-close`]:{color:u,transition:`color ${s}`,"&:hover":{color:m}}},"&-close-text":{color:u,transition:`color ${s}`,"&:hover":{color:m}}}}},z=o=>({withDescriptionIconSize:o.fontSizeHeading3,defaultPadding:`${o.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${o.paddingMD}px ${o.paddingContentHorizontalLG}px`});var H=(0,f.I$)("Alert",o=>[le(o),te(o),Z(o)],z),k=function(o,n){var r={};for(var s in o)Object.prototype.hasOwnProperty.call(o,s)&&n.indexOf(s)<0&&(r[s]=o[s]);if(o!=null&&typeof Object.getOwnPropertySymbols=="function")for(var e=0,s=Object.getOwnPropertySymbols(o);e<s.length;e++)n.indexOf(s[e])<0&&Object.prototype.propertyIsEnumerable.call(o,s[e])&&(r[s[e]]=o[s[e]]);return r};const G={success:W.Z,info:N.Z,error:Q.Z,warning:J.Z},re=o=>{const{icon:n,prefixCls:r,type:s}=o,e=G[s]||null;return n?(0,oe.wm)(n,a.createElement("span",{className:`${r}-icon`},n),()=>({className:B()(`${r}-icon`,n.props.className)})):a.createElement(e,{className:`${r}-icon`})},se=o=>{const{isClosable:n,prefixCls:r,closeIcon:s,handleClose:e,ariaProps:l}=o,u=s===!0||s===void 0?a.createElement(K.Z,null):s;return n?a.createElement("button",Object.assign({type:"button",onClick:e,className:`${r}-close-icon`,tabIndex:0},l),u):null};var g=a.forwardRef((o,n)=>{const{description:r,prefixCls:s,message:e,banner:l,className:u,rootClassName:m,style:v,onMouseEnter:p,onMouseLeave:y,onClick:I,afterClose:O,showIcon:M,closable:i,closeText:x,closeIcon:j,action:ie,id:_e}=o,ve=k(o,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[ce,he]=a.useState(!1),de=a.useRef(null);a.useImperativeHandle(n,()=>({nativeElement:de.current}));const{getPrefixCls:be,direction:Ce,closable:P,closeIcon:ue,className:Ee,style:ye}=(0,ne.dj)("alert"),c=be("alert",s),[Ie,je,Pe]=H(c),De=h=>{var $;he(!0),($=o.onClose)===null||$===void 0||$.call(o,h)},me=a.useMemo(()=>o.type!==void 0?o.type:l?"warning":"info",[o.type,l]),Se=a.useMemo(()=>typeof i=="object"&&i.closeIcon||x?!0:typeof i=="boolean"?i:j!==!1&&j!==null&&j!==void 0?!0:!!P,[x,j,i,P]),pe=l&&M===void 0?!0:M,Oe=B()(c,`${c}-${me}`,{[`${c}-with-description`]:!!r,[`${c}-no-icon`]:!pe,[`${c}-banner`]:!!l,[`${c}-rtl`]:Ce==="rtl"},Ee,u,m,Pe,je),Me=(0,ee.Z)(ve,{aria:!0,data:!0}),$e=a.useMemo(()=>typeof i=="object"&&i.closeIcon?i.closeIcon:x||(j!==void 0?j:typeof P=="object"&&P.closeIcon?P.closeIcon:ue),[j,i,x,ue]),Be=a.useMemo(()=>{const h=i!=null?i:P;if(typeof h=="object"){const{closeIcon:$}=h;return k(h,["closeIcon"])}return{}},[i,P]);return Ie(a.createElement(q.ZP,{visible:!ce,motionName:`${c}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:h=>({maxHeight:h.offsetHeight}),onLeaveEnd:O},({className:h,style:$},fe)=>a.createElement("div",Object.assign({id:_e,ref:(0,F.sQ)(de,fe),"data-show":!ce,className:B()(Oe,h),style:Object.assign(Object.assign(Object.assign({},ye),v),$),onMouseEnter:p,onMouseLeave:y,onClick:I,role:"alert"},Me),pe?a.createElement(re,{description:r,icon:o.icon,prefixCls:c,type:me}):null,a.createElement("div",{className:`${c}-content`},e?a.createElement("div",{className:`${c}-message`},e):null,r?a.createElement("div",{className:`${c}-description`},r):null),ie?a.createElement("div",{className:`${c}-action`},ie):null,a.createElement(se,{isClosable:Se,prefixCls:c,closeIcon:$e,handleClose:De,ariaProps:Be}))))}),b=t(15671),w=t(43144),A=t(61120),V=t(78814),S=t(82963);function R(o,n,r){return n=(0,A.Z)(n),(0,S.Z)(o,(0,V.Z)()?Reflect.construct(n,r||[],(0,A.Z)(o).constructor):n.apply(o,r))}var L=t(60136),_=function(o){function n(){var r;return(0,b.Z)(this,n),r=R(this,n,arguments),r.state={error:void 0,info:{componentStack:""}},r}return(0,L.Z)(n,o),(0,w.Z)(n,[{key:"componentDidCatch",value:function(s,e){this.setState({error:s,info:e})}},{key:"render",value:function(){const{message:s,description:e,id:l,children:u}=this.props,{error:m,info:v}=this.state,p=(v==null?void 0:v.componentStack)||null,y=typeof s=="undefined"?(m||"").toString():s,I=typeof e=="undefined"?p:e;return m?a.createElement(g,{id:l,type:"error",message:y,description:a.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},I)}):u}}])}(a.Component);const d=g;d.ErrorBoundary=_;var C=d}}]);
