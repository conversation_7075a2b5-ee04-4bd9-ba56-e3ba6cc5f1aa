const https = require('https');

// 测试正确的Keycloak配置端点
async function testCorrectKeycloak() {
  console.log('🔍 测试正确的Keycloak配置端点...');
  
  const keycloakUrl = 'https://111.13.109.67:9088/admin';
  const realm = 'dev_xh_key';
  
  // 正确的well-known配置端点
  const wellKnownUrl = `${keycloakUrl}/realms/${realm}/.well-known/openid_configuration`;
  
  console.log(`📡 测试URL: ${wellKnownUrl}`);
  
  return new Promise((resolve, reject) => {
    process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;
    
    const request = https.get(wellKnownUrl, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Node.js Test Client',
        'Accept': 'application/json'
      }
    }, (response) => {
      console.log(`📊 响应状态: ${response.statusCode}`);
      console.log(`📋 响应头:`, response.headers);
      
      let data = '';
      
      response.on('data', (chunk) => {
        data += chunk;
      });
      
      response.on('end', () => {
        try {
          if (response.statusCode === 200) {
            const config = JSON.parse(data);
            console.log('✅ Keycloak配置获取成功!');
            console.log('\n🔗 重要端点:');
            console.log(`- 发行者: ${config.issuer}`);
            console.log(`- 授权端点: ${config.authorization_endpoint}`);
            console.log(`- Token端点: ${config.token_endpoint}`);
            console.log(`- 用户信息端点: ${config.userinfo_endpoint}`);
            console.log(`- 登出端点: ${config.end_session_endpoint}`);
            console.log(`- JWKS端点: ${config.jwks_uri}`);
            
            console.log('\n🔧 支持的功能:');
            console.log(`- 支持的响应类型: ${config.response_types_supported?.join(', ')}`);
            console.log(`- 支持的授权方式: ${config.grant_types_supported?.join(', ')}`);
            console.log(`- 支持PKCE: ${config.code_challenge_methods_supported?.includes('S256') ? '是' : '否'}`);
            
            resolve(config);
          } else {
            console.log('❌ Keycloak响应错误');
            console.log('📄 响应内容:', data);
            reject(new Error(`HTTP ${response.statusCode}: ${data}`));
          }
        } catch (error) {
          console.log('❌ 解析响应失败:', error.message);
          console.log('📄 原始响应:', data);
          reject(error);
        }
      });
    });
    
    request.on('timeout', () => {
      console.log('⏰ 请求超时');
      request.destroy();
      reject(new Error('请求超时'));
    });
    
    request.on('error', (error) => {
      console.log('❌ 连接错误:', error.message);
      reject(error);
    });
  });
}

// 运行测试
testCorrectKeycloak().then((config) => {
  console.log('\n✅ 测试成功完成!');
  console.log('\n📋 修复建议:');
  console.log('需要更新前端Keycloak配置，移除/auth前缀:');
  console.log(`- 旧配置: https://111.13.109.67:9088/auth/realms/dev_xh_key`);
  console.log(`- 新配置: https://111.13.109.67:9088/realms/dev_xh_key`);
}).catch((error) => {
  console.log('\n❌ 测试失败:', error.message);
});
