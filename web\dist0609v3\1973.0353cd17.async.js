"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1973],{40056:function(je,re,i){i.d(re,{Z:function(){return t}});var s=i(67294),se=i(19735),E=i(17012),q=i(62208),k=i(29950),V=i(1558),ae=i(93967),_=i.n(ae),ee=i(29372),ie=i(64217),G=i(42550),ce=i(96159),de=i(53124),F=i(11568),ue=i(14747),U=i(83559);const Z=(e,o,n,r,a)=>({background:e,border:`${(0,F.bf)(r.lineWidth)} ${r.lineType} ${o}`,[`${a}-icon`]:{color:n}}),me=e=>{const{componentCls:o,motionDurationSlow:n,marginXS:r,marginSM:a,fontSize:c,fontSizeLG:b,lineHeight:m,borderRadiusLG:h,motionEaseInOutCirc:g,withDescriptionIconSize:B,colorText:O,colorTextHeading:y,withDescriptionPadding:x,defaultPadding:d}=e;return{[o]:Object.assign(Object.assign({},(0,ue.Wf)(e)),{position:"relative",display:"flex",alignItems:"center",padding:d,wordWrap:"break-word",borderRadius:h,[`&${o}-rtl`]:{direction:"rtl"},[`${o}-content`]:{flex:1,minWidth:0},[`${o}-icon`]:{marginInlineEnd:r,lineHeight:0},"&-description":{display:"none",fontSize:c,lineHeight:m},"&-message":{color:y},[`&${o}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${n} ${g}, opacity ${n} ${g},
        padding-top ${n} ${g}, padding-bottom ${n} ${g},
        margin-bottom ${n} ${g}`},[`&${o}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${o}-with-description`]:{alignItems:"flex-start",padding:x,[`${o}-icon`]:{marginInlineEnd:a,fontSize:B,lineHeight:0},[`${o}-message`]:{display:"block",marginBottom:r,color:y,fontSize:b},[`${o}-description`]:{display:"block",color:O}},[`${o}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},ge=e=>{const{componentCls:o,colorSuccess:n,colorSuccessBorder:r,colorSuccessBg:a,colorWarning:c,colorWarningBorder:b,colorWarningBg:m,colorError:h,colorErrorBorder:g,colorErrorBg:B,colorInfo:O,colorInfoBorder:y,colorInfoBg:x}=e;return{[o]:{"&-success":Z(a,r,n,e,o),"&-info":Z(x,y,O,e,o),"&-warning":Z(m,b,c,e,o),"&-error":Object.assign(Object.assign({},Z(B,g,h,e,o)),{[`${o}-description > pre`]:{margin:0,padding:0}})}}},$e=e=>{const{componentCls:o,iconCls:n,motionDurationMid:r,marginXS:a,fontSizeIcon:c,colorIcon:b,colorIconHover:m}=e;return{[o]:{"&-action":{marginInlineStart:a},[`${o}-close-icon`]:{marginInlineStart:a,padding:0,overflow:"hidden",fontSize:c,lineHeight:(0,F.bf)(c),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${n}-close`]:{color:b,transition:`color ${r}`,"&:hover":{color:m}}},"&-close-text":{color:b,transition:`color ${r}`,"&:hover":{color:m}}}}},fe=e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`});var te=(0,U.I$)("Alert",e=>[me(e),ge(e),$e(e)],fe),pe=function(e,o){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&o.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)o.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};const Q={success:se.Z,info:V.Z,error:E.Z,warning:k.Z},J=e=>{const{icon:o,prefixCls:n,type:r}=e,a=Q[r]||null;return o?(0,ce.wm)(o,s.createElement("span",{className:`${n}-icon`},o),()=>({className:_()(`${n}-icon`,o.props.className)})):s.createElement(a,{className:`${n}-icon`})},K=e=>{const{isClosable:o,prefixCls:n,closeIcon:r,handleClose:a,ariaProps:c}=e,b=r===!0||r===void 0?s.createElement(q.Z,null):r;return o?s.createElement("button",Object.assign({type:"button",onClick:a,className:`${n}-close-icon`,tabIndex:0},c),b):null};var N=s.forwardRef((e,o)=>{const{description:n,prefixCls:r,message:a,banner:c,className:b,rootClassName:m,style:h,onMouseEnter:g,onMouseLeave:B,onClick:O,afterClose:y,showIcon:x,closable:d,closeText:f,closeIcon:v,action:D,id:W}=e,j=pe(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[H,ve]=s.useState(!1),w=s.useRef(null);s.useImperativeHandle(o,()=>({nativeElement:w.current}));const{getPrefixCls:R,direction:T,closable:z,closeIcon:L,className:Ce,style:M}=(0,de.dj)("alert"),S=R("alert",r),[Y,Oe,xe]=te(S),Se=A=>{var le;ve(!0),(le=e.onClose)===null||le===void 0||le.call(e,A)},X=s.useMemo(()=>e.type!==void 0?e.type:c?"warning":"info",[e.type,c]),he=s.useMemo(()=>typeof d=="object"&&d.closeIcon||f?!0:typeof d=="boolean"?d:v!==!1&&v!==null&&v!==void 0?!0:!!z,[f,v,d,z]),Ie=c&&x===void 0?!0:x,Ne=_()(S,`${S}-${X}`,{[`${S}-with-description`]:!!n,[`${S}-no-icon`]:!Ie,[`${S}-banner`]:!!c,[`${S}-rtl`]:T==="rtl"},Ce,b,m,xe,Oe),Pe=(0,ie.Z)(j,{aria:!0,data:!0}),Be=s.useMemo(()=>typeof d=="object"&&d.closeIcon?d.closeIcon:f||(v!==void 0?v:typeof z=="object"&&z.closeIcon?z.closeIcon:L),[v,d,f,L]),Te=s.useMemo(()=>{const A=d!=null?d:z;if(typeof A=="object"){const{closeIcon:le}=A;return pe(A,["closeIcon"])}return{}},[d,z]);return Y(s.createElement(ee.ZP,{visible:!H,motionName:`${S}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:A=>({maxHeight:A.offsetHeight}),onLeaveEnd:y},({className:A,style:le},Ee)=>s.createElement("div",Object.assign({id:W,ref:(0,G.sQ)(w,Ee),"data-show":!H,className:_()(Ne,A),style:Object.assign(Object.assign(Object.assign({},M),h),le),onMouseEnter:g,onMouseLeave:B,onClick:O,role:"alert"},Pe),Ie?s.createElement(J,{description:n,icon:e.icon,prefixCls:S,type:X}):null,s.createElement("div",{className:`${S}-content`},a?s.createElement("div",{className:`${S}-message`},a):null,n?s.createElement("div",{className:`${S}-description`},n):null),D?s.createElement("div",{className:`${S}-action`},D):null,s.createElement(K,{isClosable:he,prefixCls:S,closeIcon:Be,handleClose:Se,ariaProps:Te}))))}),be=i(15671),ne=i(43144),oe=i(61120),l=i(78814),C=i(82963);function $(e,o,n){return o=(0,oe.Z)(o),(0,C.Z)(e,(0,l.Z)()?Reflect.construct(o,n||[],(0,oe.Z)(e).constructor):o.apply(e,n))}var u=i(60136),P=function(e){function o(){var n;return(0,be.Z)(this,o),n=$(this,o,arguments),n.state={error:void 0,info:{componentStack:""}},n}return(0,u.Z)(o,e),(0,ne.Z)(o,[{key:"componentDidCatch",value:function(r,a){this.setState({error:r,info:a})}},{key:"render",value:function(){const{message:r,description:a,id:c,children:b}=this.props,{error:m,info:h}=this.state,g=(h==null?void 0:h.componentStack)||null,B=typeof r=="undefined"?(m||"").toString():r,O=typeof a=="undefined"?g:a;return m?s.createElement(N,{id:c,type:"error",message:B,description:s.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},O)}):b}}])}(s.Component);const I=N;I.ErrorBoundary=P;var t=I},26412:function(je,re,i){i.d(re,{Z:function(){return I}});var s=i(67294),se=i(93967),E=i.n(se),q=i(74443),k=i(53124),V=i(98675),ae=i(25378),ee={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1},G=s.createContext({}),ce=i(50344),de=function(t,e){var o={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(o[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,n=Object.getOwnPropertySymbols(t);r<n.length;r++)e.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(t,n[r])&&(o[n[r]]=t[n[r]]);return o};const F=t=>(0,ce.Z)(t).map(e=>Object.assign(Object.assign({},e==null?void 0:e.props),{key:e.key}));function ue(t,e,o){const n=s.useMemo(()=>e||F(o),[e,o]);return s.useMemo(()=>n.map(a=>{var{span:c}=a,b=de(a,["span"]);return c==="filled"?Object.assign(Object.assign({},b),{filled:!0}):Object.assign(Object.assign({},b),{span:typeof c=="number"?c:(0,q.m9)(t,c)})}),[n,t])}var U=function(t,e){var o={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(o[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,n=Object.getOwnPropertySymbols(t);r<n.length;r++)e.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(t,n[r])&&(o[n[r]]=t[n[r]]);return o};function Z(t,e){let o=[],n=[],r=!1,a=0;return t.filter(c=>c).forEach(c=>{const{filled:b}=c,m=U(c,["filled"]);if(b){n.push(m),o.push(n),n=[],a=0;return}const h=e-a;a+=c.span||1,a>=e?(a>e?(r=!0,n.push(Object.assign(Object.assign({},m),{span:h}))):n.push(m),o.push(n),n=[],a=0):n.push(m)}),n.length>0&&o.push(n),o=o.map(c=>{const b=c.reduce((m,h)=>m+(h.span||1),0);if(b<e){const m=c[c.length-1];return m.span=e-(b-(m.span||1)),c}return c}),[o,r]}var ge=(t,e)=>{const[o,n]=(0,s.useMemo)(()=>Z(e,t),[e,t]);return o},fe=({children:t})=>t;function te(t){return t!=null}var Q=t=>{const{itemPrefixCls:e,component:o,span:n,className:r,style:a,labelStyle:c,contentStyle:b,bordered:m,label:h,content:g,colon:B,type:O,styles:y}=t,x=o,d=s.useContext(G),{classNames:f}=d;return m?s.createElement(x,{className:E()({[`${e}-item-label`]:O==="label",[`${e}-item-content`]:O==="content",[`${f==null?void 0:f.label}`]:O==="label",[`${f==null?void 0:f.content}`]:O==="content"},r),style:a,colSpan:n},te(h)&&s.createElement("span",{style:Object.assign(Object.assign({},c),y==null?void 0:y.label)},h),te(g)&&s.createElement("span",{style:Object.assign(Object.assign({},c),y==null?void 0:y.content)},g)):s.createElement(x,{className:E()(`${e}-item`,r),style:a,colSpan:n},s.createElement("div",{className:`${e}-item-container`},(h||h===0)&&s.createElement("span",{className:E()(`${e}-item-label`,f==null?void 0:f.label,{[`${e}-item-no-colon`]:!B}),style:Object.assign(Object.assign({},c),y==null?void 0:y.label)},h),(g||g===0)&&s.createElement("span",{className:E()(`${e}-item-content`,f==null?void 0:f.content),style:Object.assign(Object.assign({},b),y==null?void 0:y.content)},g)))};function J(t,{colon:e,prefixCls:o,bordered:n},{component:r,type:a,showLabel:c,showContent:b,labelStyle:m,contentStyle:h,styles:g}){return t.map(({label:B,children:O,prefixCls:y=o,className:x,style:d,labelStyle:f,contentStyle:v,span:D=1,key:W,styles:j},H)=>typeof r=="string"?s.createElement(Q,{key:`${a}-${W||H}`,className:x,style:d,styles:{label:Object.assign(Object.assign(Object.assign(Object.assign({},m),g==null?void 0:g.label),f),j==null?void 0:j.label),content:Object.assign(Object.assign(Object.assign(Object.assign({},h),g==null?void 0:g.content),v),j==null?void 0:j.content)},span:D,colon:e,component:r,itemPrefixCls:y,bordered:n,label:c?B:null,content:b?O:null,type:a}):[s.createElement(Q,{key:`label-${W||H}`,className:x,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},m),g==null?void 0:g.label),d),f),j==null?void 0:j.label),span:1,colon:e,component:r[0],itemPrefixCls:y,bordered:n,label:B,type:"label"}),s.createElement(Q,{key:`content-${W||H}`,className:x,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},h),g==null?void 0:g.content),d),v),j==null?void 0:j.content),span:D*2-1,component:r[1],itemPrefixCls:y,bordered:n,content:O,type:"content"})])}var ye=t=>{const e=s.useContext(G),{prefixCls:o,vertical:n,row:r,index:a,bordered:c}=t;return n?s.createElement(s.Fragment,null,s.createElement("tr",{key:`label-${a}`,className:`${o}-row`},J(r,t,Object.assign({component:"th",type:"label",showLabel:!0},e))),s.createElement("tr",{key:`content-${a}`,className:`${o}-row`},J(r,t,Object.assign({component:"td",type:"content",showContent:!0},e)))):s.createElement("tr",{key:a,className:`${o}-row`},J(r,t,Object.assign({component:c?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},e)))},N=i(11568),be=i(14747),ne=i(83559),oe=i(83262);const l=t=>{const{componentCls:e,labelBg:o}=t;return{[`&${e}-bordered`]:{[`> ${e}-view`]:{border:`${(0,N.bf)(t.lineWidth)} ${t.lineType} ${t.colorSplit}`,"> table":{tableLayout:"auto"},[`${e}-row`]:{borderBottom:`${(0,N.bf)(t.lineWidth)} ${t.lineType} ${t.colorSplit}`,"&:first-child":{"> th:first-child, > td:first-child":{borderStartStartRadius:t.borderRadiusLG}},"&:last-child":{borderBottom:"none","> th:first-child, > td:first-child":{borderEndStartRadius:t.borderRadiusLG}},[`> ${e}-item-label, > ${e}-item-content`]:{padding:`${(0,N.bf)(t.padding)} ${(0,N.bf)(t.paddingLG)}`,borderInlineEnd:`${(0,N.bf)(t.lineWidth)} ${t.lineType} ${t.colorSplit}`,"&:last-child":{borderInlineEnd:"none"}},[`> ${e}-item-label`]:{color:t.colorTextSecondary,backgroundColor:o,"&::after":{display:"none"}}}},[`&${e}-middle`]:{[`${e}-row`]:{[`> ${e}-item-label, > ${e}-item-content`]:{padding:`${(0,N.bf)(t.paddingSM)} ${(0,N.bf)(t.paddingLG)}`}}},[`&${e}-small`]:{[`${e}-row`]:{[`> ${e}-item-label, > ${e}-item-content`]:{padding:`${(0,N.bf)(t.paddingXS)} ${(0,N.bf)(t.padding)}`}}}}}},C=t=>{const{componentCls:e,extraColor:o,itemPaddingBottom:n,itemPaddingEnd:r,colonMarginRight:a,colonMarginLeft:c,titleMarginBottom:b}=t;return{[e]:Object.assign(Object.assign(Object.assign({},(0,be.Wf)(t)),l(t)),{"&-rtl":{direction:"rtl"},[`${e}-header`]:{display:"flex",alignItems:"center",marginBottom:b},[`${e}-title`]:Object.assign(Object.assign({},be.vS),{flex:"auto",color:t.titleColor,fontWeight:t.fontWeightStrong,fontSize:t.fontSizeLG,lineHeight:t.lineHeightLG}),[`${e}-extra`]:{marginInlineStart:"auto",color:o,fontSize:t.fontSize},[`${e}-view`]:{width:"100%",borderRadius:t.borderRadiusLG,table:{width:"100%",tableLayout:"fixed",borderCollapse:"collapse"}},[`${e}-row`]:{"> th, > td":{paddingBottom:n,paddingInlineEnd:r},"> th:last-child, > td:last-child":{paddingInlineEnd:0},"&:last-child":{borderBottom:"none","> th, > td":{paddingBottom:0}}},[`${e}-item-label`]:{color:t.labelColor,fontWeight:"normal",fontSize:t.fontSize,lineHeight:t.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:`${(0,N.bf)(c)} ${(0,N.bf)(a)}`},[`&${e}-item-no-colon::after`]:{content:'""'}},[`${e}-item-no-label`]:{"&::after":{margin:0,content:'""'}},[`${e}-item-content`]:{display:"table-cell",flex:1,color:t.contentColor,fontSize:t.fontSize,lineHeight:t.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},[`${e}-item`]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",[`${e}-item-label`]:{display:"inline-flex",alignItems:"baseline"},[`${e}-item-content`]:{display:"inline-flex",alignItems:"baseline",minWidth:"1em"}}},"&-middle":{[`${e}-row`]:{"> th, > td":{paddingBottom:t.paddingSM}}},"&-small":{[`${e}-row`]:{"> th, > td":{paddingBottom:t.paddingXS}}}})}},$=t=>({labelBg:t.colorFillAlter,labelColor:t.colorTextTertiary,titleColor:t.colorText,titleMarginBottom:t.fontSizeSM*t.lineHeightSM,itemPaddingBottom:t.padding,itemPaddingEnd:t.padding,colonMarginRight:t.marginXS,colonMarginLeft:t.marginXXS/2,contentColor:t.colorText,extraColor:t.colorText});var u=(0,ne.I$)("Descriptions",t=>{const e=(0,oe.IX)(t,{});return C(e)},$),p=function(t,e){var o={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(o[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,n=Object.getOwnPropertySymbols(t);r<n.length;r++)e.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(t,n[r])&&(o[n[r]]=t[n[r]]);return o};const P=t=>{const{prefixCls:e,title:o,extra:n,column:r,colon:a=!0,bordered:c,layout:b,children:m,className:h,rootClassName:g,style:B,size:O,labelStyle:y,contentStyle:x,styles:d,items:f,classNames:v}=t,D=p(t,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","styles","items","classNames"]),{getPrefixCls:W,direction:j,className:H,style:ve,classNames:w,styles:R}=(0,k.dj)("descriptions"),T=W("descriptions",e),z=(0,ae.Z)(),L=s.useMemo(()=>{var X;return typeof r=="number"?r:(X=(0,q.m9)(z,Object.assign(Object.assign({},ee),r)))!==null&&X!==void 0?X:3},[z,r]),Ce=ue(z,f,m),M=(0,V.Z)(O),S=ge(L,Ce),[Y,Oe,xe]=u(T),Se=s.useMemo(()=>({labelStyle:y,contentStyle:x,styles:{content:Object.assign(Object.assign({},R.content),d==null?void 0:d.content),label:Object.assign(Object.assign({},R.label),d==null?void 0:d.label)},classNames:{label:E()(w.label,v==null?void 0:v.label),content:E()(w.content,v==null?void 0:v.content)}}),[y,x,d,v,w,R]);return Y(s.createElement(G.Provider,{value:Se},s.createElement("div",Object.assign({className:E()(T,H,w.root,v==null?void 0:v.root,{[`${T}-${M}`]:M&&M!=="default",[`${T}-bordered`]:!!c,[`${T}-rtl`]:j==="rtl"},h,g,Oe,xe),style:Object.assign(Object.assign(Object.assign(Object.assign({},ve),R.root),d==null?void 0:d.root),B)},D),(o||n)&&s.createElement("div",{className:E()(`${T}-header`,w.header,v==null?void 0:v.header),style:Object.assign(Object.assign({},R.header),d==null?void 0:d.header)},o&&s.createElement("div",{className:E()(`${T}-title`,w.title,v==null?void 0:v.title),style:Object.assign(Object.assign({},R.title),d==null?void 0:d.title)},o),n&&s.createElement("div",{className:E()(`${T}-extra`,w.extra,v==null?void 0:v.extra),style:Object.assign(Object.assign({},R.extra),d==null?void 0:d.extra)},n)),s.createElement("div",{className:`${T}-view`},s.createElement("table",null,s.createElement("tbody",null,S.map((X,he)=>s.createElement(ye,{key:he,index:he,colon:a,prefixCls:T,vertical:b==="vertical",bordered:c,row:X}))))))))};P.Item=fe;var I=P},66309:function(je,re,i){i.d(re,{Z:function(){return oe}});var s=i(67294),se=i(93967),E=i.n(se),q=i(98423),k=i(98787),V=i(69760),ae=i(96159),_=i(45353),ee=i(53124),ie=i(11568),G=i(15063),ce=i(14747),de=i(83262),F=i(83559);const ue=l=>{const{paddingXXS:C,lineWidth:$,tagPaddingHorizontal:u,componentCls:p,calc:P}=l,I=P(u).sub($).equal(),t=P(C).sub($).equal();return{[p]:Object.assign(Object.assign({},(0,ce.Wf)(l)),{display:"inline-block",height:"auto",marginInlineEnd:l.marginXS,paddingInline:I,fontSize:l.tagFontSize,lineHeight:l.tagLineHeight,whiteSpace:"nowrap",background:l.defaultBg,border:`${(0,ie.bf)(l.lineWidth)} ${l.lineType} ${l.colorBorder}`,borderRadius:l.borderRadiusSM,opacity:1,transition:`all ${l.motionDurationMid}`,textAlign:"start",position:"relative",[`&${p}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:l.defaultColor},[`${p}-close-icon`]:{marginInlineStart:t,fontSize:l.tagIconSize,color:l.colorIcon,cursor:"pointer",transition:`all ${l.motionDurationMid}`,"&:hover":{color:l.colorTextHeading}},[`&${p}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${l.iconCls}-close, ${l.iconCls}-close:hover`]:{color:l.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${p}-checkable-checked):hover`]:{color:l.colorPrimary,backgroundColor:l.colorFillSecondary},"&:active, &-checked":{color:l.colorTextLightSolid},"&-checked":{backgroundColor:l.colorPrimary,"&:hover":{backgroundColor:l.colorPrimaryHover}},"&:active":{backgroundColor:l.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${l.iconCls} + span, > span + ${l.iconCls}`]:{marginInlineStart:I}}),[`${p}-borderless`]:{borderColor:"transparent",background:l.tagBorderlessBg}}},U=l=>{const{lineWidth:C,fontSizeIcon:$,calc:u}=l,p=l.fontSizeSM;return(0,de.IX)(l,{tagFontSize:p,tagLineHeight:(0,ie.bf)(u(l.lineHeightSM).mul(p).equal()),tagIconSize:u($).sub(u(C).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:l.defaultBg})},Z=l=>({defaultBg:new G.t(l.colorFillQuaternary).onBackground(l.colorBgContainer).toHexString(),defaultColor:l.colorText});var me=(0,F.I$)("Tag",l=>{const C=U(l);return ue(C)},Z),ge=function(l,C){var $={};for(var u in l)Object.prototype.hasOwnProperty.call(l,u)&&C.indexOf(u)<0&&($[u]=l[u]);if(l!=null&&typeof Object.getOwnPropertySymbols=="function")for(var p=0,u=Object.getOwnPropertySymbols(l);p<u.length;p++)C.indexOf(u[p])<0&&Object.prototype.propertyIsEnumerable.call(l,u[p])&&($[u[p]]=l[u[p]]);return $},fe=s.forwardRef((l,C)=>{const{prefixCls:$,style:u,className:p,checked:P,onChange:I,onClick:t}=l,e=ge(l,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:o,tag:n}=s.useContext(ee.E_),r=g=>{I==null||I(!P),t==null||t(g)},a=o("tag",$),[c,b,m]=me(a),h=E()(a,`${a}-checkable`,{[`${a}-checkable-checked`]:P},n==null?void 0:n.className,p,b,m);return c(s.createElement("span",Object.assign({},e,{ref:C,style:Object.assign(Object.assign({},u),n==null?void 0:n.style),className:h,onClick:r})))}),te=i(98719);const pe=l=>(0,te.Z)(l,(C,{textColor:$,lightBorderColor:u,lightColor:p,darkColor:P})=>({[`${l.componentCls}${l.componentCls}-${C}`]:{color:$,background:p,borderColor:u,"&-inverse":{color:l.colorTextLightSolid,background:P,borderColor:P},[`&${l.componentCls}-borderless`]:{borderColor:"transparent"}}}));var Q=(0,F.bk)(["Tag","preset"],l=>{const C=U(l);return pe(C)},Z);function J(l){return typeof l!="string"?l:l.charAt(0).toUpperCase()+l.slice(1)}const K=(l,C,$)=>{const u=J($);return{[`${l.componentCls}${l.componentCls}-${C}`]:{color:l[`color${$}`],background:l[`color${u}Bg`],borderColor:l[`color${u}Border`],[`&${l.componentCls}-borderless`]:{borderColor:"transparent"}}}};var ye=(0,F.bk)(["Tag","status"],l=>{const C=U(l);return[K(C,"success","Success"),K(C,"processing","Info"),K(C,"error","Error"),K(C,"warning","Warning")]},Z),N=function(l,C){var $={};for(var u in l)Object.prototype.hasOwnProperty.call(l,u)&&C.indexOf(u)<0&&($[u]=l[u]);if(l!=null&&typeof Object.getOwnPropertySymbols=="function")for(var p=0,u=Object.getOwnPropertySymbols(l);p<u.length;p++)C.indexOf(u[p])<0&&Object.prototype.propertyIsEnumerable.call(l,u[p])&&($[u[p]]=l[u[p]]);return $};const ne=s.forwardRef((l,C)=>{const{prefixCls:$,className:u,rootClassName:p,style:P,children:I,icon:t,color:e,onClose:o,bordered:n=!0,visible:r}=l,a=N(l,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:c,direction:b,tag:m}=s.useContext(ee.E_),[h,g]=s.useState(!0),B=(0,q.Z)(a,["closeIcon","closable"]);s.useEffect(()=>{r!==void 0&&g(r)},[r]);const O=(0,k.o2)(e),y=(0,k.yT)(e),x=O||y,d=Object.assign(Object.assign({backgroundColor:e&&!x?e:void 0},m==null?void 0:m.style),P),f=c("tag",$),[v,D,W]=me(f),j=E()(f,m==null?void 0:m.className,{[`${f}-${e}`]:x,[`${f}-has-color`]:e&&!x,[`${f}-hidden`]:!h,[`${f}-rtl`]:b==="rtl",[`${f}-borderless`]:!n},u,p,D,W),H=L=>{L.stopPropagation(),o==null||o(L),!L.defaultPrevented&&g(!1)},[,ve]=(0,V.Z)((0,V.w)(l),(0,V.w)(m),{closable:!1,closeIconRender:L=>{const Ce=s.createElement("span",{className:`${f}-close-icon`,onClick:H},L);return(0,ae.wm)(L,Ce,M=>({onClick:S=>{var Y;(Y=M==null?void 0:M.onClick)===null||Y===void 0||Y.call(M,S),H(S)},className:E()(M==null?void 0:M.className,`${f}-close-icon`)}))}}),w=typeof a.onClick=="function"||I&&I.type==="a",R=t||null,T=R?s.createElement(s.Fragment,null,R,I&&s.createElement("span",null,I)):I,z=s.createElement("span",Object.assign({},B,{ref:C,className:j,style:d}),T,ve,O&&s.createElement(Q,{key:"preset",prefixCls:f}),y&&s.createElement(ye,{key:"status",prefixCls:f}));return v(w?s.createElement(_.Z,{component:"Tag"},z):z)});ne.CheckableTag=fe;var oe=ne}}]);
