(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4017],{47356:function(o,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"};a.default=t},44149:function(o,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M869 487.8L491.2 159.9c-2.9-2.5-6.6-3.9-10.5-3.9h-88.5c-7.4 0-10.8 9.2-5.2 14l350.2 304H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h585.1L386.9 854c-5.6 4.9-2.2 14 5.2 14h91.5c1.9 0 3.8-.7 5.2-2L869 536.2a32.07 32.07 0 000-48.4z"}}]},name:"arrow-right",theme:"outlined"};a.default=t},88372:function(o,a,t){"use strict";t.d(a,{f:function(){return C}});var n=t(4942),c=t(21532),r=t(93967),d=t.n(r),f=t(67294),m=t(76509),y=t(1413),g=t(64847),W=function(N){return(0,n.Z)({},N.componentCls,{width:"100%","&-wide":{maxWidth:1152,margin:"0 auto"}})};function I(K){return(0,g.Xj)("ProLayoutGridContent",function(N){var U=(0,y.Z)((0,y.Z)({},N),{},{componentCls:".".concat(K)});return[W(U)]})}var S=t(85893),C=function(N){var U=(0,f.useContext)(m.X),de=N.children,$=N.contentWidth,fe=N.className,w=N.style,Z=(0,f.useContext)(c.ZP.ConfigContext),l=Z.getPrefixCls,R=N.prefixCls||l("pro"),z=$||U.contentWidth,v="".concat(R,"-grid-content"),O=I(v),M=O.wrapSSR,j=O.hashId,A=z==="Fixed"&&U.layout==="top";return M((0,S.jsx)("div",{className:d()(v,j,fe,(0,n.Z)({},"".concat(v,"-wide"),A)),style:w,children:(0,S.jsx)("div",{className:"".concat(R,"-grid-content-children ").concat(j).trim(),children:de})}))}},84017:function(o,a,t){"use strict";t.d(a,{_z:function(){return J}});var n=t(4942),c=t(91),r=t(1413),d=t(71002),f=t(10915),m=t(11941),y=t(67159),g=t(21532),W=t(64218),I=t(93967),S=t.n(I),C=t(67294),K=t(76509),N=t(12044),U=t(98423),de=t(73935),$=t(64847),fe=function(e){return(0,n.Z)({},e.componentCls,{position:"fixed",insetInlineEnd:0,bottom:0,zIndex:99,display:"flex",alignItems:"center",width:"100%",paddingInline:24,paddingBlock:0,boxSizing:"border-box",lineHeight:"64px",backgroundColor:(0,$.uK)(e.colorBgElevated,.6),borderBlockStart:"1px solid ".concat(e.colorSplit),"-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)",color:e.colorText,transition:"all 0.2s ease 0s","&-left":{flex:1,color:e.colorText},"&-right":{color:e.colorText,"> *":{marginInlineEnd:8,"&:last-child":{marginBlock:0,marginInline:0}}}})};function w(T){return(0,$.Xj)("ProLayoutFooterToolbar",function(e){var s=(0,r.Z)((0,r.Z)({},e),{},{componentCls:".".concat(T)});return[fe(s)]})}function Z(T,e){var s=e.stylish;return(0,$.Xj)("ProLayoutFooterToolbarStylish",function(h){var p=(0,r.Z)((0,r.Z)({},h),{},{componentCls:".".concat(T)});return s?[(0,n.Z)({},"".concat(p.componentCls),s==null?void 0:s(p))]:[]})}var l=t(85893),R=["children","className","extra","portalDom","style","renderContent"],z=function(e){var s=e.children,h=e.className,p=e.extra,D=e.portalDom,L=D===void 0?!0:D,G=e.style,te=e.renderContent,F=(0,c.Z)(e,R),Q=(0,C.useContext)(g.ZP.ConfigContext),V=Q.getPrefixCls,ee=Q.getTargetContainer,_=e.prefixCls||V("pro"),X="".concat(_,"-footer-bar"),B=w(X),k=B.wrapSSR,E=B.hashId,H=(0,C.useContext)(K.X),le=(0,C.useMemo)(function(){var he=H.hasSiderMenu,Se=H.isMobile,Ae=H.siderWidth;if(he)return Ae?Se?"100%":"calc(100% - ".concat(Ae,"px)"):"100%"},[H.collapsed,H.hasSiderMenu,H.isMobile,H.siderWidth]),se=(0,C.useMemo)(function(){return typeof window=="undefined"||typeof document=="undefined"?null:(ee==null?void 0:ee())||document.body},[]),Ie=Z("".concat(X,".").concat(X,"-stylish"),{stylish:e.stylish}),q=(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"".concat(X,"-left ").concat(E).trim(),children:p}),(0,l.jsx)("div",{className:"".concat(X,"-right ").concat(E).trim(),children:s})]});(0,C.useEffect)(function(){return!H||!(H!=null&&H.setHasFooterToolbar)?function(){}:(H==null||H.setHasFooterToolbar(!0),function(){var he;H==null||(he=H.setHasFooterToolbar)===null||he===void 0||he.call(H,!1)})},[]);var ne=(0,l.jsx)("div",(0,r.Z)((0,r.Z)({className:S()(h,E,X,(0,n.Z)({},"".concat(X,"-stylish"),!!e.stylish)),style:(0,r.Z)({width:le},G)},(0,U.Z)(F,["prefixCls"])),{},{children:te?te((0,r.Z)((0,r.Z)((0,r.Z)({},e),H),{},{leftWidth:le}),q):q})),Le=!(0,N.j)()||!L||!se?ne:(0,de.createPortal)(ne,se,X);return Ie.wrapSSR(k((0,l.jsx)(C.Fragment,{children:Le},X)))},v=t(88372),O=t(97685),M=t(3770),j=t.n(M),A=t(77059),re=t.n(A),je=t(85673),Ne=t(85357),pe=t(78957),Ve=t(9220),Pe=t(80334),i=function(){return{overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}},P=function(e){var s;return(0,n.Z)({},e.componentCls,(0,r.Z)((0,r.Z)({},$.Wf===null||$.Wf===void 0?void 0:(0,$.Wf)(e)),{},(0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)({position:"relative",backgroundColor:e.colorWhite,paddingBlock:e.pageHeaderPaddingVertical+2,paddingInline:e.pageHeaderPadding,"&&-ghost":{backgroundColor:e.pageHeaderBgGhost},"&-no-children":{height:(s=e.layout)===null||s===void 0||(s=s.pageContainer)===null||s===void 0?void 0:s.paddingBlockPageContainerContent},"&&-has-breadcrumb":{paddingBlockStart:e.pageHeaderPaddingBreadCrumb},"&&-has-footer":{paddingBlockEnd:0},"& &-back":(0,n.Z)({marginInlineEnd:e.margin,fontSize:16,lineHeight:1,"&-button":(0,r.Z)((0,r.Z)({fontSize:16},$.Nd===null||$.Nd===void 0?void 0:(0,$.Nd)(e)),{},{color:e.pageHeaderColorBack,cursor:"pointer"})},"".concat(e.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:0})},"& ".concat("ant","-divider-vertical"),{height:14,marginBlock:0,marginInline:e.marginSM,verticalAlign:"middle"}),"& &-breadcrumb + &-heading",{marginBlockStart:e.marginXS}),"& &-heading",{display:"flex",justifyContent:"space-between","&-left":{display:"flex",alignItems:"center",marginBlock:e.marginXS/2,marginInlineEnd:0,marginInlineStart:0,overflow:"hidden"},"&-title":(0,r.Z)((0,r.Z)({marginInlineEnd:e.marginSM,marginBlockEnd:0,color:e.colorTextHeading,fontWeight:600,fontSize:e.pageHeaderFontSizeHeaderTitle,lineHeight:e.controlHeight+"px"},i()),{},(0,n.Z)({},"".concat(e.componentCls,"-rlt &"),{marginInlineEnd:0,marginInlineStart:e.marginSM})),"&-avatar":(0,n.Z)({marginInlineEnd:e.marginSM},"".concat(e.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:e.marginSM}),"&-tags":(0,n.Z)({},"".concat(e.componentCls,"-rlt &"),{float:"right"}),"&-sub-title":(0,r.Z)((0,r.Z)({marginInlineEnd:e.marginSM,color:e.colorTextSecondary,fontSize:e.pageHeaderFontSizeHeaderSubTitle,lineHeight:e.lineHeight},i()),{},(0,n.Z)({},"".concat(e.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:12})),"&-extra":(0,n.Z)((0,n.Z)({marginBlock:e.marginXS/2,marginInlineEnd:0,marginInlineStart:0,whiteSpace:"nowrap","> *":(0,n.Z)({"white-space":"unset"},"".concat(e.componentCls,"-rlt &"),{marginInlineEnd:e.marginSM,marginInlineStart:0})},"".concat(e.componentCls,"-rlt &"),{float:"left"}),"*:first-child",(0,n.Z)({},"".concat(e.componentCls,"-rlt &"),{marginInlineEnd:0}))}),"&-content",{paddingBlockStart:e.pageHeaderPaddingContentPadding}),"&-footer",{marginBlockStart:e.margin}),"&-compact &-heading",{flexWrap:"wrap"}),"&-wide",{maxWidth:1152,margin:"0 auto"}),"&-rtl",{direction:"rtl"})))};function b(T){return(0,$.Xj)("ProLayoutPageHeader",function(e){var s=(0,r.Z)((0,r.Z)({},e),{},{componentCls:".".concat(T),pageHeaderBgGhost:"transparent",pageHeaderPadding:16,pageHeaderPaddingVertical:4,pageHeaderPaddingBreadCrumb:e.paddingSM,pageHeaderColorBack:e.colorTextHeading,pageHeaderFontSizeHeaderTitle:e.fontSizeHeading4,pageHeaderFontSizeHeaderSubTitle:14,pageHeaderPaddingContentPadding:e.paddingSM});return[P(s)]})}var u=function(e,s,h,p){return!h||!p?null:(0,l.jsx)("div",{className:"".concat(e,"-back ").concat(s).trim(),children:(0,l.jsx)("div",{role:"button",onClick:function(L){p==null||p(L)},className:"".concat(e,"-back-button ").concat(s).trim(),"aria-label":"back",children:h})})},x=function(e,s){var h;return(h=e.items)!==null&&h!==void 0&&h.length?(0,l.jsx)(je.Z,(0,r.Z)((0,r.Z)({},e),{},{className:S()("".concat(s,"-breadcrumb"),e.className)})):null},oe=function(e){var s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"ltr";return e.backIcon!==void 0?e.backIcon:s==="rtl"?(0,l.jsx)(re(),{}):(0,l.jsx)(j(),{})},ie=function(e,s){var h=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"ltr",p=arguments.length>3?arguments[3]:void 0,D=s.title,L=s.avatar,G=s.subTitle,te=s.tags,F=s.extra,Q=s.onBack,V="".concat(e,"-heading"),ee=D||G||te||F;if(!ee)return null;var _=oe(s,h),X=u(e,p,_,Q),B=X||L||ee;return(0,l.jsxs)("div",{className:V+" "+p,children:[B&&(0,l.jsxs)("div",{className:"".concat(V,"-left ").concat(p).trim(),children:[X,L&&(0,l.jsx)(Ne.Z,(0,r.Z)({className:S()("".concat(V,"-avatar"),p,L.className)},L)),D&&(0,l.jsx)("span",{className:"".concat(V,"-title ").concat(p).trim(),title:typeof D=="string"?D:void 0,children:D}),G&&(0,l.jsx)("span",{className:"".concat(V,"-sub-title ").concat(p).trim(),title:typeof G=="string"?G:void 0,children:G}),te&&(0,l.jsx)("span",{className:"".concat(V,"-tags ").concat(p).trim(),children:te})]}),F&&(0,l.jsx)("span",{className:"".concat(V,"-extra ").concat(p).trim(),children:(0,l.jsx)(pe.Z,{children:F})})]})},ye=function(e,s,h){return s?(0,l.jsx)("div",{className:"".concat(e,"-footer ").concat(h).trim(),children:s}):null},ve=function(e,s,h){return(0,l.jsx)("div",{className:"".concat(e,"-content ").concat(h).trim(),children:s})},$e=function T(e){return e==null?void 0:e.map(function(s){var h;return(0,Pe.ET)(!!s.breadcrumbName,"Route.breadcrumbName is deprecated, please use Route.title instead."),(0,r.Z)((0,r.Z)({},s),{},{breadcrumbName:void 0,children:void 0,title:s.title||s.breadcrumbName},(h=s.children)!==null&&h!==void 0&&h.length?{menu:{items:T(s.children)}}:{})})},ze=function(e){var s,h=C.useState(!1),p=(0,O.Z)(h,2),D=p[0],L=p[1],G=function(nt){var ot=nt.width;return L(ot<768)},te=C.useContext(g.ZP.ConfigContext),F=te.getPrefixCls,Q=te.direction,V=e.prefixCls,ee=e.style,_=e.footer,X=e.children,B=e.breadcrumb,k=e.breadcrumbRender,E=e.className,H=e.contentWidth,le=e.layout,se=e.ghost,Ie=se===void 0?!0:se,q=F("page-header",V),ne=b(q),Le=ne.wrapSSR,he=ne.hashId,Se=function(){return B&&!(B!=null&&B.items)&&B!==null&&B!==void 0&&B.routes&&((0,Pe.ET)(!1,"The routes of Breadcrumb is deprecated, please use items instead."),B.items=$e(B.routes)),B!=null&&B.items?x(B,q):null},Ae=Se(),_e=B&&"props"in B,He=(s=k==null?void 0:k((0,r.Z)((0,r.Z)({},e),{},{prefixCls:q}),Ae))!==null&&s!==void 0?s:Ae,We=_e?B:He,Ye=S()(q,he,E,(0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)({},"".concat(q,"-has-breadcrumb"),!!We),"".concat(q,"-has-footer"),!!_),"".concat(q,"-rtl"),Q==="rtl"),"".concat(q,"-compact"),D),"".concat(q,"-wide"),H==="Fixed"&&le=="top"),"".concat(q,"-ghost"),Ie)),Ke=ie(q,e,Q,he),Qe=X&&ve(q,X,he),ce=ye(q,_,he);return!We&&!Ke&&!ce&&!Qe?(0,l.jsx)("div",{className:S()(he,["".concat(q,"-no-children")])}):Le((0,l.jsx)(Ve.Z,{onResize:G,children:(0,l.jsxs)("div",{className:Ye,style:ee,children:[We,Ke,Qe,ce]})}))},De=t(83832),Be=function(e){if(!e)return 1;var s=e.backingStorePixelRatio||e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||1;return(window.devicePixelRatio||1)/s},Oe=function(e){var s=(0,$.dQ)(),h=s.token,p=e.children,D=e.style,L=e.className,G=e.markStyle,te=e.markClassName,F=e.zIndex,Q=F===void 0?9:F,V=e.gapX,ee=V===void 0?212:V,_=e.gapY,X=_===void 0?222:_,B=e.width,k=B===void 0?120:B,E=e.height,H=E===void 0?64:E,le=e.rotate,se=le===void 0?-22:le,Ie=e.image,q=e.offsetLeft,ne=e.offsetTop,Le=e.fontStyle,he=Le===void 0?"normal":Le,Se=e.fontWeight,Ae=Se===void 0?"normal":Se,_e=e.fontColor,He=_e===void 0?h.colorFill:_e,We=e.fontSize,Ye=We===void 0?16:We,Ke=e.fontFamily,Qe=Ke===void 0?"sans-serif":Ke,ce=e.prefixCls,we=(0,C.useContext)(g.ZP.ConfigContext),nt=we.getPrefixCls,ot=nt("pro-layout-watermark",ce),vt=S()("".concat(ot,"-wrapper"),L),mt=S()(ot,te),gt=(0,C.useState)(""),st=(0,O.Z)(gt,2),ct=st[0],dt=st[1];return(0,C.useEffect)(function(){var rt=document.createElement("canvas"),Fe=rt.getContext("2d"),Je=Be(Fe),ht="".concat((ee+k)*Je,"px"),yt="".concat((X+H)*Je,"px"),pt=q||ee/2,Ct=ne||X/2;if(rt.setAttribute("width",ht),rt.setAttribute("height",yt),!Fe){console.error("\u5F53\u524D\u73AF\u5883\u4E0D\u652F\u6301Canvas");return}Fe.translate(pt*Je,Ct*Je),Fe.rotate(Math.PI/180*Number(se));var bt=k*Je,ut=H*Je,ft=function(at){var it=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,lt=Number(Ye)*Je;Fe.font="".concat(he," normal ").concat(Ae," ").concat(lt,"px/").concat(ut,"px ").concat(Qe),Fe.fillStyle=He,Array.isArray(at)?at==null||at.forEach(function(xt,St){return Fe.fillText(xt,0,St*lt+it)}):Fe.fillText(at,0,it?it+lt:0),dt(rt.toDataURL())};if(Ie){var et=new Image;et.crossOrigin="anonymous",et.referrerPolicy="no-referrer",et.src=Ie,et.onload=function(){if(Fe.drawImage(et,0,0,bt,ut),dt(rt.toDataURL()),e.content){ft(e.content,et.height+8);return}};return}if(e.content){ft(e.content);return}},[ee,X,q,ne,se,he,Ae,k,H,Qe,He,Ie,e.content,Ye]),(0,l.jsxs)("div",{style:(0,r.Z)({position:"relative"},D),className:vt,children:[p,(0,l.jsx)("div",{className:mt,style:(0,r.Z)((0,r.Z)({zIndex:Q,position:"absolute",left:0,top:0,width:"100%",height:"100%",backgroundSize:"".concat(ee+k,"px"),pointerEvents:"none",backgroundRepeat:"repeat"},ct?{backgroundImage:"url('".concat(ct,"')")}:{}),G)})]})},me=[576,768,992,1200].map(function(T){return"@media (max-width: ".concat(T,"px)")}),ue=(0,O.Z)(me,4),Ce=ue[0],be=ue[1],ge=ue[2],Ee=ue[3],Te=function(e){var s,h,p,D,L,G,te,F,Q,V,ee,_,X,B,k,E,H,le;return(0,n.Z)({},e.componentCls,(0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)({position:"relative","&-children-container":{paddingBlockStart:0,paddingBlockEnd:(s=e.layout)===null||s===void 0||(s=s.pageContainer)===null||s===void 0?void 0:s.paddingBlockPageContainerContent,paddingInline:(h=e.layout)===null||h===void 0||(h=h.pageContainer)===null||h===void 0?void 0:h.paddingInlinePageContainerContent},"&-children-container-no-header":{paddingBlockStart:(p=e.layout)===null||p===void 0||(p=p.pageContainer)===null||p===void 0?void 0:p.paddingBlockPageContainerContent},"&-affix":(0,n.Z)({},"".concat(e.antCls,"-affix"),(0,n.Z)({},"".concat(e.componentCls,"-warp"),{backgroundColor:(D=e.layout)===null||D===void 0||(D=D.pageContainer)===null||D===void 0?void 0:D.colorBgPageContainerFixed,transition:"background-color 0.3s",boxShadow:"0 2px 8px #f0f1f2"}))},"& &-warp-page-header",(0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)({paddingBlockStart:((L=(G=e.layout)===null||G===void 0||(G=G.pageContainer)===null||G===void 0?void 0:G.paddingBlockPageContainerContent)!==null&&L!==void 0?L:40)/4,paddingBlockEnd:((te=(F=e.layout)===null||F===void 0||(F=F.pageContainer)===null||F===void 0?void 0:F.paddingBlockPageContainerContent)!==null&&te!==void 0?te:40)/2,paddingInlineStart:(Q=e.layout)===null||Q===void 0||(Q=Q.pageContainer)===null||Q===void 0?void 0:Q.paddingInlinePageContainerContent,paddingInlineEnd:(V=e.layout)===null||V===void 0||(V=V.pageContainer)===null||V===void 0?void 0:V.paddingInlinePageContainerContent},"& ~ ".concat(e.proComponentsCls,"-grid-content"),(0,n.Z)({},"".concat(e.proComponentsCls,"-page-container-children-content"),{paddingBlock:((ee=(_=e.layout)===null||_===void 0||(_=_.pageContainer)===null||_===void 0?void 0:_.paddingBlockPageContainerContent)!==null&&ee!==void 0?ee:24)/3})),"".concat(e.antCls,"-page-header-breadcrumb"),{paddingBlockStart:((X=(B=e.layout)===null||B===void 0||(B=B.pageContainer)===null||B===void 0?void 0:B.paddingBlockPageContainerContent)!==null&&X!==void 0?X:40)/4+10}),"".concat(e.antCls,"-page-header-heading"),{paddingBlockStart:((k=(E=e.layout)===null||E===void 0||(E=E.pageContainer)===null||E===void 0?void 0:E.paddingBlockPageContainerContent)!==null&&k!==void 0?k:40)/4}),"".concat(e.antCls,"-page-header-footer"),{marginBlockStart:((H=(le=e.layout)===null||le===void 0||(le=le.pageContainer)===null||le===void 0?void 0:le.paddingBlockPageContainerContent)!==null&&H!==void 0?H:40)/4})),"&-detail",(0,n.Z)({display:"flex"},Ce,{display:"block"})),"&-main",{width:"100%"}),"&-row",(0,n.Z)({display:"flex",width:"100%"},be,{display:"block"})),"&-content",{flex:"auto",width:"100%"}),"&-extraContent",(0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)({flex:"0 1 auto",minWidth:"242px",marginInlineStart:88,textAlign:"end"},Ee,{marginInlineStart:44}),ge,{marginInlineStart:20}),be,{marginInlineStart:0,textAlign:"start"}),Ce,{marginInlineStart:0})))};function Xe(T,e){return(0,$.Xj)("ProLayoutPageContainer",function(s){var h,p=(0,r.Z)((0,r.Z)({},s),{},{componentCls:".".concat(T),layout:(0,r.Z)((0,r.Z)({},s==null?void 0:s.layout),{},{pageContainer:(0,r.Z)((0,r.Z)({},s==null||(h=s.layout)===null||h===void 0?void 0:h.pageContainer),e)})});return[Te(p)]})}function xe(T,e){var s=e.stylish;return(0,$.Xj)("ProLayoutPageContainerStylish",function(h){var p=(0,r.Z)((0,r.Z)({},h),{},{componentCls:".".concat(T)});return s?[(0,n.Z)({},"div".concat(p.componentCls),s==null?void 0:s(p))]:[]})}var Ue=t(1977),Ze=["title","content","pageHeaderRender","header","prefixedClassName","extraContent","childrenContentStyle","style","prefixCls","hashId","value","breadcrumbRender"],Me=["children","loading","className","style","footer","affixProps","token","fixedHeader","breadcrumbRender","footerToolBarProps","childrenContentStyle"];function Re(T){return(0,d.Z)(T)==="object"?T:{spinning:T}}var Ge=function(e){var s=e.tabList,h=e.tabActiveKey,p=e.onTabChange,D=e.hashId,L=e.tabBarExtraContent,G=e.tabProps,te=e.prefixedClassName;return Array.isArray(s)||L?(0,l.jsx)(m.Z,(0,r.Z)((0,r.Z)({className:"".concat(te,"-tabs ").concat(D).trim(),activeKey:h,onChange:function(Q){p&&p(Q)},tabBarExtraContent:L,items:s==null?void 0:s.map(function(F,Q){var V;return(0,r.Z)((0,r.Z)({label:F.tab},F),{},{key:((V=F.key)===null||V===void 0?void 0:V.toString())||(Q==null?void 0:Q.toString())})})},G),{},{children:(0,Ue.n)(y.Z,"4.23.0")<0?s==null?void 0:s.map(function(F,Q){return(0,l.jsx)(m.Z.TabPane,(0,r.Z)({tab:F.tab},F),F.key||Q)}):null})):null},tt=function(e,s,h,p){return!e&&!s?null:(0,l.jsx)("div",{className:"".concat(h,"-detail ").concat(p).trim(),children:(0,l.jsx)("div",{className:"".concat(h,"-main ").concat(p).trim(),children:(0,l.jsxs)("div",{className:"".concat(h,"-row ").concat(p).trim(),children:[e&&(0,l.jsx)("div",{className:"".concat(h,"-content ").concat(p).trim(),children:e}),s&&(0,l.jsx)("div",{className:"".concat(h,"-extraContent ").concat(p).trim(),children:s})]})})})},ke=function(e){var s=useContext(RouteContext);return _jsx("div",{style:{height:"100%",display:"flex",alignItems:"center"},children:_jsx(Breadcrumb,_objectSpread(_objectSpread(_objectSpread({},s==null?void 0:s.breadcrumb),s==null?void 0:s.breadcrumbProps),e))})},qe=function(e){var s,h=e.title,p=e.content,D=e.pageHeaderRender,L=e.header,G=e.prefixedClassName,te=e.extraContent,F=e.childrenContentStyle,Q=e.style,V=e.prefixCls,ee=e.hashId,_=e.value,X=e.breadcrumbRender,B=(0,c.Z)(e,Ze),k=function(){if(X)return X};if(D===!1)return null;if(D)return(0,l.jsxs)(l.Fragment,{children:[" ",D((0,r.Z)((0,r.Z)({},e),_))]});var E=h;!h&&h!==!1&&(E=_.title);var H=(0,r.Z)((0,r.Z)((0,r.Z)({},_),{},{title:E},B),{},{footer:Ge((0,r.Z)((0,r.Z)({},B),{},{hashId:ee,breadcrumbRender:X,prefixedClassName:G}))},L),le=H,se=le.breadcrumb,Ie=(!se||!(se!=null&&se.itemRender)&&!(se!=null&&(s=se.items)!==null&&s!==void 0&&s.length))&&!X;return["title","subTitle","extra","tags","footer","avatar","backIcon"].every(function(q){return!H[q]})&&Ie&&!p&&!te?null:(0,l.jsx)(ze,(0,r.Z)((0,r.Z)({},H),{},{className:"".concat(G,"-warp-page-header ").concat(ee).trim(),breadcrumb:X===!1?void 0:(0,r.Z)((0,r.Z)({},H.breadcrumb),_.breadcrumbProps),breadcrumbRender:k(),prefixCls:V,children:(L==null?void 0:L.children)||tt(p,te,G,ee)}))},ae=function(e){var s,h,p=e.children,D=e.loading,L=D===void 0?!1:D,G=e.className,te=e.style,F=e.footer,Q=e.affixProps,V=e.token,ee=e.fixedHeader,_=e.breadcrumbRender,X=e.footerToolBarProps,B=e.childrenContentStyle,k=(0,c.Z)(e,Me),E=(0,C.useContext)(K.X);(0,C.useEffect)(function(){var ce;return!E||!(E!=null&&E.setHasPageContainer)?function(){}:(E==null||(ce=E.setHasPageContainer)===null||ce===void 0||ce.call(E,function(we){return we+1}),function(){var we;E==null||(we=E.setHasPageContainer)===null||we===void 0||we.call(E,function(nt){return nt-1})})},[]);var H=(0,C.useContext)(f.L_),le=H.token,se=(0,C.useContext)(g.ZP.ConfigContext),Ie=se.getPrefixCls,q=e.prefixCls||Ie("pro"),ne="".concat(q,"-page-container"),Le=Xe(ne,V),he=Le.wrapSSR,Se=Le.hashId,Ae=xe("".concat(ne,".").concat(ne,"-stylish"),{stylish:e.stylish}),_e=(0,C.useMemo)(function(){var ce;return _==!1?!1:_||(k==null||(ce=k.header)===null||ce===void 0?void 0:ce.breadcrumbRender)},[_,k==null||(s=k.header)===null||s===void 0?void 0:s.breadcrumbRender]),He=qe((0,r.Z)((0,r.Z)({},k),{},{breadcrumbRender:_e,ghost:!0,hashId:Se,prefixCls:void 0,prefixedClassName:ne,value:E})),We=(0,C.useMemo)(function(){if(C.isValidElement(L))return L;if(typeof L=="boolean"&&!L)return null;var ce=Re(L);return ce.spinning?(0,l.jsx)(De.S,(0,r.Z)({},ce)):null},[L]),Ye=(0,C.useMemo)(function(){return p?(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{className:S()(Se,"".concat(ne,"-children-container"),(0,n.Z)({},"".concat(ne,"-children-container-no-header"),!He)),style:B,children:p})}):null},[p,ne,B,Se]),Ke=(0,C.useMemo)(function(){var ce=We||Ye;if(e.waterMarkProps||E.waterMarkProps){var we=(0,r.Z)((0,r.Z)({},E.waterMarkProps),e.waterMarkProps);return(0,l.jsx)(Oe,(0,r.Z)((0,r.Z)({},we),{},{children:ce}))}return ce},[e.waterMarkProps,E.waterMarkProps,We,Ye]),Qe=S()(ne,Se,G,(0,n.Z)((0,n.Z)((0,n.Z)({},"".concat(ne,"-with-footer"),F),"".concat(ne,"-with-affix"),ee&&He),"".concat(ne,"-stylish"),!!k.stylish));return he(Ae.wrapSSR((0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{style:te,className:Qe,children:[ee&&He?(0,l.jsx)(W.Z,(0,r.Z)((0,r.Z)({offsetTop:E.hasHeader&&E.fixedHeader?(h=le.layout)===null||h===void 0||(h=h.header)===null||h===void 0?void 0:h.heightLayoutHeader:1},Q),{},{className:"".concat(ne,"-affix ").concat(Se).trim(),children:(0,l.jsx)("div",{className:"".concat(ne,"-warp ").concat(Se).trim(),children:He})})):He,Ke&&(0,l.jsx)(v.f,{children:Ke})]}),F&&(0,l.jsx)(z,(0,r.Z)((0,r.Z)({stylish:k.footerStylish,prefixCls:q},X),{},{children:F}))]})))},J=function(e){return(0,l.jsx)(f._Y,{needDeps:!0,children:(0,l.jsx)(ae,(0,r.Z)({},e))})},Y=function(e){var s=useContext(RouteContext);return qe(_objectSpread(_objectSpread({},e),{},{hashId:"",value:s}))}},3770:function(o,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;const n=c(t(27863));function c(d){return d&&d.__esModule?d:{default:d}}const r=n;a.default=r,o.exports=r},77059:function(o,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;const n=c(t(21379));function c(d){return d&&d.__esModule?d:{default:d}}const r=n;a.default=r,o.exports=r},33046:function(o,a,t){"use strict";"use client";var n=t(64836).default,c=t(75263).default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var r=n(t(10434)),d=n(t(27424)),f=n(t(38416)),m=n(t(70215)),y=c(t(67294)),g=n(t(93967)),W=t(87646),I=n(t(61711)),S=n(t(27727)),C=t(26814),K=t(72014),N=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];(0,C.setTwoToneColor)(W.blue.primary);var U=y.forwardRef(function($,fe){var w=$.className,Z=$.icon,l=$.spin,R=$.rotate,z=$.tabIndex,v=$.onClick,O=$.twoToneColor,M=(0,m.default)($,N),j=y.useContext(I.default),A=j.prefixCls,re=A===void 0?"anticon":A,je=j.rootClassName,Ne=(0,g.default)(je,re,(0,f.default)((0,f.default)({},"".concat(re,"-").concat(Z.name),!!Z.name),"".concat(re,"-spin"),!!l||Z.name==="loading"),w),pe=z;pe===void 0&&v&&(pe=-1);var Ve=R?{msTransform:"rotate(".concat(R,"deg)"),transform:"rotate(".concat(R,"deg)")}:void 0,Pe=(0,K.normalizeTwoToneColors)(O),i=(0,d.default)(Pe,2),P=i[0],b=i[1];return y.createElement("span",(0,r.default)({role:"img","aria-label":Z.name},M,{ref:fe,tabIndex:pe,onClick:v,className:Ne}),y.createElement(S.default,{icon:Z,primaryColor:P,secondaryColor:b,style:Ve}))});U.displayName="AntdIcon",U.getTwoToneColor=C.getTwoToneColor,U.setTwoToneColor=C.setTwoToneColor;var de=a.default=U},61711:function(o,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var n=t(67294),c=(0,n.createContext)({}),r=a.default=c},27727:function(o,a,t){"use strict";var n=t(64836).default,c=t(75263).default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var r=n(t(70215)),d=n(t(42122)),f=c(t(67294)),m=t(72014),y=["icon","className","onClick","style","primaryColor","secondaryColor"],g={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};function W(K){var N=K.primaryColor,U=K.secondaryColor;g.primaryColor=N,g.secondaryColor=U||(0,m.getSecondaryColor)(N),g.calculated=!!U}function I(){return(0,d.default)({},g)}var S=function(N){var U=N.icon,de=N.className,$=N.onClick,fe=N.style,w=N.primaryColor,Z=N.secondaryColor,l=(0,r.default)(N,y),R=f.useRef(),z=g;if(w&&(z={primaryColor:w,secondaryColor:Z||(0,m.getSecondaryColor)(w)}),(0,m.useInsertStyles)(R),(0,m.warning)((0,m.isIconDefinition)(U),"icon should be icon definiton, but got ".concat(U)),!(0,m.isIconDefinition)(U))return null;var v=U;return v&&typeof v.icon=="function"&&(v=(0,d.default)((0,d.default)({},v),{},{icon:v.icon(z.primaryColor,z.secondaryColor)})),(0,m.generate)(v.icon,"svg-".concat(v.name),(0,d.default)((0,d.default)({className:de,onClick:$,style:fe,"data-icon":v.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},l),{},{ref:R}))};S.displayName="IconReact",S.getTwoToneColors=I,S.setTwoToneColors=W;var C=a.default=S},26814:function(o,a,t){"use strict";var n=t(64836).default;Object.defineProperty(a,"__esModule",{value:!0}),a.getTwoToneColor=m,a.setTwoToneColor=f;var c=n(t(27424)),r=n(t(27727)),d=t(72014);function f(y){var g=(0,d.normalizeTwoToneColors)(y),W=(0,c.default)(g,2),I=W[0],S=W[1];return r.default.setTwoToneColors({primaryColor:I,secondaryColor:S})}function m(){var y=r.default.getTwoToneColors();return y.calculated?[y.primaryColor,y.secondaryColor]:y.primaryColor}},27863:function(o,a,t){"use strict";var n=t(75263).default,c=t(64836).default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var r=c(t(10434)),d=n(t(67294)),f=c(t(47356)),m=c(t(33046)),y=function(S,C){return d.createElement(m.default,(0,r.default)({},S,{ref:C,icon:f.default}))},g=d.forwardRef(y),W=a.default=g},21379:function(o,a,t){"use strict";var n=t(75263).default,c=t(64836).default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var r=c(t(10434)),d=n(t(67294)),f=c(t(44149)),m=c(t(33046)),y=function(S,C){return d.createElement(m.default,(0,r.default)({},S,{ref:C,icon:f.default}))},g=d.forwardRef(y),W=a.default=g},72014:function(o,a,t){"use strict";var n=t(75263).default,c=t(64836).default;Object.defineProperty(a,"__esModule",{value:!0}),a.generate=U,a.getSecondaryColor=de,a.iconStyles=void 0,a.isIconDefinition=K,a.normalizeAttrs=N,a.normalizeTwoToneColors=$,a.useInsertStyles=a.svgBaseProps=void 0,a.warning=C;var r=c(t(42122)),d=c(t(18698)),f=t(87646),m=t(93399),y=t(63298),g=c(t(45520)),W=n(t(67294)),I=c(t(61711));function S(l){return l.replace(/-(.)/g,function(R,z){return z.toUpperCase()})}function C(l,R){(0,g.default)(l,"[@ant-design/icons] ".concat(R))}function K(l){return(0,d.default)(l)==="object"&&typeof l.name=="string"&&typeof l.theme=="string"&&((0,d.default)(l.icon)==="object"||typeof l.icon=="function")}function N(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return Object.keys(l).reduce(function(R,z){var v=l[z];switch(z){case"class":R.className=v,delete R.class;break;default:delete R[z],R[S(z)]=v}return R},{})}function U(l,R,z){return z?W.default.createElement(l.tag,(0,r.default)((0,r.default)({key:R},N(l.attrs)),z),(l.children||[]).map(function(v,O){return U(v,"".concat(R,"-").concat(l.tag,"-").concat(O))})):W.default.createElement(l.tag,(0,r.default)({key:R},N(l.attrs)),(l.children||[]).map(function(v,O){return U(v,"".concat(R,"-").concat(l.tag,"-").concat(O))}))}function de(l){return(0,f.generate)(l)[0]}function $(l){return l?Array.isArray(l)?l:[l]:[]}var fe=a.svgBaseProps={width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",focusable:"false"},w=a.iconStyles=`
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`,Z=a.useInsertStyles=function(R){var z=(0,W.useContext)(I.default),v=z.csp,O=z.prefixCls,M=z.layer,j=w;O&&(j=j.replace(/anticon/g,O)),M&&(j="@layer ".concat(M,` {
`).concat(j,`
}`)),(0,W.useEffect)(function(){var A=R.current,re=(0,y.getShadowRoot)(A);(0,m.updateCSS)(j,"@ant-design-icons",{prepend:!M,csp:v,attachTo:re})},[])}},64218:function(o,a,t){"use strict";t.d(a,{Z:function(){return z}});var n=t(67294),c=t(93967),r=t.n(c),d=t(9220),f=t(74902),m=t(75164);function y(v){let O;const M=A=>()=>{O=null,v.apply(void 0,(0,f.Z)(A))},j=(...A)=>{O==null&&(O=(0,m.Z)(M(A)))};return j.cancel=()=>{m.Z.cancel(O),O=null},j}var g=y,W=t(53124),I=t(83559);const S=v=>{const{componentCls:O}=v;return{[O]:{position:"fixed",zIndex:v.zIndexPopup}}},C=v=>({zIndexPopup:v.zIndexBase+10});var K=(0,I.I$)("Affix",S,C);function N(v){return v!==window?v.getBoundingClientRect():{top:0,bottom:window.innerHeight}}function U(v,O,M){if(M!==void 0&&Math.round(O.top)>Math.round(v.top)-M)return M+O.top}function de(v,O,M){if(M!==void 0&&Math.round(O.bottom)<Math.round(v.bottom)+M){const j=window.innerHeight-O.bottom;return M+j}}var $=function(v,O){var M={};for(var j in v)Object.prototype.hasOwnProperty.call(v,j)&&O.indexOf(j)<0&&(M[j]=v[j]);if(v!=null&&typeof Object.getOwnPropertySymbols=="function")for(var A=0,j=Object.getOwnPropertySymbols(v);A<j.length;A++)O.indexOf(j[A])<0&&Object.prototype.propertyIsEnumerable.call(v,j[A])&&(M[j[A]]=v[j[A]]);return M};const fe=["resize","scroll","touchstart","touchmove","touchend","pageshow","load"];function w(){return typeof window!="undefined"?window:null}const Z=0,l=1;var z=n.forwardRef((v,O)=>{var M;const{style:j,offsetTop:A,offsetBottom:re,prefixCls:je,className:Ne,rootClassName:pe,children:Ve,target:Pe,onChange:i,onTestUpdatePosition:P}=v,b=$(v,["style","offsetTop","offsetBottom","prefixCls","className","rootClassName","children","target","onChange","onTestUpdatePosition"]),{getPrefixCls:u,getTargetContainer:x}=n.useContext(W.E_),oe=u("affix",je),[ie,ye]=n.useState(!1),[ve,$e]=n.useState(),[ze,De]=n.useState(),Be=n.useRef(Z),Oe=n.useRef(null),me=n.useRef(null),ue=n.useRef(null),Ce=n.useRef(null),be=n.useRef(null),ge=(M=Pe!=null?Pe:x)!==null&&M!==void 0?M:w,Ee=re===void 0&&A===void 0?0:A,Te=()=>{if(Be.current!==l||!Ce.current||!ue.current||!ge)return;const ae=ge();if(ae){const J={status:Z},Y=N(ue.current);if(Y.top===0&&Y.left===0&&Y.width===0&&Y.height===0)return;const T=N(ae),e=U(Y,T,Ee),s=de(Y,T,re);e!==void 0?(J.affixStyle={position:"fixed",top:e,width:Y.width,height:Y.height},J.placeholderStyle={width:Y.width,height:Y.height}):s!==void 0&&(J.affixStyle={position:"fixed",bottom:s,width:Y.width,height:Y.height},J.placeholderStyle={width:Y.width,height:Y.height}),J.lastAffix=!!J.affixStyle,ie!==J.lastAffix&&(i==null||i(J.lastAffix)),Be.current=J.status,$e(J.affixStyle),De(J.placeholderStyle),ye(J.lastAffix)}},Xe=()=>{Be.current=l,Te()},xe=g(()=>{Xe()}),Ue=g(()=>{if(ge&&ve){const ae=ge();if(ae&&ue.current){const J=N(ae),Y=N(ue.current),T=U(Y,J,Ee),e=de(Y,J,re);if(T!==void 0&&ve.top===T||e!==void 0&&ve.bottom===e)return}}Xe()}),Ze=()=>{const ae=ge==null?void 0:ge();ae&&(fe.forEach(J=>{var Y;me.current&&((Y=Oe.current)===null||Y===void 0||Y.removeEventListener(J,me.current)),ae==null||ae.addEventListener(J,Ue)}),Oe.current=ae,me.current=Ue)},Me=()=>{be.current&&(clearTimeout(be.current),be.current=null);const ae=ge==null?void 0:ge();fe.forEach(J=>{var Y;ae==null||ae.removeEventListener(J,Ue),me.current&&((Y=Oe.current)===null||Y===void 0||Y.removeEventListener(J,me.current))}),xe.cancel(),Ue.cancel()};n.useImperativeHandle(O,()=>({updatePosition:xe})),n.useEffect(()=>(be.current=setTimeout(Ze),()=>Me()),[]),n.useEffect(()=>(Ze(),()=>Me()),[Pe,ve,ie,A,re]),n.useEffect(()=>{xe()},[Pe,A,re]);const[Re,Ge,tt]=K(oe),ke=r()(pe,Ge,oe,tt),qe=r()({[ke]:ve});return Re(n.createElement(d.Z,{onResize:xe},n.createElement("div",Object.assign({style:j,className:Ne,ref:ue},b),ve&&n.createElement("div",{style:ze,"aria-hidden":"true"}),n.createElement("div",{className:qe,ref:Ce,style:ve},n.createElement(d.Z,{onResize:xe},Ve)))))})},85673:function(o,a,t){"use strict";t.d(a,{Z:function(){return Pe}});var n=t(67294),c=t(93967),r=t.n(c),d=t(50344),f=t(64217),m=t(96159),y=t(53124),g=t(13622),W=t(7743);const I=({children:i})=>{const{getPrefixCls:P}=n.useContext(y.E_),b=P("breadcrumb");return n.createElement("li",{className:`${b}-separator`,"aria-hidden":"true"},i===""?i:i||"/")};I.__ANT_BREADCRUMB_SEPARATOR=!0;var S=I,C=function(i,P){var b={};for(var u in i)Object.prototype.hasOwnProperty.call(i,u)&&P.indexOf(u)<0&&(b[u]=i[u]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var x=0,u=Object.getOwnPropertySymbols(i);x<u.length;x++)P.indexOf(u[x])<0&&Object.prototype.propertyIsEnumerable.call(i,u[x])&&(b[u[x]]=i[u[x]]);return b};function K(i,P){if(i.title===void 0||i.title===null)return null;const b=Object.keys(P).join("|");return typeof i.title=="object"?i.title:String(i.title).replace(new RegExp(`:(${b})`,"g"),(u,x)=>P[x]||u)}function N(i,P,b,u){if(b==null)return null;const{className:x,onClick:oe}=P,ie=C(P,["className","onClick"]),ye=Object.assign(Object.assign({},(0,f.Z)(ie,{data:!0,aria:!0})),{onClick:oe});return u!==void 0?n.createElement("a",Object.assign({},ye,{className:r()(`${i}-link`,x),href:u}),b):n.createElement("span",Object.assign({},ye,{className:r()(`${i}-link`,x)}),b)}function U(i,P){return(u,x,oe,ie,ye)=>{if(P)return P(u,x,oe,ie);const ve=K(u,x);return N(i,u,ve,ye)}}var de=function(i,P){var b={};for(var u in i)Object.prototype.hasOwnProperty.call(i,u)&&P.indexOf(u)<0&&(b[u]=i[u]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var x=0,u=Object.getOwnPropertySymbols(i);x<u.length;x++)P.indexOf(u[x])<0&&Object.prototype.propertyIsEnumerable.call(i,u[x])&&(b[u[x]]=i[u[x]]);return b};const $=i=>{const{prefixCls:P,separator:b="/",children:u,menu:x,overlay:oe,dropdownProps:ie,href:ye}=i,$e=(ze=>{if(x||oe){const De=Object.assign({},ie);if(x){const Be=x||{},{items:Oe}=Be,me=de(Be,["items"]);De.menu=Object.assign(Object.assign({},me),{items:Oe==null?void 0:Oe.map((ue,Ce)=>{var{key:be,title:ge,label:Ee,path:Te}=ue,Xe=de(ue,["key","title","label","path"]);let xe=Ee!=null?Ee:ge;return Te&&(xe=n.createElement("a",{href:`${ye}${Te}`},xe)),Object.assign(Object.assign({},Xe),{key:be!=null?be:Ce,label:xe})})})}else oe&&(De.overlay=oe);return n.createElement(W.Z,Object.assign({placement:"bottom"},De),n.createElement("span",{className:`${P}-overlay-link`},ze,n.createElement(g.Z,null)))}return ze})(u);return $e!=null?n.createElement(n.Fragment,null,n.createElement("li",null,$e),b&&n.createElement(S,null,b)):null},fe=i=>{const{prefixCls:P,children:b,href:u}=i,x=de(i,["prefixCls","children","href"]),{getPrefixCls:oe}=n.useContext(y.E_),ie=oe("breadcrumb",P);return n.createElement($,Object.assign({},x,{prefixCls:ie}),N(ie,x,b,u))};fe.__ANT_BREADCRUMB_ITEM=!0;var w=fe,Z=t(11568),l=t(14747),R=t(83559),z=t(83262);const v=i=>{const{componentCls:P,iconCls:b,calc:u}=i;return{[P]:Object.assign(Object.assign({},(0,l.Wf)(i)),{color:i.itemColor,fontSize:i.fontSize,[b]:{fontSize:i.iconFontSize},ol:{display:"flex",flexWrap:"wrap",margin:0,padding:0,listStyle:"none"},a:Object.assign({color:i.linkColor,transition:`color ${i.motionDurationMid}`,padding:`0 ${(0,Z.bf)(i.paddingXXS)}`,borderRadius:i.borderRadiusSM,height:i.fontHeight,display:"inline-block",marginInline:u(i.marginXXS).mul(-1).equal(),"&:hover":{color:i.linkHoverColor,backgroundColor:i.colorBgTextHover}},(0,l.Qy)(i)),"li:last-child":{color:i.lastItemColor},[`${P}-separator`]:{marginInline:i.separatorMargin,color:i.separatorColor},[`${P}-link`]:{[`
          > ${b} + span,
          > ${b} + a
        `]:{marginInlineStart:i.marginXXS}},[`${P}-overlay-link`]:{borderRadius:i.borderRadiusSM,height:i.fontHeight,display:"inline-block",padding:`0 ${(0,Z.bf)(i.paddingXXS)}`,marginInline:u(i.marginXXS).mul(-1).equal(),[`> ${b}`]:{marginInlineStart:i.marginXXS,fontSize:i.fontSizeIcon},"&:hover":{color:i.linkHoverColor,backgroundColor:i.colorBgTextHover,a:{color:i.linkHoverColor}},a:{"&:hover":{backgroundColor:"transparent"}}},[`&${i.componentCls}-rtl`]:{direction:"rtl"}})}},O=i=>({itemColor:i.colorTextDescription,lastItemColor:i.colorText,iconFontSize:i.fontSize,linkColor:i.colorTextDescription,linkHoverColor:i.colorText,separatorColor:i.colorTextDescription,separatorMargin:i.marginXS});var M=(0,R.I$)("Breadcrumb",i=>{const P=(0,z.IX)(i,{});return v(P)},O),j=function(i,P){var b={};for(var u in i)Object.prototype.hasOwnProperty.call(i,u)&&P.indexOf(u)<0&&(b[u]=i[u]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var x=0,u=Object.getOwnPropertySymbols(i);x<u.length;x++)P.indexOf(u[x])<0&&Object.prototype.propertyIsEnumerable.call(i,u[x])&&(b[u[x]]=i[u[x]]);return b};function A(i){const{breadcrumbName:P,children:b}=i,u=j(i,["breadcrumbName","children"]),x=Object.assign({title:P},u);return b&&(x.menu={items:b.map(oe=>{var{breadcrumbName:ie}=oe,ye=j(oe,["breadcrumbName"]);return Object.assign(Object.assign({},ye),{title:ie})})}),x}function re(i,P){return(0,n.useMemo)(()=>i||(P?P.map(A):null),[i,P])}var je=function(i,P){var b={};for(var u in i)Object.prototype.hasOwnProperty.call(i,u)&&P.indexOf(u)<0&&(b[u]=i[u]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var x=0,u=Object.getOwnPropertySymbols(i);x<u.length;x++)P.indexOf(u[x])<0&&Object.prototype.propertyIsEnumerable.call(i,u[x])&&(b[u[x]]=i[u[x]]);return b};const Ne=(i,P)=>{if(P===void 0)return P;let b=(P||"").replace(/^\//,"");return Object.keys(i).forEach(u=>{b=b.replace(`:${u}`,i[u])}),b},pe=i=>{const{prefixCls:P,separator:b="/",style:u,className:x,rootClassName:oe,routes:ie,items:ye,children:ve,itemRender:$e,params:ze={}}=i,De=je(i,["prefixCls","separator","style","className","rootClassName","routes","items","children","itemRender","params"]),{getPrefixCls:Be,direction:Oe,breadcrumb:me}=n.useContext(y.E_);let ue;const Ce=Be("breadcrumb",P),[be,ge,Ee]=M(Ce),Te=re(ye,ie),Xe=U(Ce,$e);if(Te&&Te.length>0){const Ze=[],Me=ye||ie;ue=Te.map((Re,Ge)=>{const{path:tt,key:ke,type:qe,menu:ae,overlay:J,onClick:Y,className:T,separator:e,dropdownProps:s}=Re,h=Ne(ze,tt);h!==void 0&&Ze.push(h);const p=ke!=null?ke:Ge;if(qe==="separator")return n.createElement(S,{key:p},e);const D={},L=Ge===Te.length-1;ae?D.menu=ae:J&&(D.overlay=J);let{href:G}=Re;return Ze.length&&h!==void 0&&(G=`#/${Ze.join("/")}`),n.createElement($,Object.assign({key:p},D,(0,f.Z)(Re,{data:!0,aria:!0}),{className:T,dropdownProps:s,href:G,separator:L?"":b,onClick:Y,prefixCls:Ce}),Xe(Re,ze,Me,Ze,G))})}else if(ve){const Ze=(0,d.Z)(ve).length;ue=(0,d.Z)(ve).map((Me,Re)=>{if(!Me)return Me;const Ge=Re===Ze-1;return(0,m.Tm)(Me,{separator:Ge?"":b,key:Re})})}const xe=r()(Ce,me==null?void 0:me.className,{[`${Ce}-rtl`]:Oe==="rtl"},x,oe,ge,Ee),Ue=Object.assign(Object.assign({},me==null?void 0:me.style),u);return be(n.createElement("nav",Object.assign({className:xe,style:Ue},De),n.createElement("ol",null,ue)))};pe.Item=w,pe.Separator=S;var Ve=pe,Pe=Ve},19158:function(o,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=t;function t(){return!!(typeof window!="undefined"&&window.document&&window.document.createElement)}},32191:function(o,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=t;function t(n,c){if(!n)return!1;if(n.contains)return n.contains(c);for(var r=c;r;){if(r===n)return!0;r=r.parentNode}return!1}},93399:function(o,a,t){"use strict";var n=t(64836).default;Object.defineProperty(a,"__esModule",{value:!0}),a.clearContainerCache=$,a.injectCSS=K,a.removeCSS=U,a.updateCSS=fe;var c=n(t(42122)),r=n(t(19158)),d=n(t(32191)),f="data-rc-order",m="data-rc-priority",y="rc-util-key",g=new Map;function W(){var w=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},Z=w.mark;return Z?Z.startsWith("data-")?Z:"data-".concat(Z):y}function I(w){if(w.attachTo)return w.attachTo;var Z=document.querySelector("head");return Z||document.body}function S(w){return w==="queue"?"prependQueue":w?"prepend":"append"}function C(w){return Array.from((g.get(w)||w).children).filter(function(Z){return Z.tagName==="STYLE"})}function K(w){var Z=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!(0,r.default)())return null;var l=Z.csp,R=Z.prepend,z=Z.priority,v=z===void 0?0:z,O=S(R),M=O==="prependQueue",j=document.createElement("style");j.setAttribute(f,O),M&&v&&j.setAttribute(m,"".concat(v)),l!=null&&l.nonce&&(j.nonce=l==null?void 0:l.nonce),j.innerHTML=w;var A=I(Z),re=A.firstChild;if(R){if(M){var je=(Z.styles||C(A)).filter(function(Ne){if(!["prepend","prependQueue"].includes(Ne.getAttribute(f)))return!1;var pe=Number(Ne.getAttribute(m)||0);return v>=pe});if(je.length)return A.insertBefore(j,je[je.length-1].nextSibling),j}A.insertBefore(j,re)}else A.appendChild(j);return j}function N(w){var Z=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},l=I(Z);return(Z.styles||C(l)).find(function(R){return R.getAttribute(W(Z))===w})}function U(w){var Z=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},l=N(w,Z);if(l){var R=I(Z);R.removeChild(l)}}function de(w,Z){var l=g.get(w);if(!l||!(0,d.default)(document,l)){var R=K("",Z),z=R.parentNode;g.set(w,z),w.removeChild(R)}}function $(){g.clear()}function fe(w,Z){var l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},R=I(l),z=C(R),v=(0,c.default)((0,c.default)({},l),{},{styles:z});de(R,v);var O=N(Z,v);if(O){var M,j;if((M=v.csp)!==null&&M!==void 0&&M.nonce&&O.nonce!==((j=v.csp)===null||j===void 0?void 0:j.nonce)){var A;O.nonce=(A=v.csp)===null||A===void 0?void 0:A.nonce}return O.innerHTML!==w&&(O.innerHTML=w),O}var re=K(w,v);return re.setAttribute(W(v),Z),re}},63298:function(o,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.getShadowRoot=c,a.inShadow=n;function t(r){var d;return r==null||(d=r.getRootNode)===null||d===void 0?void 0:d.call(r)}function n(r){return t(r)instanceof ShadowRoot}function c(r){return n(r)?t(r):null}},45520:function(o,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.call=m,a.default=void 0,a.note=d,a.noteOnce=g,a.preMessage=void 0,a.resetWarned=f,a.warning=r,a.warningOnce=y;var t={},n=[],c=a.preMessage=function(S){n.push(S)};function r(I,S){if(0)var C}function d(I,S){if(0)var C}function f(){t={}}function m(I,S,C){!S&&!t[C]&&(I(!1,C),t[C]=!0)}function y(I,S){m(r,I,S)}function g(I,S){m(d,I,S)}y.preMessage=c,y.resetWarned=f,y.noteOnce=g;var W=a.default=y},73897:function(o){function a(t,n){(n==null||n>t.length)&&(n=t.length);for(var c=0,r=Array(n);c<n;c++)r[c]=t[c];return r}o.exports=a,o.exports.__esModule=!0,o.exports.default=o.exports},85372:function(o){function a(t){if(Array.isArray(t))return t}o.exports=a,o.exports.__esModule=!0,o.exports.default=o.exports},38416:function(o,a,t){var n=t(64062);function c(r,d,f){return(d=n(d))in r?Object.defineProperty(r,d,{value:f,enumerable:!0,configurable:!0,writable:!0}):r[d]=f,r}o.exports=c,o.exports.__esModule=!0,o.exports.default=o.exports},10434:function(o){function a(){return o.exports=a=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var c=arguments[n];for(var r in c)({}).hasOwnProperty.call(c,r)&&(t[r]=c[r])}return t},o.exports.__esModule=!0,o.exports.default=o.exports,a.apply(null,arguments)}o.exports=a,o.exports.__esModule=!0,o.exports.default=o.exports},64836:function(o){function a(t){return t&&t.__esModule?t:{default:t}}o.exports=a,o.exports.__esModule=!0,o.exports.default=o.exports},75263:function(o,a,t){var n=t(18698).default;function c(r,d){if(typeof WeakMap=="function")var f=new WeakMap,m=new WeakMap;return(o.exports=c=function(g,W){if(!W&&g&&g.__esModule)return g;var I,S,C={__proto__:null,default:g};if(g===null||n(g)!="object"&&typeof g!="function")return C;if(I=W?m:f){if(I.has(g))return I.get(g);I.set(g,C)}for(var K in g)K!=="default"&&{}.hasOwnProperty.call(g,K)&&((S=(I=Object.defineProperty)&&Object.getOwnPropertyDescriptor(g,K))&&(S.get||S.set)?I(C,K,S):C[K]=g[K]);return C},o.exports.__esModule=!0,o.exports.default=o.exports)(r,d)}o.exports=c,o.exports.__esModule=!0,o.exports.default=o.exports},68872:function(o){function a(t,n){var c=t==null?null:typeof Symbol!="undefined"&&t[Symbol.iterator]||t["@@iterator"];if(c!=null){var r,d,f,m,y=[],g=!0,W=!1;try{if(f=(c=c.call(t)).next,n===0){if(Object(c)!==c)return;g=!1}else for(;!(g=(r=f.call(c)).done)&&(y.push(r.value),y.length!==n);g=!0);}catch(I){W=!0,d=I}finally{try{if(!g&&c.return!=null&&(m=c.return(),Object(m)!==m))return}finally{if(W)throw d}}return y}}o.exports=a,o.exports.__esModule=!0,o.exports.default=o.exports},12218:function(o){function a(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}o.exports=a,o.exports.__esModule=!0,o.exports.default=o.exports},42122:function(o,a,t){var n=t(38416);function c(d,f){var m=Object.keys(d);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(d);f&&(y=y.filter(function(g){return Object.getOwnPropertyDescriptor(d,g).enumerable})),m.push.apply(m,y)}return m}function r(d){for(var f=1;f<arguments.length;f++){var m=arguments[f]!=null?arguments[f]:{};f%2?c(Object(m),!0).forEach(function(y){n(d,y,m[y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(d,Object.getOwnPropertyDescriptors(m)):c(Object(m)).forEach(function(y){Object.defineProperty(d,y,Object.getOwnPropertyDescriptor(m,y))})}return d}o.exports=r,o.exports.__esModule=!0,o.exports.default=o.exports},70215:function(o,a,t){var n=t(7071);function c(r,d){if(r==null)return{};var f,m,y=n(r,d);if(Object.getOwnPropertySymbols){var g=Object.getOwnPropertySymbols(r);for(m=0;m<g.length;m++)f=g[m],d.indexOf(f)===-1&&{}.propertyIsEnumerable.call(r,f)&&(y[f]=r[f])}return y}o.exports=c,o.exports.__esModule=!0,o.exports.default=o.exports},7071:function(o){function a(t,n){if(t==null)return{};var c={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(n.indexOf(r)!==-1)continue;c[r]=t[r]}return c}o.exports=a,o.exports.__esModule=!0,o.exports.default=o.exports},27424:function(o,a,t){var n=t(85372),c=t(68872),r=t(86116),d=t(12218);function f(m,y){return n(m)||c(m,y)||r(m,y)||d()}o.exports=f,o.exports.__esModule=!0,o.exports.default=o.exports},95036:function(o,a,t){var n=t(18698).default;function c(r,d){if(n(r)!="object"||!r)return r;var f=r[Symbol.toPrimitive];if(f!==void 0){var m=f.call(r,d||"default");if(n(m)!="object")return m;throw new TypeError("@@toPrimitive must return a primitive value.")}return(d==="string"?String:Number)(r)}o.exports=c,o.exports.__esModule=!0,o.exports.default=o.exports},64062:function(o,a,t){var n=t(18698).default,c=t(95036);function r(d){var f=c(d,"string");return n(f)=="symbol"?f:f+""}o.exports=r,o.exports.__esModule=!0,o.exports.default=o.exports},18698:function(o){function a(t){"@babel/helpers - typeof";return o.exports=a=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},o.exports.__esModule=!0,o.exports.default=o.exports,a(t)}o.exports=a,o.exports.__esModule=!0,o.exports.default=o.exports},86116:function(o,a,t){var n=t(73897);function c(r,d){if(r){if(typeof r=="string")return n(r,d);var f={}.toString.call(r).slice(8,-1);return f==="Object"&&r.constructor&&(f=r.constructor.name),f==="Map"||f==="Set"?Array.from(r):f==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(f)?n(r,d):void 0}}o.exports=c,o.exports.__esModule=!0,o.exports.default=o.exports}}]);
