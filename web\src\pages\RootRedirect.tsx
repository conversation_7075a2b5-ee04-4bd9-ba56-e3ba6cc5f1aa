import React, { useEffect } from 'react';
import { history, useModel } from '@umijs/max';
import { Spin } from 'antd';
import { keycloakService } from '@/services/keycloak';


const RootRedirect: React.FC = () => {
  const { initialState } = useModel('@@initialState');

  useEffect(() => {
    const handleRootRedirect = async () => {
      try {
        console.log('=== 根路径重定向处理开始 ===');
        
        // 检查是否已经有用户状态
        if (initialState?.currentUser) {
          console.log('用户已登录，重定向到主页');
          history.replace('/Console/projects');
          return;
        }

        // 尝试初始化Keycloak并检查认证状态
        console.log('检查Keycloak认证状态...');
        try {
          const isAuthenticated = await keycloakService.init();

          if (isAuthenticated) {
            console.log('用户已通过Keycloak认证，重定向到主页');
            history.replace('/Console/projects');
          } else {
            console.log('用户未认证，重定向到登录页');
            history.replace('/user/login');
          }
        } catch (initError) {
          console.error('Keycloak初始化失败:', initError);
          // 初始化失败时重定向到登录页
          history.replace('/user/login');
        }
      } catch (error) {
        console.error('根路径重定向处理失败:', error);
        // 出错时重定向到登录页
        history.replace('/user/login');
      }
    };

    handleRootRedirect();
  }, [initialState]);

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      flexDirection: 'column',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white'
    }}>
      <Spin size="large" style={{ color: 'white' }} />
      <div style={{
        marginTop: 24,
        fontSize: '18px',
        fontWeight: 500,
        textAlign: 'center'
      }}>
        正在检查登录状态...
      </div>
      <div style={{
        marginTop: 8,
        fontSize: '14px',
        opacity: 0.8,
        textAlign: 'center'
      }}>
        请稍候
      </div>
    </div>
  );
};

export default RootRedirect;
